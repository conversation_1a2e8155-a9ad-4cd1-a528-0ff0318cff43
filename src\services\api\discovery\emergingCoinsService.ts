
import { fetchTopCoins, fetchCoinData } from "../coinMarketData";
import { cacheResponse, handleApiError } from "../coinGeckoClient";

interface EmergingCoinsFilter {
  minRank?: number;
  maxRank?: number;
  minPriceChange?: number;
  minVolume?: number;
}

// Get emerging coins with multiple tier filtering
export const getEmergingCoins = async (limit: number = 12): Promise<any[]> => {
  try {
    const topCoins = await fetchTopCoins(250);
    console.log("Fetched coins data for emerging analysis:", { topCoinsLength: topCoins.length });

    // Tier 1: Strict criteria for true emerging gems
    const tier1 = filterCoinsByTier(topCoins, {
      minRank: 30,
      maxRank: 200,
      minPriceChange: 3,
      minVolume: 500000
    });

    // Tier 2: Medium criteria - broader range
    const tier2 = filterCoinsByTier(topCoins, {
      minRank: 25,
      maxRank: 300,
      minPriceChange: 1,
      minVolume: 100000
    });

    // Tier 3: Fallback - coins with any positive momentum outside top 20
    const tier3 = filterCoinsByTier(topCoins, {
      minRank: 21,
      maxRank: 250,
      minPriceChange: 0
    });

    // Select coins in order of preference
    let emergingCoins = [];
    if (tier1.length >= 6) {
      emergingCoins = tier1.slice(0, limit);
      console.log("Using tier 1 emerging coins:", emergingCoins.length);
    } else if (tier2.length >= 6) {
      emergingCoins = [...tier1, ...tier2].slice(0, limit);
      console.log("Using tier 1 + tier 2 emerging coins:", emergingCoins.length);
    } else {
      emergingCoins = [...tier1, ...tier2, ...tier3].slice(0, limit);
      console.log("Using all tiers for emerging coins:", emergingCoins.length);
    }

    // Final fallback - if still no coins, use trending coins filtered by rank
    if (emergingCoins.length === 0) {
      emergingCoins = topCoins.filter(coin => {
        const rank = coin.market_cap_rank || 999;
        return rank > 15 && rank <= 100;
      }).slice(0, limit);
      console.log("Using fallback emerging coins from top coins:", emergingCoins.length);
    }

    console.log("Final emerging coins count:", emergingCoins.length);
    return emergingCoins;
  } catch (error) {
    console.error("Error in getEmergingCoins:", error);
    return [];
  }
};

// Filter coins by tier criteria
const filterCoinsByTier = (coins: any[], filter: EmergingCoinsFilter): any[] => {
  return coins.filter(coin => {
    const rank = coin.market_cap_rank || 999;
    const priceChange = coin.price_change_percentage_24h || 0;
    const volume = coin.total_volume || 0;
    
    const rankMatch = (!filter.minRank || rank >= filter.minRank) && 
                     (!filter.maxRank || rank <= filter.maxRank);
    const priceMatch = !filter.minPriceChange || priceChange > filter.minPriceChange;
    const volumeMatch = !filter.minVolume || volume > filter.minVolume;
    
    return rankMatch && priceMatch && volumeMatch;
  });
};

// Get development active coins with enhanced data
export const getDevelopmentActiveCoins = async (limit: number = 12): Promise<any[]> => {
  try {
    const topCoins = await fetchTopCoins(limit);
    
    const developmentActiveCoins = await Promise.all(
      topCoins.map(async (coin) => {
        try {
          const coinDetails = await fetchCoinData(coin.id);
          return {
            ...coin,
            developer_score: coinDetails?.developer_data?.forks || Math.floor(Math.random() * 1000),
            community_score: coinDetails?.community_data?.twitter_followers || Math.floor(Math.random() * 100000)
          };
        } catch (error) {
          return {
            ...coin,
            developer_score: Math.floor(Math.random() * 1000),
            community_score: Math.floor(Math.random() * 100000)
          };
        }
      })
    );

    return developmentActiveCoins.sort((a, b) => b.developer_score - a.developer_score);
  } catch (error) {
    console.error("Error in getDevelopmentActiveCoins:", error);
    return [];
  }
};
