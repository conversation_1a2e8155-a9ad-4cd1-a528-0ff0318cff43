<svg xmlns="http://www.w3.org/2000/svg" width="800" height="350" viewBox="0 0 800 350" fill="none">
  <defs>
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E293B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0F172A;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:0.3" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="350" fill="#0F172A"/>
  
  <!-- Laptop base -->
  <ellipse cx="400" cy="320" rx="200" ry="20" fill="#374151" opacity="0.5"/>
  
  <!-- Laptop screen -->
  <rect x="150" y="50" width="500" height="280" rx="12" fill="#1F2937" stroke="#4B5563" stroke-width="2"/>
  <rect x="160" y="60" width="480" height="260" rx="8" fill="url(#screenGradient)"/>
  
  <!-- Screen content -->
  <!-- Header bar -->
  <rect x="170" y="70" width="460" height="30" fill="#374151" rx="4"/>
  <circle cx="185" cy="85" r="4" fill="#EF4444"/>
  <circle cx="200" cy="85" r="4" fill="#F59E0B"/>
  <circle cx="215" cy="85" r="4" fill="#10B981"/>
  <text x="400" y="90" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">CryptoVision Pro - Trading Dashboard</text>
  
  <!-- Main chart area -->
  <rect x="180" y="110" width="300" height="180" fill="#0F172A" stroke="#374151" stroke-width="1" rx="4"/>
  
  <!-- Chart grid -->
  <g stroke="#374151" stroke-width="0.5" opacity="0.5">
    <line x1="180" y1="140" x2="480" y2="140"/>
    <line x1="180" y1="170" x2="480" y2="170"/>
    <line x1="180" y1="200" x2="480" y2="200"/>
    <line x1="180" y1="230" x2="480" y2="230"/>
    <line x1="180" y1="260" x2="480" y2="260"/>
    
    <line x1="220" y1="110" x2="220" y2="290"/>
    <line x1="280" y1="110" x2="280" y2="290"/>
    <line x1="340" y1="110" x2="340" y2="290"/>
    <line x1="400" y1="110" x2="400" y2="290"/>
    <line x1="440" y1="110" x2="440" y2="290"/>
  </g>
  
  <!-- Price chart -->
  <path d="M 190 250 Q 220 240 240 220 Q 280 180 320 160 Q 360 140 400 130 Q 440 120 470 110" 
        stroke="#10B981" stroke-width="3" fill="none"/>
  
  <!-- Chart area fill -->
  <path d="M 190 250 Q 220 240 240 220 Q 280 180 320 160 Q 360 140 400 130 Q 440 120 470 110 L 470 290 L 190 290 Z" 
        fill="url(#chartGradient)"/>
  
  <!-- Price indicators -->
  <circle cx="240" cy="220" r="3" fill="#10B981"/>
  <circle cx="320" cy="160" r="3" fill="#10B981"/>
  <circle cx="400" cy="130" r="3" fill="#10B981"/>
  <circle cx="470" cy="110" r="3" fill="#10B981"/>
  
  <!-- Side panel -->
  <rect x="490" y="110" width="140" height="180" fill="#1E293B" stroke="#374151" stroke-width="1" rx="4"/>
  
  <!-- Portfolio section -->
  <text x="560" y="130" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Portfolio</text>
  
  <!-- Asset list -->
  <g transform="translate(500, 140)">
    <rect width="120" height="20" fill="#374151" rx="2"/>
    <circle cx="10" cy="10" r="6" fill="#F7931A"/>
    <text x="10" y="14" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">₿</text>
    <text x="25" y="14" fill="white" font-family="Arial, sans-serif" font-size="10">BTC</text>
    <text x="110" y="14" text-anchor="end" fill="#10B981" font-family="Arial, sans-serif" font-size="10">+5.2%</text>
  </g>
  
  <g transform="translate(500, 165)">
    <rect width="120" height="20" fill="#374151" rx="2"/>
    <circle cx="10" cy="10" r="6" fill="#627EEA"/>
    <text x="10" y="14" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">Ξ</text>
    <text x="25" y="14" fill="white" font-family="Arial, sans-serif" font-size="10">ETH</text>
    <text x="110" y="14" text-anchor="end" fill="#10B981" font-family="Arial, sans-serif" font-size="10">+3.8%</text>
  </g>
  
  <g transform="translate(500, 190)">
    <rect width="120" height="20" fill="#374151" rx="2"/>
    <circle cx="10" cy="10" r="6" fill="#0033AD"/>
    <text x="10" y="14" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">₳</text>
    <text x="25" y="14" fill="white" font-family="Arial, sans-serif" font-size="10">ADA</text>
    <text x="110" y="14" text-anchor="end" fill="#EF4444" font-family="Arial, sans-serif" font-size="10">-1.2%</text>
  </g>
  
  <!-- Trading buttons -->
  <rect x="500" y="220" width="50" height="25" fill="#10B981" rx="4"/>
  <text x="525" y="237" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">BUY</text>
  
  <rect x="570" y="220" width="50" height="25" fill="#EF4444" rx="4"/>
  <text x="595" y="237" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">SELL</text>
  
  <!-- Market stats -->
  <text x="560" y="265" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Market Cap</text>
  <text x="560" y="280" text-anchor="middle" fill="#94A3B8" font-family="Arial, sans-serif" font-size="9">$2.1T</text>
  
  <!-- Laptop keyboard -->
  <rect x="200" y="330" width="400" height="15" rx="8" fill="#374151"/>
  
  <!-- Hands on keyboard -->
  <ellipse cx="300" cy="340" rx="40" ry="15" fill="#D1D5DB" opacity="0.7"/>
  <ellipse cx="500" cy="340" rx="40" ry="15" fill="#D1D5DB" opacity="0.7"/>
</svg>
