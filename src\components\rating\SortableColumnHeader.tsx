
import { ArrowUpDown } from "lucide-react";

interface SortableColumnHeaderProps {
  label: string;
  onClick: () => void;
  isBold?: boolean;
}

export function SortableColumnHeader({ label, onClick, isBold = false }: SortableColumnHeaderProps) {
  return (
    <button 
      onClick={onClick} 
      className={`flex items-center gap-1 hover:text-foreground transition-colors ${isBold ? 'font-bold' : ''}`}
    >
      {label}
      <ArrowUpDown size={14} className="text-muted-foreground/50" />
    </button>
  );
}
