/**
 * Token Volatility & Stability Dashboard Component
 * Risk/return quadrants and volatility analysis with ML predictions
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, TrendingUp, TrendingDown, Shield, AlertTriangle, Target, Info } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import {
  fetchVolatilityAnalysis,
  generateRiskReturnQuadrants,
  getVolatilityInsights,
  VolatilityMetrics,
  RiskReturnQuadrant
} from '@/services/api/volatilityAnalysisService';
import { <PERSON>att<PERSON><PERSON><PERSON>, <PERSON>att<PERSON>, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Cell } from 'recharts';

interface VolatilityDashboardProps {
  className?: string;
}

export default function VolatilityDashboard({ className }: VolatilityDashboardProps) {
  const [viewMode, setViewMode] = useState<'quadrants' | 'rankings' | 'predictions'>('quadrants');
  const [sortBy, setSortBy] = useState<'riskScore' | 'stabilityScore' | 'expectedReturn'>('riskScore');
  const [investorType, setInvestorType] = useState<'all' | 'conservative' | 'balanced' | 'growth' | 'speculative'>('all');

  const { data: volatilityData, isLoading, error, refetch } = useQuery({
    queryKey: ['volatility-analysis'],
    queryFn: fetchVolatilityAnalysis,
    staleTime: 15 * 60 * 1000, // 15 minutes
    retry: 2
  });

  const { data: quadrants } = useQuery({
    queryKey: ['risk-return-quadrants', volatilityData],
    queryFn: generateRiskReturnQuadrants,
    enabled: !!volatilityData,
    staleTime: 30 * 60 * 1000
  });

  const { data: insights } = useQuery({
    queryKey: ['volatility-insights', volatilityData],
    queryFn: () => volatilityData ? getVolatilityInsights(volatilityData) : Promise.resolve(''),
    enabled: !!volatilityData,
    staleTime: 30 * 60 * 1000
  });

  const getRiskColor = (riskScore: number): string => {
    if (riskScore > 75) return '#ef4444'; // High risk - red
    if (riskScore > 50) return '#f59e0b'; // Medium risk - amber
    if (riskScore > 25) return '#3b82f6'; // Low-medium risk - blue
    return '#10b981'; // Low risk - green
  };

  const getInvestorTypeColor = (type: string): string => {
    switch (type) {
      case 'conservative': return '#10b981';
      case 'balanced': return '#3b82f6';
      case 'growth': return '#f59e0b';
      case 'speculative': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const filterByInvestorType = (data: VolatilityMetrics[]) => {
    if (investorType === 'all') return data;
    return data.filter(item => item.investorType === investorType);
  };

  const renderQuadrantsView = () => {
    if (!quadrants || !volatilityData) return null;

    const scatterData = volatilityData.map(token => ({
      x: token.riskScore,
      y: token.expectedReturn,
      name: token.symbol,
      marketCap: token.marketCap,
      color: getRiskColor(token.riskScore),
      size: Math.log(token.marketCap) / 2
    }));

    return (
      <div className="space-y-6">
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart data={scatterData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                type="number" 
                dataKey="x" 
                name="Risk Score"
                domain={[0, 100]}
                label={{ value: 'Risk Score →', position: 'insideBottom', offset: -10 }}
              />
              <YAxis 
                type="number" 
                dataKey="y" 
                name="Expected Return"
                label={{ value: '← Expected Return (%)', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip 
                cursor={{ strokeDasharray: '3 3' }}
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    const data = payload[0].payload;
                    return (
                      <div className="bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg">
                        <p className="font-semibold">{data.name}</p>
                        <p className="text-sm">Risk Score: {data.x.toFixed(1)}</p>
                        <p className="text-sm">Expected Return: {data.y.toFixed(1)}%</p>
                        <p className="text-sm">Market Cap: ${(data.marketCap / 1e9).toFixed(1)}B</p>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Scatter dataKey="y" fill="#3b82f6">
                {scatterData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Scatter>
            </ScatterChart>
          </ResponsiveContainer>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {quadrants.map(quadrant => (
            <Card key={quadrant.quadrant} className="border-2">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  {quadrant.quadrant.includes('low-risk') ? 
                    <Shield className="h-5 w-5 text-green-600" /> : 
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                  }
                  {quadrant.quadrant.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </CardTitle>
                <p className="text-sm text-muted-foreground">{quadrant.description}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 mb-3">
                  <p className="text-sm font-medium text-blue-600">{quadrant.recommendation}</p>
                </div>
                
                <div className="space-y-1">
                  <div className="text-sm font-medium">Top Assets ({quadrant.tokens.length}):</div>
                  <div className="flex flex-wrap gap-1">
                    {quadrant.tokens.slice(0, 8).map(token => (
                      <Badge 
                        key={token.symbol} 
                        variant="outline"
                        style={{ borderColor: getRiskColor(token.riskScore) }}
                      >
                        {token.symbol}
                      </Badge>
                    ))}
                    {quadrant.tokens.length > 8 && (
                      <Badge variant="outline">+{quadrant.tokens.length - 8} more</Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderRankingsView = () => {
    if (!volatilityData) return null;

    const filteredData = filterByInvestorType(volatilityData);
    const sortedData = [...filteredData].sort((a, b) => {
      switch (sortBy) {
        case 'riskScore': return a.riskScore - b.riskScore;
        case 'stabilityScore': return b.stabilityScore - a.stabilityScore;
        case 'expectedReturn': return b.expectedReturn - a.expectedReturn;
        default: return 0;
      }
    });

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="riskScore">Lowest Risk First</SelectItem>
              <SelectItem value="stabilityScore">Most Stable First</SelectItem>
              <SelectItem value="expectedReturn">Highest Return First</SelectItem>
            </SelectContent>
          </Select>

          <Select value={investorType} onValueChange={(value) => setInvestorType(value as any)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="conservative">Conservative</SelectItem>
              <SelectItem value="balanced">Balanced</SelectItem>
              <SelectItem value="growth">Growth</SelectItem>
              <SelectItem value="speculative">Speculative</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2 max-h-96 overflow-y-auto">
          {sortedData.slice(0, 20).map((token, index) => (
            <Card key={token.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="text-lg font-bold text-muted-foreground">
                    #{index + 1}
                  </div>
                  <div>
                    <div className="font-semibold">{token.symbol}</div>
                    <div className="text-sm text-muted-foreground">{token.name}</div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">Risk Score</div>
                    <div className="font-semibold" style={{ color: getRiskColor(token.riskScore) }}>
                      {token.riskScore.toFixed(1)}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">Stability</div>
                    <div className="font-semibold text-green-600">
                      {token.stabilityScore.toFixed(1)}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">Expected Return</div>
                    <div className={`font-semibold ${token.expectedReturn > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {token.expectedReturn > 0 ? '+' : ''}{token.expectedReturn.toFixed(1)}%
                    </div>
                  </div>

                  <Badge 
                    style={{ backgroundColor: getInvestorTypeColor(token.investorType) }}
                    className="text-white"
                  >
                    {token.investorType}
                  </Badge>
                </div>
              </div>

              <div className="mt-3 grid grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">7d Vol:</span>
                  <span className="ml-1 font-medium">{token.volatility7d.toFixed(1)}%</span>
                </div>
                <div>
                  <span className="text-muted-foreground">30d Vol:</span>
                  <span className="ml-1 font-medium">{token.volatility30d.toFixed(1)}%</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Predicted:</span>
                  <span className="ml-1 font-medium">{token.predictedVolatility.toFixed(1)}%</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Trend:</span>
                  <span className={`ml-1 font-medium ${
                    token.volatilityTrend === 'increasing' ? 'text-red-600' :
                    token.volatilityTrend === 'decreasing' ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    {token.volatilityTrend === 'increasing' ? '↗' : 
                     token.volatilityTrend === 'decreasing' ? '↘' : '→'} {token.volatilityTrend}
                  </span>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderPredictionsView = () => {
    if (!volatilityData) return null;

    const predictionData = volatilityData.slice(0, 15).map(token => ({
      name: token.symbol,
      current: token.volatility7d,
      predicted: token.predictedVolatility,
      difference: token.predictedVolatility - token.volatility7d
    }));

    return (
      <div className="space-y-6">
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={predictionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis label={{ value: 'Volatility %', angle: -90, position: 'insideLeft' }} />
              <Tooltip />
              <Bar dataKey="current" fill="#3b82f6" name="Current Volatility" />
              <Bar dataKey="predicted" fill="#10b981" name="Predicted Volatility" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-red-600" />
                Increasing Volatility
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {volatilityData
                  .filter(token => token.volatilityTrend === 'increasing')
                  .slice(0, 5)
                  .map(token => (
                    <div key={token.id} className="flex justify-between items-center">
                      <span className="font-medium">{token.symbol}</span>
                      <Badge variant="destructive">
                        +{(token.predictedVolatility - token.volatility7d).toFixed(1)}%
                      </Badge>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingDown className="h-5 w-5 text-green-600" />
                Decreasing Volatility
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {volatilityData
                  .filter(token => token.volatilityTrend === 'decreasing')
                  .slice(0, 5)
                  .map(token => (
                    <div key={token.id} className="flex justify-between items-center">
                      <span className="font-medium">{token.symbol}</span>
                      <Badge variant="default">
                        {(token.predictedVolatility - token.volatility7d).toFixed(1)}%
                      </Badge>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Target className="h-5 w-5 text-blue-600" />
                Stable Outlook
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {volatilityData
                  .filter(token => token.volatilityTrend === 'stable')
                  .slice(0, 5)
                  .map(token => (
                    <div key={token.id} className="flex justify-between items-center">
                      <span className="font-medium">{token.symbol}</span>
                      <Badge variant="secondary">
                        {Math.abs(token.predictedVolatility - token.volatility7d).toFixed(1)}%
                      </Badge>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-muted-foreground">Analyzing volatility patterns...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-muted-foreground mb-4">Failed to load volatility data</p>
            <Button onClick={() => refetch()} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-primary" />
            <CardTitle>Volatility & Risk Dashboard</CardTitle>
          </div>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            Refresh
          </Button>
        </div>
        
        {insights && (
          <div className="bg-green-50 dark:bg-green-950 p-3 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-green-800 dark:text-green-200">{insights}</p>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="quadrants">Risk/Return</TabsTrigger>
            <TabsTrigger value="rankings">Rankings</TabsTrigger>
            <TabsTrigger value="predictions">Predictions</TabsTrigger>
          </TabsList>

          <TabsContent value="quadrants" className="mt-4">
            {renderQuadrantsView()}
          </TabsContent>

          <TabsContent value="rankings" className="mt-4">
            {renderRankingsView()}
          </TabsContent>

          <TabsContent value="predictions" className="mt-4">
            {renderPredictionsView()}
          </TabsContent>
        </Tabs>

        {volatilityData && (
          <div className="mt-6 grid grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">
                {volatilityData.filter(t => t.category === 'conservative').length}
              </div>
              <div className="text-sm text-muted-foreground">Conservative</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {volatilityData.filter(t => t.category === 'moderate').length}
              </div>
              <div className="text-sm text-muted-foreground">Moderate</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-amber-600">
                {volatilityData.filter(t => t.category === 'aggressive').length}
              </div>
              <div className="text-sm text-muted-foreground">Aggressive</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">
                {volatilityData.filter(t => t.category === 'speculative').length}
              </div>
              <div className="text-sm text-muted-foreground">Speculative</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
