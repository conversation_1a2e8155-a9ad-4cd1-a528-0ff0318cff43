
import { Exchange, FeeCalculationResult, DecentralizedExchange } from "./types";

/**
 * Calculates all associated fees for a cryptocurrency purchase
 * 
 * @param selectedExchange - The exchange where the transaction will occur
 * @param investmentAmount - The amount in USD to invest
 * @param slippagePercentage - Expected slippage percentage (only applies to DEX)
 * @returns FeeCalculationResult object with detailed breakdown of all costs
 */
export const calculateFees = (
  selectedExchange: Exchange,
  investmentAmount: number,
  slippagePercentage: number
): FeeCalculationResult => {
  // Ensure investment amount is a number
  const investmentAmountNum = Number(investmentAmount);
  
  if (isNaN(investmentAmountNum) || investmentAmountNum <= 0) {
    return {
      tradingFee: 0,
      slippageCost: 0,
      gasFee: 0,
      withdrawalFee: 0,
      totalCost: 0,
      receivedCrypto: 0
    };
  }
  
  // Calculate trading fee (percentage of investment amount)
  const tradingFee = investmentAmountNum * (selectedExchange.tradingFee / 100);
  
  // Calculate slippage cost (only for DEX exchanges)
  const slippageCost = selectedExchange.type === 'DEX' 
    ? investmentAmountNum * (slippagePercentage / 100) 
    : 0;
  
  // Calculate gas fee (only for DEX exchanges)
  const gasFee = selectedExchange.type === 'DEX' 
    ? (selectedExchange as DecentralizedExchange).gasEstimate 
    : 0;
  
  // Get withdrawal fee (fixed amount)
  const withdrawalFee = selectedExchange.withdrawalFee;
  
  // Calculate total cost and final received amount
  const totalCost = tradingFee + slippageCost + gasFee + withdrawalFee;
  const receivedCrypto = investmentAmountNum - totalCost;
  
  return {
    tradingFee,
    slippageCost,
    gasFee,
    withdrawalFee,
    totalCost,
    receivedCrypto
  };
};
