
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { NetworkHealthData } from "@/hooks/useOnChainAnalytics";
import { TrendingUp, TrendingDown, Activity, Database } from "lucide-react";
import { Progress } from "@/components/ui/progress";

interface NetworkHealthSectionProps {
  data: NetworkHealthData;
  isLoading: boolean;
}

export default function NetworkHealthSection({ data, isLoading }: NetworkHealthSectionProps) {
  const formatNumber = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(2)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };
  
  const formatChange = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">Active Nodes</CardTitle>
              <CardDescription>Network validators</CardDescription>
            </div>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.nodes.total)}</div>
            <div className={`text-xs ${data.nodes.change >= 0 ? 'text-green-500' : 'text-red-500'} flex items-center mt-1`}>
              {data.nodes.change >= 0 ? <TrendingUp className="mr-1 h-3 w-3" /> : <TrendingDown className="mr-1 h-3 w-3" />}
              {formatChange(data.nodes.change)} from last week
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">Staking</CardTitle>
              <CardDescription>Total staked amount</CardDescription>
            </div>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.staking.total)}</div>
            <div className="flex items-center justify-between mt-2 mb-1">
              <span className="text-xs text-muted-foreground">Staked Percentage</span>
              <span className="text-xs font-medium">{data.staking.percentage}%</span>
            </div>
            <Progress value={data.staking.percentage} className="h-1" />
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">Network Hashrate</CardTitle>
              <CardDescription>Mining power</CardDescription>
            </div>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.hashrate.value} {data.hashrate.unit}</div>
            <div className={`text-xs ${data.hashrate.change >= 0 ? 'text-green-500' : 'text-red-500'} flex items-center mt-1`}>
              {data.hashrate.change >= 0 ? <TrendingUp className="mr-1 h-3 w-3" /> : <TrendingDown className="mr-1 h-3 w-3" />}
              {formatChange(data.hashrate.change)} from last week
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">Difficulty</CardTitle>
              <CardDescription>Mining difficulty</CardDescription>
            </div>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.difficulty.value.toFixed(2)} T</div>
            <div className={`text-xs ${data.difficulty.change >= 0 ? 'text-green-500' : 'text-red-500'} flex items-center mt-1`}>
              {data.difficulty.change >= 0 ? <TrendingUp className="mr-1 h-3 w-3" /> : <TrendingDown className="mr-1 h-3 w-3" />}
              {formatChange(data.difficulty.change)} from last adjustment
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Network Health Metrics</CardTitle>
            <CardDescription>Key indicators of blockchain performance and health</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-[200px] flex items-center justify-center">
                <p className="text-muted-foreground">Loading data...</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                {data.metrics.map((metric) => (
                  <Card key={metric.name}>
                    <CardContent className="pt-6">
                      <div className="text-sm font-medium text-center mb-2">{metric.name}</div>
                      <div className="text-2xl font-bold text-center">{formatNumber(metric.value)}</div>
                      <div className={`text-xs ${metric.change >= 0 ? 'text-green-500' : 'text-red-500'} flex items-center justify-center mt-2`}>
                        {metric.change >= 0 ? <TrendingUp className="mr-1 h-3 w-3" /> : <TrendingDown className="mr-1 h-3 w-3" />}
                        {formatChange(metric.change)}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
