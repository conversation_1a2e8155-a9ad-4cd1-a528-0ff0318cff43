
import { Responsive<PERSON><PERSON><PERSON>, AreaChart, Area, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from "recharts";

const mockDepthData = [
  { price: 67000, bids: 12.5, asks: 8.2 },
  { price: 67100, bids: 18.3, asks: 15.7 },
  { price: 67200, bids: 25.1, asks: 22.4 },
  { price: 67300, bids: 32.8, asks: 28.9 },
  { price: 67400, bids: 41.2, asks: 35.6 },
  { price: 67500, bids: 48.9, asks: 42.1 },
];

export default function MarketDepthChart() {
  return (
    <div className="h-48">
      <div className="flex justify-between items-center mb-4">
        <h4 className="text-white font-semibold text-sm">BTC/USD Depth</h4>
        <div className="flex space-x-4 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-gray-400">Bids</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span className="text-gray-400">Asks</span>
          </div>
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={mockDepthData}>
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
          <XAxis 
            dataKey="price" 
            axisLine={false}
            tickLine={false}
            tick={{ fill: '#9CA3AF', fontSize: 10 }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fill: '#9CA3AF', fontSize: 10 }}
          />
          <Area 
            type="monotone" 
            dataKey="bids" 
            stackId="1"
            stroke="#10B981" 
            fill="#10B981" 
            fillOpacity={0.3}
          />
          <Area 
            type="monotone" 
            dataKey="asks" 
            stackId="2"
            stroke="#EF4444" 
            fill="#EF4444" 
            fillOpacity={0.3}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}
