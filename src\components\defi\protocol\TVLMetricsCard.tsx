
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Database } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";

interface TVLMetricsCardProps {
  protocol: any | null;
  isLoading: boolean;
  formatCurrency: (value: number) => string;
}

const TVLMetricsCard = ({ protocol, isLoading, formatCurrency }: TVLMetricsCardProps) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center gap-1">
            <Database className="h-4 w-4" /> TVL & Growth
          </CardTitle>
          {!isLoading && protocol && (
            <Badge variant={protocol.tvlChange24h >= 0 ? "default" : "destructive"}>
              {protocol.tvlChange24h > 0 ? "+" : ""}
              {protocol.tvlChange24h.toFixed(2)}%
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading || !protocol ? (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        ) : (
          <div className="space-y-3">
            <div>
              <p className="text-sm text-muted-foreground">Total Value Locked</p>
              <p className="text-3xl font-bold">{formatCurrency(protocol.tvl)}</p>
            </div>
            
            <div className="h-[180px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={protocol.tvlTrend}
                  margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.15} />
                  <XAxis 
                    dataKey="date"
                    tickFormatter={(date) => new Date(date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                    tick={{ fontSize: 10 }}
                    dy={10}
                  />
                  <YAxis 
                    tickFormatter={(value) => formatCurrency(value).replace('$', '')}
                    tick={{ fontSize: 10 }}
                    width={50}
                  />
                  <Tooltip 
                    formatter={(value: number) => [`${formatCurrency(value)}`, "TVL"]}
                    labelFormatter={(label) => new Date(label).toLocaleDateString()}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="tvl" 
                    stroke="#8884d8" 
                    fill="#8884d8"
                    fillOpacity={0.2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TVLMetricsCard;
