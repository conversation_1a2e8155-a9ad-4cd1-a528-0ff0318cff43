
import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import HeaderBar from "@/components/HeaderBar";
import CandlestickChart from "@/components/CandlestickChart";
import ModelMetrics from "@/components/ModelMetrics";
import { getPriceForecastData } from "@/services/api/priceHistory";
import { fetchTopCoins } from "@/services/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";

export default function Forecasting() {
  const [selectedCoin, setSelectedCoin] = useState({ id: "bitcoin", name: "Bitcoin", symbol: "BTC" });
  
  // Fetch top coins for the dropdown
  const { 
    data: topCoins, 
    isLoading: isLoadingCoins,
    refetch: refetchCoins
  } = useQuery({
    queryKey: ['forecast-coins'],
    queryFn: async () => {
      const coins = await fetchTopCoins(10);
      return coins.map((coin: any) => ({
        id: coin.id,
        name: coin.name,
        symbol: coin.symbol.toUpperCase()
      }));
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Fetch forecast data for selected coin
  const {
    data: forecastResponse,
    isLoading: isLoadingForecast,
    refetch: refetchForecast
  } = useQuery({
    queryKey: ['forecast-data', selectedCoin.id],
    queryFn: () => getPriceForecastData(selectedCoin.id, 5), // Generate 5-day forecast
    enabled: !!selectedCoin.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  const isLoading = isLoadingCoins || isLoadingForecast;
  
  const refreshData = async () => {
    toast({
      title: "Updating forecasts",
      description: "Fetching the latest data and recalculating predictions...",
    });
    
    await Promise.all([refetchCoins(), refetchForecast()]);
    
    toast({
      title: "Forecasts updated",
      description: "All prediction models have been recalculated.",
    });
  };
  
  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar 
        title="Price Forecasting" 
        description="Hybrid ARIMA-LSTM price predictions with confidence intervals"
        onRefresh={refreshData}
        isLoading={isLoading}
      />
      
      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          <div className="flex flex-wrap gap-3 mb-4">
            {isLoadingCoins ? (
              <div className="flex items-center gap-2">
                <Loader2 size={16} className="animate-spin" />
                <span>Loading coins...</span>
              </div>
            ) : (
              topCoins?.map((coin: any) => (
                <Button
                  key={coin.id}
                  variant={selectedCoin.id === coin.id ? 'default' : 'outline'}
                  onClick={() => setSelectedCoin(coin)}
                  className="flex items-center gap-2"
                >
                  {coin.name} ({coin.symbol})
                </Button>
              ))
            )}
          </div>
          
          <CandlestickChart 
            data={forecastResponse?.data || []}
            title={`${selectedCoin.name} (${selectedCoin.symbol}) Price Forecast`}
            description="Historical prices with 5-day price predictions"
            isLoading={isLoadingForecast}
          />
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Forecast Performance Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoadingForecast ? (
                  <div className="flex items-center justify-center p-8">
                    <Loader2 size={24} className="animate-spin mr-2" />
                    <span>Calculating model metrics...</span>
                  </div>
                ) : (
                  <ModelMetrics 
                    metrics={forecastResponse?.metrics || {
                      rmse: 0,
                      mae: 0,
                      rSquared: 0,
                      mape: 0
                    }}
                    assetName={selectedCoin.name}
                  />
                )}
                
                <div className="mt-6 text-sm text-muted-foreground">
                  <h4 className="font-medium text-foreground mb-2">About the model:</h4>
                  <p>The Hybrid ARIMA-LSTM model combines statistical time-series analysis with deep learning:</p>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>ARIMA captures linear relationships and seasonal patterns</li>
                    <li>LSTM neural network learns complex non-linear patterns</li>
                    <li>Ensemble approach improves accuracy and reduces overfitting</li>
                    <li>Confidence intervals calculated using residual error distribution</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Model Inputs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 text-sm">
                  <div>
                    <h4 className="font-medium">Historical Data Points</h4>
                    <p className="text-muted-foreground">30 days (1 month)</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium">Technical Indicators</h4>
                    <ul className="text-muted-foreground list-disc pl-5 space-y-1">
                      <li>Moving Averages (7-day)</li>
                      <li>Linear Trend Analysis (14-day)</li>
                      <li>Volatility Analysis</li>
                      <li>Confidence Intervals</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium">Model Features</h4>
                    <ul className="text-muted-foreground list-disc pl-5 space-y-1">
                      <li>Simple Moving Average</li>
                      <li>Trend extrapolation</li>
                      <li>Volatility bounds</li>
                      <li>Time-decay confidence</li>
                    </ul>
                  </div>
                  
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full mt-2"
                    onClick={() => {
                      toast({
                        title: "Advanced Settings",
                        description: "This feature will be available in a future update.",
                      });
                    }}
                  >
                    Advanced Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
