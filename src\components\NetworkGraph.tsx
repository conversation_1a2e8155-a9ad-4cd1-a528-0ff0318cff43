
import { useEffect, useRef, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';

interface Node {
  id: string;
  name: string;
  group: string;
  value: number;
}

interface Link {
  source: string;
  target: string;
  value: number;
  type: string;
}

interface NetworkGraphProps {
  title: string;
  description?: string;
  nodes: Node[];
  links: Link[];
  isLoading?: boolean;
  height?: number;
  layoutOptions?: {
    enableSimulation?: boolean;
    chargeStrength?: number;
    linkDistance?: number;
  };
}

// Simple D3-like force simulation implementation
const createForceSimulation = (
  nodes: Node[], 
  links: Link[], 
  width: number, 
  height: number,
  chargeStrength = -30,
  linkDistance = 100
) => {
  // Initialize node positions randomly
  const nodePositions = new Map<string, {x: number, y: number, vx: number, vy: number}>();
  
  nodes.forEach(node => {
    nodePositions.set(node.id, {
      x: Math.random() * width,
      y: Math.random() * height,
      vx: 0,
      vy: 0
    });
  });
  
  // Convert links to use indices
  const nodeMap = new Map<string, number>();
  nodes.forEach((node, index) => {
    nodeMap.set(node.id, index);
  });
  
  const indexedLinks = links.map(link => ({
    source: nodeMap.get(link.source) ?? 0,
    target: nodeMap.get(link.target) ?? 0,
    value: link.value,
    type: link.type
  }));

  // Run simulation iterations
  const iterations = 300;
  for (let i = 0; i < iterations; i++) {
    // Apply forces
    // 1. Repulsive force between nodes (charge)
    for (let a = 0; a < nodes.length; a++) {
      for (let b = a + 1; b < nodes.length; b++) {
        const nodeA = nodes[a];
        const nodeB = nodes[b];
        const posA = nodePositions.get(nodeA.id)!;
        const posB = nodePositions.get(nodeB.id)!;
        
        const dx = posB.x - posA.x;
        const dy = posB.y - posA.y;
        const distance = Math.sqrt(dx * dx + dy * dy) || 1;
        
        // Avoid division by zero
        if (distance === 0) continue;
        
        const force = chargeStrength / (distance * distance);
        const fx = dx / distance * force;
        const fy = dy / distance * force;
        
        posA.vx -= fx;
        posA.vy -= fy;
        posB.vx += fx;
        posB.vy += fy;
      }
    }
    
    // 2. Attractive force along links
    indexedLinks.forEach(link => {
      const source = nodes[link.source];
      const target = nodes[link.target];
      const posSource = nodePositions.get(source.id)!;
      const posTarget = nodePositions.get(target.id)!;
      
      const dx = posTarget.x - posSource.x;
      const dy = posTarget.y - posSource.y;
      const distance = Math.sqrt(dx * dx + dy * dy) || 1;
      
      if (distance === 0) return;
      
      const force = (distance - linkDistance) / (distance * 10);
      const fx = dx * force;
      const fy = dy * force;
      
      posSource.vx += fx;
      posSource.vy += fy;
      posTarget.vx -= fx;
      posTarget.vy -= fy;
    });
    
    // Update positions
    nodes.forEach(node => {
      const pos = nodePositions.get(node.id)!;
      
      // Apply velocity with damping
      pos.x += pos.vx * 0.1;
      pos.y += pos.vy * 0.1;
      
      // Damping
      pos.vx *= 0.9;
      pos.vy *= 0.9;
      
      // Boundary constraints
      pos.x = Math.max(10, Math.min(width - 10, pos.x));
      pos.y = Math.max(10, Math.min(height - 10, pos.y));
    });
  }
  
  return nodePositions;
};

// Calculate node radius based on value
const calculateNodeRadius = (value: number, minValue: number, maxValue: number) => {
  const minRadius = 5;
  const maxRadius = 20;
  const normalizedValue = (value - minValue) / (maxValue - minValue) || 0;
  return minRadius + normalizedValue * (maxRadius - minRadius);
};

// Color mapping for node groups
const groupColors: Record<string, string> = {
  'exchange': '#10b981', // green
  'defi': '#3b82f6', // blue
  'chain': '#8b5cf6', // purple
  'token': '#f59e0b', // amber
  'oracle': '#ef4444', // red
  'bridge': '#64748b', // slate
  default: '#6b7280'  // gray
};

const linkColors: Record<string, string> = {
  'bridge': '#f97316', // orange
  'supply': '#8b5cf6', // purple
  'swap': '#06b6d4', // cyan
  'stake': '#10b981', // green
  'liquidity': '#3b82f6', // blue
  default: '#9ca3af' // gray
};

export default function NetworkGraph({
  title,
  description,
  nodes,
  links,
  isLoading = false,
  height = 500,
  layoutOptions = {
    enableSimulation: true,
    chargeStrength: -120,
    linkDistance: 120
  }
}: NetworkGraphProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedGroup, setSelectedGroup] = useState<string>("all");
  const [hoveredNode, setHoveredNode] = useState<Node | null>(null);
  const [groups, setGroups] = useState<string[]>([]);
  const [tooltipPos, setTooltipPos] = useState({ x: 0, y: 0 });
  const [isCanvasReady, setCanvasReady] = useState(false);
  
  // Filter nodes and links based on selected group
  const filteredNodes = selectedGroup === "all" 
    ? nodes 
    : nodes.filter(node => node.group === selectedGroup);
  
  const filteredLinks = selectedGroup === "all"
    ? links
    : links.filter(link => 
        nodes.find(n => n.id === link.source)?.group === selectedGroup || 
        nodes.find(n => n.id === link.target)?.group === selectedGroup
      );
  
  // Extract unique groups from nodes
  useEffect(() => {
    const uniqueGroups = Array.from(new Set(nodes.map(node => node.group)));
    setGroups(uniqueGroups);
  }, [nodes]);
  
  // Handle canvas rendering
  useEffect(() => {
    if (isLoading || !canvasRef.current || filteredNodes.length === 0 || !containerRef.current) {
      return;
    }
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions based on container
    const container = containerRef.current;
    const rect = container.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = height;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Calculate node positions using force simulation
    const nodePositions = createForceSimulation(
      filteredNodes, 
      filteredLinks, 
      canvas.width, 
      canvas.height,
      layoutOptions.chargeStrength,
      layoutOptions.linkDistance
    );

    // Find min/max values for node sizing
    const nodeValues = filteredNodes.map(node => node.value);
    const minValue = Math.min(...nodeValues);
    const maxValue = Math.max(...nodeValues);
    
    // Draw links first (so they're under nodes)
    ctx.lineWidth = 1;
    filteredLinks.forEach(link => {
      const sourceNode = filteredNodes.find(n => n.id === link.source);
      const targetNode = filteredNodes.find(n => n.id === link.target);
      
      if (!sourceNode || !targetNode) return;
      
      const sourcePos = nodePositions.get(sourceNode.id);
      const targetPos = nodePositions.get(targetNode.id);
      
      if (!sourcePos || !targetPos) return;
      
      // Draw link
      const linkColor = linkColors[link.type] || linkColors.default;
      ctx.beginPath();
      ctx.moveTo(sourcePos.x, sourcePos.y);
      ctx.lineTo(targetPos.x, targetPos.y);
      ctx.strokeStyle = linkColor;
      ctx.globalAlpha = 0.6;
      ctx.stroke();
      
      // Draw link value if significant
      if (link.value > 0.5 * (maxValue - minValue) / 2) {
        const midX = (sourcePos.x + targetPos.x) / 2;
        const midY = (sourcePos.y + targetPos.y) / 2;
        
        ctx.fillStyle = linkColor;
        ctx.globalAlpha = 0.9;
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(link.value.toString(), midX, midY);
      }
    });
    
    // Draw nodes
    filteredNodes.forEach(node => {
      const pos = nodePositions.get(node.id);
      if (!pos) return;
      
      const radius = calculateNodeRadius(node.value, minValue, maxValue);
      const color = groupColors[node.group] || groupColors.default;
      
      // Draw node circle
      ctx.beginPath();
      ctx.arc(pos.x, pos.y, radius, 0, Math.PI * 2);
      ctx.fillStyle = color;
      ctx.globalAlpha = 0.8;
      ctx.fill();
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 0.5;
      ctx.stroke();
      
      // Draw node label
      ctx.fillStyle = '#ffffff';
      ctx.globalAlpha = 1;
      ctx.font = '10px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      // Truncate long names
      const displayName = node.name.length > 10
        ? node.name.substring(0, 8) + '...'
        : node.name;
      
      ctx.fillText(displayName, pos.x, pos.y);
    });

    setCanvasReady(true);

    // Add event listeners for node hover
    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      // Check if mouse is over any node
      let hovered = null;
      
      for (const node of filteredNodes) {
        const pos = nodePositions.get(node.id);
        if (!pos) continue;
        
        const radius = calculateNodeRadius(node.value, minValue, maxValue);
        const distance = Math.sqrt(Math.pow(x - pos.x, 2) + Math.pow(y - pos.y, 2));
        
        if (distance <= radius) {
          hovered = node;
          setTooltipPos({ x: e.clientX, y: e.clientY });
          break;
        }
      }
      
      setHoveredNode(hovered);
    };
    
    canvas.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      canvas.removeEventListener('mousemove', handleMouseMove);
    };
  }, [filteredNodes, filteredLinks, isLoading, height, layoutOptions, selectedGroup]);

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          
          <Select value={selectedGroup} onValueChange={setSelectedGroup}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All groups</SelectItem>
              {groups.map(group => (
                <SelectItem key={group} value={group}>
                  {group.charAt(0).toUpperCase() + group.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <Skeleton className="w-full h-[500px]" />
        ) : (
          <div className="relative" ref={containerRef}>
            <canvas 
              ref={canvasRef} 
              className="w-full h-full"
              style={{ height }}
            />
            {hoveredNode && isCanvasReady && (
              <div 
                className="absolute z-10 bg-card border border-border p-3 rounded-md shadow-md"
                style={{
                  left: `${tooltipPos.x + 10}px`,
                  top: `${tooltipPos.y - 20}px`,
                  transform: 'translate(-50%, -100%)'
                }}
              >
                <div className="text-sm font-medium">{hoveredNode.name}</div>
                <div className="text-xs text-muted-foreground">
                  Type: {hoveredNode.group.charAt(0).toUpperCase() + hoveredNode.group.slice(1)}
                </div>
                <div className="text-xs text-muted-foreground">
                  Value: {hoveredNode.value.toLocaleString()}
                </div>
                
                {/* Show connections */}
                <div className="text-xs mt-1 border-t border-border pt-1">
                  {filteredLinks.filter(link => 
                    link.source === hoveredNode.id || link.target === hoveredNode.id
                  ).length} connections
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Legend */}
        <div className="mt-4 flex flex-wrap gap-3 text-xs">
          <div className="font-medium">Legend:</div>
          {Object.entries(groupColors).map(([group, color]) => (
            <div key={group} className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: color }}></div>
              <span>{group.charAt(0).toUpperCase() + group.slice(1)}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
