
import { Slider } from "@/components/ui/slider";
import type { FactorWeight } from "@/types/rating";

interface FactorSliderProps {
  factor: FactorWeight;
  onWeightChange: (weight: number) => void;
}

export function FactorSlider({ factor, onWeightChange }: FactorSliderProps) {
  return (
    <div key={factor.id} className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">{factor.name}</span>
        <span className="text-sm font-bold bg-secondary px-2 py-0.5 rounded">
          {factor.weight}
        </span>
      </div>
      <Slider
        defaultValue={[factor.weight]}
        max={10}
        min={1}
        step={1}
        onValueChange={(value) => onWeightChange(value[0])}
      />
      <p className="text-xs text-muted-foreground">{factor.description}</p>
    </div>
  );
}
