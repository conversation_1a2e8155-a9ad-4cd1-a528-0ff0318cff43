<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400" fill="none">
  <defs>
    <linearGradient id="buyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="sellGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EF4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="#0F172A"/>
  
  <!-- Title -->
  <text x="400" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="22" font-weight="bold">Cryptocurrency Order Types</text>
  <text x="400" y="50" text-anchor="middle" fill="#94A3B8" font-family="Arial, sans-serif" font-size="14">Understanding different ways to buy and sell crypto</text>
  
  <!-- Price Chart Background -->
  <g transform="translate(50, 80)">
    <rect width="300" height="200" fill="#1E293B" stroke="#334155" stroke-width="2" rx="8"/>
    <text x="150" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">BTC/USD Price Chart</text>
    
    <!-- Grid -->
    <g stroke="#374151" stroke-width="0.5" opacity="0.5">
      <line x1="20" y1="50" x2="280" y2="50"/>
      <line x1="20" y1="100" x2="280" y2="100"/>
      <line x1="20" y1="150" x2="280" y2="150"/>
      <line x1="20" y1="180" x2="280" y2="180"/>
      
      <line x1="70" y1="30" x2="70" y2="190"/>
      <line x1="120" y1="30" x2="120" y2="190"/>
      <line x1="170" y1="30" x2="170" y2="190"/>
      <line x1="220" y1="30" x2="220" y2="190"/>
    </g>
    
    <!-- Price line -->
    <path d="M 30 160 Q 70 140 120 120 Q 170 100 220 90 Q 250 85 270 80" 
          stroke="#F59E0B" stroke-width="3" fill="none"/>
    
    <!-- Current price indicator -->
    <circle cx="270" cy="80" r="4" fill="#F59E0B"/>
    <text x="275" y="85" fill="#F59E0B" font-family="Arial, sans-serif" font-size="10" font-weight="bold">$45,000</text>
    
    <!-- Order levels -->
    <!-- Limit Buy Order -->
    <line x1="20" y1="140" x2="280" y2="140" stroke="#10B981" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="25" y="135" fill="#10B981" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Limit Buy: $42,000</text>
    
    <!-- Limit Sell Order -->
    <line x1="20" y1="60" x2="280" y2="60" stroke="#EF4444" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="25" y="55" fill="#EF4444" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Limit Sell: $48,000</text>
    
    <!-- Stop Loss -->
    <line x1="20" y1="170" x2="280" y2="170" stroke="#F59E0B" stroke-width="2" stroke-dasharray="3,3"/>
    <text x="25" y="185" fill="#F59E0B" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Stop Loss: $40,000</text>
  </g>
  
  <!-- Order Types Explanation -->
  <g transform="translate(400, 80)">
    <rect width="350" height="280" fill="#1E293B" stroke="#334155" stroke-width="2" rx="8"/>
    <text x="175" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Order Types Explained</text>
    
    <!-- Market Order -->
    <g transform="translate(20, 40)">
      <rect width="310" height="45" fill="#374151" rx="4"/>
      <circle cx="15" cy="22" r="8" fill="#06B6D4"/>
      <text x="15" y="27" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">M</text>
      <text x="35" y="18" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Market Order</text>
      <text x="35" y="32" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">Executes immediately at current market price</text>
      <text x="35" y="42" fill="#94A3B8" font-family="Arial, sans-serif" font-size="9">✓ Fast execution  ✗ Price uncertainty</text>
    </g>
    
    <!-- Limit Order -->
    <g transform="translate(20, 95)">
      <rect width="310" height="45" fill="#374151" rx="4"/>
      <circle cx="15" cy="22" r="8" fill="#10B981"/>
      <text x="15" y="27" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">L</text>
      <text x="35" y="18" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Limit Order</text>
      <text x="35" y="32" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">Executes only at specified price or better</text>
      <text x="35" y="42" fill="#94A3B8" font-family="Arial, sans-serif" font-size="9">✓ Price control  ✗ May not execute</text>
    </g>
    
    <!-- Stop Loss -->
    <g transform="translate(20, 150)">
      <rect width="310" height="45" fill="#374151" rx="4"/>
      <circle cx="15" cy="22" r="8" fill="#EF4444"/>
      <text x="15" y="27" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">S</text>
      <text x="35" y="18" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Stop Loss</text>
      <text x="35" y="32" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">Triggers market order when price hits stop level</text>
      <text x="35" y="42" fill="#94A3B8" font-family="Arial, sans-serif" font-size="9">✓ Risk management  ✗ May gap in volatile markets</text>
    </g>
    
    <!-- Stop Limit -->
    <g transform="translate(20, 205)">
      <rect width="310" height="45" fill="#374151" rx="4"/>
      <circle cx="15" cy="22" r="8" fill="#8B5CF6"/>
      <text x="15" y="27" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9" font-weight="bold">SL</text>
      <text x="35" y="18" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Stop Limit</text>
      <text x="35" y="32" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">Triggers limit order when price hits stop level</text>
      <text x="35" y="42" fill="#94A3B8" font-family="Arial, sans-serif" font-size="9">✓ Price + risk control  ✗ Complex setup</text>
    </g>
  </g>
  
  <!-- Trading Strategy Tips -->
  <g transform="translate(50, 300)">
    <rect width="700" height="80" fill="#1E293B" stroke="#334155" stroke-width="2" rx="8"/>
    <text x="350" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">When to Use Each Order Type</text>
    
    <g transform="translate(20, 35)">
      <text x="0" y="0" fill="#06B6D4" font-family="Arial, sans-serif" font-size="11" font-weight="bold">Market Orders:</text>
      <text x="0" y="15" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">• Quick entry/exit in liquid markets</text>
      <text x="0" y="28" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">• When speed matters more than price</text>
    </g>
    
    <g transform="translate(200, 35)">
      <text x="0" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="11" font-weight="bold">Limit Orders:</text>
      <text x="0" y="15" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">• Better price control</text>
      <text x="0" y="28" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">• Patient trading strategies</text>
    </g>
    
    <g transform="translate(380, 35)">
      <text x="0" y="0" fill="#EF4444" font-family="Arial, sans-serif" font-size="11" font-weight="bold">Stop Orders:</text>
      <text x="0" y="15" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">• Risk management</text>
      <text x="0" y="28" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">• Automated loss prevention</text>
    </g>
    
    <g transform="translate(560, 35)">
      <text x="0" y="0" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="11" font-weight="bold">Stop Limit:</text>
      <text x="0" y="15" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">• Advanced risk control</text>
      <text x="0" y="28" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">• Volatile market conditions</text>
    </g>
  </g>
</svg>
