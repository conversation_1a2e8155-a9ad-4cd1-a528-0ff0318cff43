
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Users, CheckCircle, XCircle, Github, Linkedin, Alert<PERSON>riangle } from "lucide-react";
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer } from "recharts";

interface TeamData {
  teamVerified: boolean;
  teamMembers: number;
  linkedinProfiles: number;
  githubActivity: number;
  transparency: number;
}

interface TeamAnalysisProps {
  teamData: TeamData | null;
  isLoading: boolean;
}

export default function TeamAnalysis({ teamData, isLoading }: TeamAnalysisProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-64 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  if (!teamData) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-muted-foreground">No team data available</p>
        </CardContent>
      </Card>
    );
  }

  // Radar chart data for team assessment
  const teamRadarData = [
    { metric: "Verification", value: teamData.teamVerified ? 100 : 0, fullMark: 100 },
    { metric: "Team Size", value: Math.min(100, teamData.teamMembers * 8), fullMark: 100 },
    { metric: "LinkedIn", value: Math.min(100, teamData.linkedinProfiles * 12), fullMark: 100 },
    { metric: "GitHub", value: Math.min(100, teamData.githubActivity), fullMark: 100 },
    { metric: "Transparency", value: teamData.transparency, fullMark: 100 }
  ];

  const getTeamRiskLevel = () => {
    const score = (
      (teamData.teamVerified ? 25 : 0) +
      Math.min(25, teamData.teamMembers * 3) +
      Math.min(20, teamData.linkedinProfiles * 5) +
      Math.min(15, teamData.githubActivity * 0.15) +
      Math.min(15, teamData.transparency * 0.15)
    );
    
    if (score >= 80) return { level: "Low", color: "default" };
    if (score >= 50) return { level: "Medium", color: "secondary" };
    return { level: "High", color: "destructive" };
  };

  const riskAssessment = getTeamRiskLevel();

  return (
    <div className="space-y-6">
      {/* Team Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users size={16} className="text-blue-500" />
              Team Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {teamData.teamVerified ? (
                <>
                  <CheckCircle className="h-6 w-6 text-green-500" />
                  <span className="font-bold text-green-600">Verified</span>
                </>
              ) : (
                <>
                  <XCircle className="h-6 w-6 text-red-500" />
                  <span className="font-bold text-red-600">Anonymous</span>
                </>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1">Team verification status</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users size={16} className="text-purple-500" />
              Team Size
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{teamData.teamMembers}</div>
            <p className="text-xs text-muted-foreground">Known team members</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Linkedin size={16} className="text-blue-600" />
              LinkedIn Profiles
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{teamData.linkedinProfiles}</div>
            <p className="text-xs text-muted-foreground">Professional profiles found</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Github size={16} className="text-gray-700" />
              GitHub Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{teamData.githubActivity}</div>
            <Progress value={Math.min(100, teamData.githubActivity)} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground">Development activity score</p>
          </CardContent>
        </Card>
      </div>

      {/* Team Risk Assessment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Team Risk Assessment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            <span className="text-lg font-semibold">Team Risk Level:</span>
            <Badge variant={riskAssessment.color as any} className="text-sm">
              {riskAssessment.level} Risk
            </Badge>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Radar Chart */}
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart data={teamRadarData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="metric" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar
                    name="Team Score"
                    dataKey="value"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.3}
                  />
                </RadarChart>
              </ResponsiveContainer>
            </div>

            {/* Team Metrics */}
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Transparency Score</label>
                <div className="mt-2">
                  <Progress value={teamData.transparency} className="h-3" />
                  <span className="text-sm text-muted-foreground">{teamData.transparency.toFixed(1)}/100</span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Professional Presence</label>
                <div className="mt-2">
                  <Progress value={Math.min(100, teamData.linkedinProfiles * 12)} className="h-3" />
                  <span className="text-sm text-muted-foreground">
                    {Math.min(100, teamData.linkedinProfiles * 12).toFixed(1)}/100
                  </span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Development Activity</label>
                <div className="mt-2">
                  <Progress value={Math.min(100, teamData.githubActivity)} className="h-3" />
                  <span className="text-sm text-muted-foreground">
                    {Math.min(100, teamData.githubActivity).toFixed(1)}/100
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Red Flags */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <XCircle className="h-5 w-5 text-red-500" />
            Team Red Flags
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              {
                condition: !teamData.teamVerified,
                flag: "Anonymous team - higher risk of exit scam",
                severity: "high"
              },
              {
                condition: teamData.teamMembers < 3,
                flag: "Very small team size - limited expertise and accountability",
                severity: "medium"
              },
              {
                condition: teamData.linkedinProfiles === 0,
                flag: "No professional profiles found - lack of credibility",
                severity: "high"
              },
              {
                condition: teamData.githubActivity < 20,
                flag: "Low development activity - project may be abandoned",
                severity: "medium"
              },
              {
                condition: teamData.transparency < 30,
                flag: "Poor transparency - limited communication with community",
                severity: "medium"
              }
            ].filter(item => item.condition).map((redFlag, index) => (
              <div 
                key={index} 
                className={`p-3 rounded-lg border ${redFlag.severity === 'high' ? 'border-red-200 bg-red-50' : 'border-orange-200 bg-orange-50'}`}
              >
                <div className="flex items-center gap-2">
                  <AlertTriangle className={`h-4 w-4 ${redFlag.severity === 'high' ? 'text-red-500' : 'text-orange-500'}`} />
                  <span className="text-sm font-medium">{redFlag.flag}</span>
                  <Badge variant={redFlag.severity === 'high' ? "destructive" : "secondary"}>
                    {redFlag.severity.toUpperCase()}
                  </Badge>
                </div>
              </div>
            ))}
            
            {/* If no red flags */}
            {![
              !teamData.teamVerified,
              teamData.teamMembers < 3,
              teamData.linkedinProfiles === 0,
              teamData.githubActivity < 20,
              teamData.transparency < 30
            ].some(Boolean) && (
              <div className="p-3 rounded-lg border border-green-200 bg-green-50">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">No major team red flags detected</span>
                  <Badge variant="default">GOOD</Badge>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Team Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
              <p className="text-sm">
                <strong>Verify team credentials:</strong> Cross-check LinkedIn profiles and GitHub repositories
              </p>
            </div>
            <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
              <p className="text-sm">
                <strong>Check communication:</strong> Review team's responsiveness in official channels
              </p>
            </div>
            <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
              <p className="text-sm">
                <strong>Research history:</strong> Look up team members' previous projects and reputation
              </p>
            </div>
            <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
              <p className="text-sm">
                <strong>Monitor updates:</strong> Track regular development progress and milestone delivery
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
