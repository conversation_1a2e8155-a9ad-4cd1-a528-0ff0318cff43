
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { FundamentalCoin } from "@/hooks/useFundamentalAnalysis";
import { Card } from "@/components/ui/card";
import { PriceChangeIndicator } from "@/components/rating/PriceChangeIndicator";
import { Progress } from "@/components/ui/progress";

interface FundamentalTableProps {
  coins: FundamentalCoin[];
  onSelectCoin: (coinId: string) => void;
  isCompact?: boolean;
}

export function FundamentalTable({ coins, onSelectCoin, isCompact = false }: FundamentalTableProps) {
  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 8) return "bg-green-500";
    if (score >= 6) return "bg-blue-500";
    if (score >= 4) return "bg-amber-500";
    return "bg-red-500";
  };
  
  return (
    <Card className="overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">Rank</TableHead>
              <TableHead>Asset</TableHead>
              <TableHead className="text-right">Price</TableHead>
              <TableHead className="text-right">24h</TableHead>
              {!isCompact && (
                <>
                  <TableHead className="text-right">Market Cap</TableHead>
                  <TableHead className="text-right">Tokenomics</TableHead>
                  <TableHead className="text-right">Development</TableHead>
                  <TableHead className="text-right">Utility</TableHead>
                </>
              )}
              <TableHead className="text-right">Fundamental Score</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {coins.map((coin, index) => (
              <TableRow 
                key={coin.id} 
                className="cursor-pointer hover:bg-secondary/30"
                onClick={() => onSelectCoin(coin.id)}
              >
                <TableCell className="font-medium">{index + 1}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full overflow-hidden bg-secondary flex items-center justify-center">
                      {coin.image ? (
                        <img src={coin.image} alt={coin.name} className="w-full h-full object-cover" />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-xs font-bold">
                          {coin.symbol.charAt(0)}
                        </div>
                      )}
                    </div>
                    <div>
                      <div className="font-medium">{coin.name}</div>
                      <div className="text-xs text-muted-foreground">{coin.symbol}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="text-right">${coin.price?.toLocaleString()}</TableCell>
                <TableCell className="text-right">
                  <PriceChangeIndicator changePercentage={coin.change24h} />
                </TableCell>
                {!isCompact && (
                  <>
                    <TableCell className="text-right">${(coin.marketCap / 1000000).toFixed(1)}M</TableCell>
                    <TableCell className="text-right">
                      {coin.fundamentalScore ? (
                        <div className="flex items-center justify-end gap-2">
                          <span className="text-xs">{coin.fundamentalScore.tokenomics.toFixed(1)}</span>
                          <Progress 
                            value={coin.fundamentalScore.tokenomics * 10} 
                            className={`w-16 h-1 ${getScoreColor(coin.fundamentalScore.tokenomics)}`} 
                          />
                        </div>
                      ) : (
                        "N/A"
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      {coin.fundamentalScore ? (
                        <div className="flex items-center justify-end gap-2">
                          <span className="text-xs">{coin.fundamentalScore.development.toFixed(1)}</span>
                          <Progress 
                            value={coin.fundamentalScore.development * 10} 
                            className={`w-16 h-1 ${getScoreColor(coin.fundamentalScore.development)}`} 
                          />
                        </div>
                      ) : (
                        "N/A"
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      {coin.fundamentalScore ? (
                        <div className="flex items-center justify-end gap-2">
                          <span className="text-xs">{coin.fundamentalScore.utility.toFixed(1)}</span>
                          <Progress 
                            value={coin.fundamentalScore.utility * 10} 
                            className={`w-16 h-1 ${getScoreColor(coin.fundamentalScore.utility)}`} 
                          />
                        </div>
                      ) : (
                        "N/A"
                      )}
                    </TableCell>
                  </>
                )}
                <TableCell className="text-right">
                  {coin.fundamentalScore ? (
                    <div className="flex items-center justify-end gap-2">
                      <span className="font-bold text-primary">{coin.fundamentalScore.total.toFixed(1)}</span>
                      <Progress 
                        value={coin.fundamentalScore.total * 10} 
                        className={`w-16 h-2 ${getScoreColor(coin.fundamentalScore.total)}`} 
                      />
                    </div>
                  ) : (
                    "N/A"
                  )}
                </TableCell>
              </TableRow>
            ))}
            {coins.length === 0 && (
              <TableRow>
                <TableCell colSpan={isCompact ? 5 : 9} className="text-center py-8">
                  No coins found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </Card>
  );
}
