<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400" fill="none">
  <defs>
    <linearGradient id="cexGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dexGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5B21B6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="#0F172A"/>
  
  <!-- Title -->
  <text x="400" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="22" font-weight="bold">Centralized vs Decentralized Exchanges</text>
  
  <!-- CEX Section -->
  <g transform="translate(50, 70)">
    <rect width="300" height="280" rx="12" fill="url(#cexGradient)" stroke="#60A5FA" stroke-width="2"/>
    <text x="150" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Centralized Exchange (CEX)</text>
    
    <!-- CEX Building illustration -->
    <g transform="translate(125, 50)">
      <rect width="50" height="60" fill="white" opacity="0.9" rx="4"/>
      <rect width="8" height="8" x="8" y="10" fill="#3B82F6"/>
      <rect width="8" height="8" x="20" y="10" fill="#3B82F6"/>
      <rect width="8" height="8" x="32" y="10" fill="#3B82F6"/>
      <rect width="8" height="8" x="8" y="25" fill="#3B82F6"/>
      <rect width="8" height="8" x="20" y="25" fill="#3B82F6"/>
      <rect width="8" height="8" x="32" y="25" fill="#3B82F6"/>
      <rect width="12" height="20" x="19" y="40" fill="#1E40AF"/>
      <text x="25" y="75" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">EXCHANGE</text>
    </g>
    
    <!-- CEX Features -->
    <g transform="translate(20, 130)">
      <text x="0" y="0" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Features:</text>
      <text x="0" y="20" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">✓ User-friendly interface</text>
      <text x="0" y="35" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">✓ High liquidity</text>
      <text x="0" y="50" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">✓ Customer support</text>
      <text x="0" y="65" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">✓ Fiat on/off ramps</text>
      <text x="0" y="80" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">✓ Advanced trading tools</text>
      
      <text x="0" y="110" fill="#FEE2E2" font-family="Arial, sans-serif" font-size="11">✗ Custody of your funds</text>
      <text x="0" y="125" fill="#FEE2E2" font-family="Arial, sans-serif" font-size="11">✗ KYC/AML requirements</text>
      <text x="0" y="140" fill="#FEE2E2" font-family="Arial, sans-serif" font-size="11">✗ Centralized control</text>
    </g>
  </g>
  
  <!-- DEX Section -->
  <g transform="translate(450, 70)">
    <rect width="300" height="280" rx="12" fill="url(#dexGradient)" stroke="#A78BFA" stroke-width="2"/>
    <text x="150" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Decentralized Exchange (DEX)</text>
    
    <!-- DEX Network illustration -->
    <g transform="translate(100, 50)">
      <!-- Central node -->
      <circle cx="50" cy="30" r="12" fill="white" opacity="0.9"/>
      <text x="50" y="35" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="10" font-weight="bold">DEX</text>
      
      <!-- Connected nodes -->
      <circle cx="20" cy="10" r="8" fill="white" opacity="0.7"/>
      <circle cx="80" cy="10" r="8" fill="white" opacity="0.7"/>
      <circle cx="20" cy="50" r="8" fill="white" opacity="0.7"/>
      <circle cx="80" cy="50" r="8" fill="white" opacity="0.7"/>
      
      <!-- Connection lines -->
      <line x1="38" y1="22" x2="28" y2="18" stroke="white" stroke-width="2"/>
      <line x1="62" y1="22" x2="72" y2="18" stroke="white" stroke-width="2"/>
      <line x1="38" y1="38" x2="28" y2="42" stroke="white" stroke-width="2"/>
      <line x1="62" y1="38" x2="72" y2="42" stroke="white" stroke-width="2"/>
      
      <text x="50" y="75" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">NETWORK</text>
    </g>
    
    <!-- DEX Features -->
    <g transform="translate(20, 130)">
      <text x="0" y="0" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Features:</text>
      <text x="0" y="20" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">✓ Self-custody of funds</text>
      <text x="0" y="35" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">✓ No KYC required</text>
      <text x="0" y="50" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">✓ Permissionless access</text>
      <text x="0" y="65" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">✓ Transparent operations</text>
      <text x="0" y="80" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">✓ Global accessibility</text>
      
      <text x="0" y="110" fill="#FEE2E2" font-family="Arial, sans-serif" font-size="11">✗ Complex for beginners</text>
      <text x="0" y="125" fill="#FEE2E2" font-family="Arial, sans-serif" font-size="11">✗ Gas fees required</text>
      <text x="0" y="140" fill="#FEE2E2" font-family="Arial, sans-serif" font-size="11">✗ Limited fiat support</text>
    </g>
  </g>
  
  <!-- VS indicator -->
  <g transform="translate(375, 200)">
    <circle cx="25" cy="25" r="25" fill="#F59E0B"/>
    <text x="25" y="32" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">VS</text>
  </g>
  
  <!-- Bottom comparison -->
  <g transform="translate(50, 370)">
    <text x="0" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Best for: Beginners, high-volume trading</text>
    <text x="400" y="0" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Best for: Privacy, DeFi integration</text>
  </g>
</svg>
