
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { DropletIcon, Lock, TrendingUp, AlertCircle } from "lucide-react";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from "recharts";

interface LiquidityData {
  totalLiquidity: number;
  lockedPercentage: number;
  liquidityProviders: number;
  dailyVolume: number;
  priceImpact: number;
}

interface LiquidityAnalysisProps {
  liquidityData: LiquidityData | null;
  isLoading: boolean;
}

export default function LiquidityAnalysis({ liquidityData, isLoading }: LiquidityAnalysisProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-64 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  if (!liquidityData) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-muted-foreground">No liquidity data available</p>
        </CardContent>
      </Card>
    );
  }

  // Mock liquidity history data
  const liquidityHistory = [
    { date: "7d ago", liquidity: liquidityData.totalLiquidity * 0.6, volume: liquidityData.dailyVolume * 0.4 },
    { date: "6d ago", liquidity: liquidityData.totalLiquidity * 0.7, volume: liquidityData.dailyVolume * 0.6 },
    { date: "5d ago", liquidity: liquidityData.totalLiquidity * 0.8, volume: liquidityData.dailyVolume * 0.8 },
    { date: "4d ago", liquidity: liquidityData.totalLiquidity * 0.9, volume: liquidityData.dailyVolume * 0.9 },
    { date: "3d ago", liquidity: liquidityData.totalLiquidity * 0.95, volume: liquidityData.dailyVolume * 1.1 },
    { date: "2d ago", liquidity: liquidityData.totalLiquidity * 0.98, volume: liquidityData.dailyVolume * 1.2 },
    { date: "Today", liquidity: liquidityData.totalLiquidity, volume: liquidityData.dailyVolume }
  ];

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(2)}M`;
    if (value >= 1000) return `$${(value / 1000).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };

  const getLiquidityRisk = (liquidity: number, locked: number) => {
    if (liquidity > 500000 && locked > 80) return { level: "Low", color: "default" };
    if (liquidity > 100000 && locked > 50) return { level: "Medium", color: "secondary" };
    return { level: "High", color: "destructive" };
  };

  const riskAssessment = getLiquidityRisk(liquidityData.totalLiquidity, liquidityData.lockedPercentage);

  return (
    <div className="space-y-6">
      {/* Liquidity Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <DropletIcon size={16} className="text-blue-500" />
              Total Liquidity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(liquidityData.totalLiquidity)}</div>
            <p className="text-xs text-muted-foreground">Available trading liquidity</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Lock size={16} className="text-green-500" />
              Locked Percentage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{liquidityData.lockedPercentage.toFixed(1)}%</div>
            <Progress value={liquidityData.lockedPercentage} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground">Liquidity locked</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp size={16} className="text-purple-500" />
              Daily Volume
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(liquidityData.dailyVolume)}</div>
            <p className="text-xs text-muted-foreground">24h trading volume</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <AlertCircle size={16} className="text-orange-500" />
              Price Impact
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{liquidityData.priceImpact.toFixed(2)}%</div>
            <Badge variant={liquidityData.priceImpact > 15 ? "destructive" : liquidityData.priceImpact > 5 ? "secondary" : "default"}>
              {liquidityData.priceImpact > 15 ? "High" : liquidityData.priceImpact > 5 ? "Medium" : "Low"}
            </Badge>
            <p className="text-xs text-muted-foreground">For $1K trade</p>
          </CardContent>
        </Card>
      </div>

      {/* Risk Assessment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-orange-500" />
            Liquidity Risk Assessment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <span className="text-lg font-semibold">Overall Risk Level:</span>
            <Badge variant={riskAssessment.color as any} className="text-sm">
              {riskAssessment.level} Risk
            </Badge>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Liquidity Providers</h4>
              <div className="text-2xl font-bold">{liquidityData.liquidityProviders.toLocaleString()}</div>
              <p className="text-sm text-muted-foreground">
                {liquidityData.liquidityProviders > 100 ? "Well distributed" : "Concentrated risk"}
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Volume/Liquidity Ratio</h4>
              <div className="text-2xl font-bold">
                {((liquidityData.dailyVolume / liquidityData.totalLiquidity) * 100).toFixed(1)}%
              </div>
              <p className="text-sm text-muted-foreground">Daily turnover rate</p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Lock Duration</h4>
              <div className="text-2xl font-bold">
                {liquidityData.lockedPercentage > 80 ? "Long-term" : liquidityData.lockedPercentage > 50 ? "Medium" : "Short"}
              </div>
              <p className="text-sm text-muted-foreground">Estimated lock period</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Liquidity History Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Liquidity Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={liquidityHistory}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis tickFormatter={(value) => formatCurrency(value)} />
                  <Tooltip formatter={(value) => [formatCurrency(value as number), "Liquidity"]} />
                  <Area 
                    type="monotone" 
                    dataKey="liquidity" 
                    stroke="#3b82f6" 
                    fill="#3b82f6" 
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Volume History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={liquidityHistory}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis tickFormatter={(value) => formatCurrency(value)} />
                  <Tooltip formatter={(value) => [formatCurrency(value as number), "Volume"]} />
                  <Bar dataKey="volume" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liquidity Warnings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            Liquidity Warnings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              {
                condition: liquidityData.totalLiquidity < 100000,
                warning: "Low total liquidity may result in high slippage",
                severity: "high"
              },
              {
                condition: liquidityData.lockedPercentage < 50,
                warning: "Insufficient liquidity lock exposes investors to rug pulls",
                severity: "high"
              },
              {
                condition: liquidityData.priceImpact > 10,
                warning: "High price impact indicates thin liquidity",
                severity: "medium"
              },
              {
                condition: liquidityData.liquidityProviders < 50,
                warning: "Concentrated liquidity providers increase manipulation risk",
                severity: "medium"
              }
            ].filter(item => item.condition).map((warning, index) => (
              <div 
                key={index} 
                className={`p-3 rounded-lg border ${warning.severity === 'high' ? 'border-red-200 bg-red-50' : 'border-orange-200 bg-orange-50'}`}
              >
                <div className="flex items-center gap-2">
                  <AlertCircle className={`h-4 w-4 ${warning.severity === 'high' ? 'text-red-500' : 'text-orange-500'}`} />
                  <span className="text-sm font-medium">{warning.warning}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
