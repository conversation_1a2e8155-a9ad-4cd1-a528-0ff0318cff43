
// DeFi API shared types

// Yield farming types
export interface YieldOpportunity {
  protocol: string;
  pool: string;
  chain: string;
  baseApy: number;
  rewardsApr: number;
  realApy: number;
  riskScore: number;
  tvl: number;
}

// Protocol risk types
export interface ProtocolRisk {
  name: string;
  description: string;
  riskScore: number;
  tvl: number;
  age: number;
  auditCount: number;
  auditFirms: string[];
  securityIncidents: number;
  contractRisk: number;
}

// Strategy builder types
export interface Allocation {
  name: string;
  percentage: number;
  color: string;
}

export interface Strategy {
  id: string;
  name: string;
  description: string;
  riskLevel: "Low" | "Medium" | "High";
  expectedApy: number;
  timeframe: string;
  protocolCount: number;
  allocation: Allocation[];
}

// Liquidity pool types
export interface LiquidityPool {
  id: string;
  name: string;
  protocol: string;
  token0: string;
  token1: string;
  tvl: number;
  volume24h: number;
  apy: number;
  ilRisk: number;
  impermanentLoss7d: number;
  impermanentLoss30d: number;
  historicalData: Array<{
    date: string;
    tvl: number;
    volume: number;
    apy: number;
  }>;
}

// Protocol health types
export interface ProtocolHealth {
  id: string;
  name: string;
  category: string;
  tvl: number;
  tvlChange24h: number;
  tvlTrend: Array<{ date: string; tvl: number }>;
  userCount: number;
  userGrowth: number;
  userTrend: Array<{ date: string; users: number }>;
  securityScore: number;
  audits: Array<{
    firm: string;
    date: string;
    status: string;
    issues: { critical: number; high: number; medium: number; low: number };
  }>;
  bugBounty: boolean;
  exploits: Array<{ date: string; description: string; amount: number; fixed: boolean }>;
}

// Gas optimization types
export interface GasInfo {
  networkId: string;
  networkName: string;
  currentGwei: number;
  baseFee: number;
  priorityFee: number;
  estimatedTimes: Record<string, number>;
  historicalData: Array<{ timestamp: number; price: number }>;
  forecast: Array<{ hour: number; price: number; confidence: number }>;
  recommendations: {
    fast: { time: string; price: number };
    standard: { time: string; price: number };
    slow: { time: string; price: number };
  };
}

// Bridge types
export interface Bridge {
  id: string;
  name: string;
  supportedNetworks: string[];
  fees: {
    percentage: number;
    fixed: Record<string, number>;
  };
  speed: Record<string, number>;
  securityScore: number;
  securityDetails: {
    audited: boolean;
    auditors: string[];
    tvlLocked: number;
    incidentHistory: Array<{ date: string; description: string; severity: string }>;
  };
  userRating: number;
  volume24h: number;
}
