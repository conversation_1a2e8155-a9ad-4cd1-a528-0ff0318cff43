/**
 * Complete API Provider Test Suite
 * Comprehensive testing for the entire API provider save functionality
 */

import { supabase } from '@/integrations/supabase/client';

export class CompleteApiProviderTest {
  /**
   * Step 1: Test Supabase connection
   */
  static async testSupabaseConnection(): Promise<boolean> {
    console.log('🔗 Testing Supabase connection...');
    
    try {
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        console.log('❌ Supabase connection error:', error);
        return false;
      }
      
      console.log('✅ Supabase connection successful');
      console.log('👤 Session data:', data);
      return true;
      
    } catch (error) {
      console.log('❌ Supabase connection failed:', error);
      return false;
    }
  }

  /**
   * Step 2: Test database table existence
   */
  static async testTableExistence(): Promise<{
    exists: boolean;
    error?: string;
    needsMigration: boolean;
  }> {
    console.log('🗄️ Testing api_providers table existence...');
    
    try {
      const { data, error } = await supabase
        .from('api_providers')
        .select('count')
        .limit(1);

      if (error) {
        if (error.code === '42P01' || error.message.includes('does not exist')) {
          console.log('❌ api_providers table does not exist');
          console.log('📋 Migration needed!');
          return {
            exists: false,
            error: 'Table does not exist',
            needsMigration: true
          };
        } else {
          console.log('❌ Database error:', error);
          return {
            exists: false,
            error: error.message,
            needsMigration: false
          };
        }
      }

      console.log('✅ api_providers table exists');
      return {
        exists: true,
        needsMigration: false
      };

    } catch (error: any) {
      console.log('❌ Table existence test failed:', error);
      return {
        exists: false,
        error: error.message,
        needsMigration: true
      };
    }
  }

  /**
   * Step 3: Test basic CRUD operations
   */
  static async testBasicCRUD(): Promise<{
    success: boolean;
    operations: Record<string, boolean>;
    error?: string;
  }> {
    console.log('🧪 Testing basic CRUD operations...');
    
    const operations = {
      create: false,
      read: false,
      update: false,
      delete: false
    };

    try {
      // Test CREATE
      console.log('➕ Testing CREATE operation...');
      const testProvider = {
        name: `Test Provider ${Date.now()}`,
        type: 'market' as const,
        priority: 99,
        rate_limit_per_minute: 60,
        monthly_quota: 5000,
        cost_per_request: 0.002,
        is_active: true,
        config: { test: true }
      };

      const { data: created, error: createError } = await supabase
        .from('api_providers')
        .insert(testProvider)
        .select()
        .single();

      if (createError) {
        console.log('❌ CREATE failed:', createError);
        return {
          success: false,
          operations,
          error: `CREATE failed: ${createError.message}`
        };
      }

      operations.create = true;
      console.log('✅ CREATE successful:', created);

      // Test READ
      console.log('👁️ Testing READ operation...');
      const { data: read, error: readError } = await supabase
        .from('api_providers')
        .select('*')
        .eq('id', created.id)
        .single();

      if (readError) {
        console.log('❌ READ failed:', readError);
        return {
          success: false,
          operations,
          error: `READ failed: ${readError.message}`
        };
      }

      operations.read = true;
      console.log('✅ READ successful:', read);

      // Test UPDATE
      console.log('✏️ Testing UPDATE operation...');
      const { data: updated, error: updateError } = await supabase
        .from('api_providers')
        .update({ priority: 88 })
        .eq('id', created.id)
        .select()
        .single();

      if (updateError) {
        console.log('❌ UPDATE failed:', updateError);
        return {
          success: false,
          operations,
          error: `UPDATE failed: ${updateError.message}`
        };
      }

      operations.update = true;
      console.log('✅ UPDATE successful:', updated);

      // Test DELETE
      console.log('🗑️ Testing DELETE operation...');
      const { error: deleteError } = await supabase
        .from('api_providers')
        .delete()
        .eq('id', created.id);

      if (deleteError) {
        console.log('❌ DELETE failed:', deleteError);
        return {
          success: false,
          operations,
          error: `DELETE failed: ${deleteError.message}`
        };
      }

      operations.delete = true;
      console.log('✅ DELETE successful');

      return {
        success: true,
        operations
      };

    } catch (error: any) {
      console.log('❌ CRUD test failed:', error);
      return {
        success: false,
        operations,
        error: error.message
      };
    }
  }

  /**
   * Step 4: Test React Query hooks
   */
  static async testReactQueryHooks(): Promise<{
    success: boolean;
    error?: string;
  }> {
    console.log('🎣 Testing React Query hooks...');
    
    try {
      // Import the hooks dynamically
      const { getAllApiProviders } = await import('@/services/api/providers/apiProviderService');
      
      const providers = await getAllApiProviders();
      console.log('✅ React Query hooks working, providers:', providers);
      
      return {
        success: true
      };
      
    } catch (error: any) {
      console.log('❌ React Query hooks test failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Run complete test suite
   */
  static async runCompleteTest(): Promise<{
    success: boolean;
    results: Record<string, any>;
    recommendations: string[];
  }> {
    console.log('🚀 Running Complete API Provider Test Suite...');
    console.log('=' .repeat(60));
    
    const results: Record<string, any> = {};
    const recommendations: string[] = [];

    // Test 1: Supabase Connection
    results.supabaseConnection = await this.testSupabaseConnection();
    
    if (!results.supabaseConnection) {
      recommendations.push('Fix Supabase connection configuration');
      return {
        success: false,
        results,
        recommendations
      };
    }

    // Test 2: Table Existence
    results.tableExistence = await this.testTableExistence();
    
    if (!results.tableExistence.exists) {
      if (results.tableExistence.needsMigration) {
        recommendations.push('Run database migration script in Supabase SQL Editor');
        recommendations.push('Use getMigrationScript() in browser console to get the script');
      } else {
        recommendations.push('Check database permissions and configuration');
      }
      
      return {
        success: false,
        results,
        recommendations
      };
    }

    // Test 3: CRUD Operations
    results.crudOperations = await this.testBasicCRUD();
    
    if (!results.crudOperations.success) {
      recommendations.push('Check database permissions and RLS policies');
      recommendations.push('Verify table schema matches expected structure');
      
      return {
        success: false,
        results,
        recommendations
      };
    }

    // Test 4: React Query Hooks
    results.reactQueryHooks = await this.testReactQueryHooks();
    
    if (!results.reactQueryHooks.success) {
      recommendations.push('Check React Query configuration and imports');
    }

    const allTestsPassed = Object.values(results).every(result => 
      typeof result === 'boolean' ? result : result.success
    );

    if (allTestsPassed) {
      recommendations.push('All tests passed! API provider functionality should work correctly.');
      recommendations.push('If you still have issues, check browser console for form submission errors.');
    }

    console.log('=' .repeat(60));
    console.log('📊 Test Results Summary:');
    console.log('Supabase Connection:', results.supabaseConnection ? '✅' : '❌');
    console.log('Table Existence:', results.tableExistence.exists ? '✅' : '❌');
    console.log('CRUD Operations:', results.crudOperations.success ? '✅' : '❌');
    console.log('React Query Hooks:', results.reactQueryHooks.success ? '✅' : '❌');
    console.log('Overall:', allTestsPassed ? '✅ PASS' : '❌ FAIL');
    console.log('=' .repeat(60));

    return {
      success: allTestsPassed,
      results,
      recommendations
    };
  }
}

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).completeApiProviderTest = CompleteApiProviderTest;
  (window as any).testApiProviderComplete = () => CompleteApiProviderTest.runCompleteTest();
}
