/**
 * AI-Enhanced Team & Developer Screener Component
 * Analyzes project teams, developer activity, and credibility
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Loader2, Users, Code, Shield, AlertTriangle, Github, Linkedin, TrendingUp, Info } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import {
  fetchTeamAnalysis,
  getTeamInsights,
  TeamAnalysis,
  TeamMember
} from '@/services/api/teamAnalysisService';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

interface TeamScreenerProps {
  className?: string;
}

export default function TeamScreener({ className }: TeamScreenerProps) {
  const [viewMode, setViewMode] = useState<'overview' | 'detailed' | 'metrics'>('overview');
  const [sortBy, setSortBy] = useState<'overallScore' | 'teamCredibility' | 'transparencyScore'>('overallScore');
  const [recommendationFilter, setRecommendationFilter] = useState<string>('all');

  const { data: teamAnalyses, isLoading, error, refetch } = useQuery({
    queryKey: ['team-analysis'],
    queryFn: fetchTeamAnalysis,
    staleTime: 60 * 60 * 1000, // 1 hour
    retry: 2
  });

  const { data: insights } = useQuery({
    queryKey: ['team-insights', teamAnalyses],
    queryFn: () => teamAnalyses ? getTeamInsights(teamAnalyses) : Promise.resolve(''),
    enabled: !!teamAnalyses,
    staleTime: 60 * 60 * 1000
  });

  const getRecommendationColor = (recommendation: string): string => {
    switch (recommendation) {
      case 'strong-buy': return '#10b981';
      case 'buy': return '#3b82f6';
      case 'hold': return '#f59e0b';
      case 'high-risk': return '#ef4444';
      case 'avoid': return '#dc2626';
      default: return '#6b7280';
    }
  };

  const getCredibilityColor = (score: number): string => {
    if (score > 80) return '#10b981';
    if (score > 60) return '#3b82f6';
    if (score > 40) return '#f59e0b';
    return '#ef4444';
  };

  const filterByRecommendation = (data: TeamAnalysis[]) => {
    if (recommendationFilter === 'all') return data;
    return data.filter(item => item.recommendation === recommendationFilter);
  };

  const renderOverviewCards = () => {
    if (!teamAnalyses) return null;

    const filteredData = filterByRecommendation(teamAnalyses);
    const sortedData = [...filteredData].sort((a, b) => {
      switch (sortBy) {
        case 'overallScore': return b.overallScore - a.overallScore;
        case 'teamCredibility': return b.teamCredibility - a.teamCredibility;
        case 'transparencyScore': return b.transparencyScore - a.transparencyScore;
        default: return 0;
      }
    });

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sortedData.slice(0, 12).map((analysis) => (
          <Card key={analysis.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">{analysis.symbol}</CardTitle>
                  <p className="text-sm text-muted-foreground">{analysis.name}</p>
                </div>
                <Badge 
                  style={{ backgroundColor: getRecommendationColor(analysis.recommendation) }}
                  className="text-white"
                >
                  {analysis.recommendation.replace('-', ' ')}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Overall Score */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Overall Score</span>
                  <span className="font-medium">{analysis.overallScore.toFixed(0)}/100</span>
                </div>
                <Progress value={analysis.overallScore} className="h-2" />
              </div>

              {/* Team Credibility */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Team Credibility</span>
                  <span className="font-medium">{analysis.teamCredibility.toFixed(0)}/100</span>
                </div>
                <Progress 
                  value={analysis.teamCredibility} 
                  className="h-2"
                  style={{ 
                    '--progress-background': getCredibilityColor(analysis.teamCredibility) 
                  } as React.CSSProperties}
                />
              </div>

              {/* Transparency */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Transparency</span>
                  <span className="font-medium">{analysis.transparencyScore}/100</span>
                </div>
                <Progress value={analysis.transparencyScore} className="h-2" />
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  <span>{analysis.teamSize} members</span>
                </div>
                <div className="flex items-center gap-1">
                  <Code className="h-3 w-3" />
                  <span>{analysis.developerMetrics.activeDevelopers} devs</span>
                </div>
                <div className="flex items-center gap-1">
                  <Github className="h-3 w-3" />
                  <span>{analysis.hasGithubRepo ? 'Public' : 'Private'}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  <span>{analysis.hasPublicTeam ? 'Known' : 'Anonymous'}</span>
                </div>
              </div>

              {/* Risk Factors */}
              {analysis.riskFactors.length > 0 && (
                <div>
                  <div className="text-xs text-red-600 font-medium mb-1">Risk Factors:</div>
                  <div className="flex flex-wrap gap-1">
                    {analysis.riskFactors.slice(0, 2).map((factor, index) => (
                      <Badge key={index} variant="destructive" className="text-xs">
                        {factor}
                      </Badge>
                    ))}
                    {analysis.riskFactors.length > 2 && (
                      <Badge variant="destructive" className="text-xs">
                        +{analysis.riskFactors.length - 2} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Positive Factors */}
              {analysis.positiveFactors.length > 0 && (
                <div>
                  <div className="text-xs text-green-600 font-medium mb-1">Strengths:</div>
                  <div className="flex flex-wrap gap-1">
                    {analysis.positiveFactors.slice(0, 2).map((factor, index) => (
                      <Badge key={index} variant="default" className="text-xs">
                        {factor}
                      </Badge>
                    ))}
                    {analysis.positiveFactors.length > 2 && (
                      <Badge variant="default" className="text-xs">
                        +{analysis.positiveFactors.length - 2} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderDetailedView = () => {
    if (!teamAnalyses) return null;

    const topProjects = teamAnalyses
      .filter(analysis => analysis.overallScore > 60)
      .slice(0, 8);

    return (
      <div className="space-y-6">
        {topProjects.map((analysis) => (
          <Card key={analysis.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">{analysis.name} ({analysis.symbol})</CardTitle>
                  <div className="flex items-center gap-4 mt-2">
                    <Badge 
                      style={{ backgroundColor: getRecommendationColor(analysis.recommendation) }}
                      className="text-white"
                    >
                      {analysis.recommendation.replace('-', ' ')}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      Overall Score: {analysis.overallScore.toFixed(0)}/100
                    </span>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Team Information */}
                <div>
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Team Information
                  </h4>
                  
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Team Size:</span>
                        <span className="ml-2 font-medium">{analysis.teamSize} members</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Public Team:</span>
                        <span className="ml-2 font-medium">
                          {analysis.hasPublicTeam ? '✅ Yes' : '❌ No'}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">GitHub Repo:</span>
                        <span className="ml-2 font-medium">
                          {analysis.hasGithubRepo ? '✅ Yes' : '❌ No'}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Whitepaper:</span>
                        <span className="ml-2 font-medium">
                          {analysis.hasWhitepaper ? '✅ Yes' : '❌ No'}
                        </span>
                      </div>
                    </div>

                    {analysis.teamMembers.length > 0 && (
                      <div>
                        <div className="text-sm font-medium mb-2">Key Team Members:</div>
                        <div className="space-y-2">
                          {analysis.teamMembers.slice(0, 3).map((member, index) => (
                            <div key={index} className="flex items-center justify-between text-sm">
                              <div>
                                <span className="font-medium">{member.name}</span>
                                <span className="text-muted-foreground ml-2">({member.role})</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline">{member.experience}y exp</Badge>
                                <Badge 
                                  style={{ backgroundColor: getCredibilityColor(member.credibilityScore) }}
                                  className="text-white text-xs"
                                >
                                  {member.credibilityScore.toFixed(0)}
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Developer Metrics */}
                <div>
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <Code className="h-4 w-4" />
                    Developer Activity
                  </h4>
                  
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Total Developers:</span>
                        <span className="ml-2 font-medium">{analysis.developerMetrics.totalDevelopers}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Active Developers:</span>
                        <span className="ml-2 font-medium">{analysis.developerMetrics.activeDevelopers}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Core Contributors:</span>
                        <span className="ml-2 font-medium">{analysis.developerMetrics.coreContributors}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Commits/Week:</span>
                        <span className="ml-2 font-medium">{analysis.developerMetrics.commitFrequency}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Code Quality:</span>
                        <span className="ml-2 font-medium">{analysis.developerMetrics.codeQuality}/100</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Activity Trend:</span>
                        <span className={`ml-2 font-medium ${
                          analysis.developerMetrics.activityTrend === 'increasing' ? 'text-green-600' :
                          analysis.developerMetrics.activityTrend === 'decreasing' ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {analysis.developerMetrics.activityTrend === 'increasing' ? '↗' : 
                           analysis.developerMetrics.activityTrend === 'decreasing' ? '↘' : '→'} 
                          {analysis.developerMetrics.activityTrend}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">GitHub Stars:</span>
                        <span className="ml-2 font-medium">{analysis.developerMetrics.githubStars.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">GitHub Forks:</span>
                        <span className="ml-2 font-medium">{analysis.developerMetrics.githubForks.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Social Metrics */}
              <div className="mt-6">
                <h4 className="font-semibold mb-3">Social Proof</h4>
                <div className="grid grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold text-blue-600">
                      {(analysis.socialMetrics.twitterFollowers / 1000).toFixed(0)}K
                    </div>
                    <div className="text-xs text-muted-foreground">Twitter</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-purple-600">
                      {(analysis.socialMetrics.discordMembers / 1000).toFixed(0)}K
                    </div>
                    <div className="text-xs text-muted-foreground">Discord</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-blue-500">
                      {(analysis.socialMetrics.telegramMembers / 1000).toFixed(0)}K
                    </div>
                    <div className="text-xs text-muted-foreground">Telegram</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-orange-600">
                      {(analysis.socialMetrics.redditSubscribers / 1000).toFixed(0)}K
                    </div>
                    <div className="text-xs text-muted-foreground">Reddit</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderMetricsView = () => {
    if (!teamAnalyses) return null;

    const chartData = teamAnalyses.slice(0, 10).map(analysis => ({
      name: analysis.symbol,
      teamCredibility: analysis.teamCredibility,
      transparency: analysis.transparencyScore,
      devActivity: analysis.developerMetrics.codeQuality,
      overall: analysis.overallScore
    }));

    const recommendationData = [
      { name: 'Strong Buy', count: teamAnalyses.filter(a => a.recommendation === 'strong-buy').length },
      { name: 'Buy', count: teamAnalyses.filter(a => a.recommendation === 'buy').length },
      { name: 'Hold', count: teamAnalyses.filter(a => a.recommendation === 'hold').length },
      { name: 'High Risk', count: teamAnalyses.filter(a => a.recommendation === 'high-risk').length },
      { name: 'Avoid', count: teamAnalyses.filter(a => a.recommendation === 'avoid').length }
    ];

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Team Quality Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="teamCredibility" fill="#3b82f6" name="Team Credibility" />
                    <Bar dataKey="transparency" fill="#10b981" name="Transparency" />
                    <Bar dataKey="devActivity" fill="#f59e0b" name="Dev Activity" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recommendation Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={recommendationData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-green-600">
              {teamAnalyses.filter(a => a.hasPublicTeam).length}
            </div>
            <div className="text-sm text-muted-foreground">Public Teams</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {teamAnalyses.filter(a => a.hasGithubRepo).length}
            </div>
            <div className="text-sm text-muted-foreground">GitHub Repos</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-600">
              {Math.round(teamAnalyses.reduce((sum, a) => sum + a.teamCredibility, 0) / teamAnalyses.length)}
            </div>
            <div className="text-sm text-muted-foreground">Avg Credibility</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-amber-600">
              {Math.round(teamAnalyses.reduce((sum, a) => sum + a.developerMetrics.activeDevelopers, 0) / teamAnalyses.length)}
            </div>
            <div className="text-sm text-muted-foreground">Avg Active Devs</div>
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-muted-foreground">Analyzing project teams...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-muted-foreground mb-4">Failed to load team data</p>
            <Button onClick={() => refetch()} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const recommendations = teamAnalyses ? 
    Array.from(new Set(teamAnalyses.map(a => a.recommendation))) : [];

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-primary" />
            <CardTitle>AI Team Screener</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="overallScore">Overall Score</SelectItem>
                <SelectItem value="teamCredibility">Team Credibility</SelectItem>
                <SelectItem value="transparencyScore">Transparency</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={recommendationFilter} onValueChange={setRecommendationFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                {recommendations.map(rec => (
                  <SelectItem key={rec} value={rec}>
                    {rec.replace('-', ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button onClick={() => refetch()} variant="outline" size="sm">
              Refresh
            </Button>
          </div>
        </div>
        
        {insights && (
          <div className="bg-purple-50 dark:bg-purple-950 p-3 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-purple-800 dark:text-purple-200">{insights}</p>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="detailed">Detailed</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-4">
            <div className="max-h-96 overflow-y-auto">
              {renderOverviewCards()}
            </div>
          </TabsContent>

          <TabsContent value="detailed" className="mt-4">
            <div className="max-h-96 overflow-y-auto">
              {renderDetailedView()}
            </div>
          </TabsContent>

          <TabsContent value="metrics" className="mt-4">
            {renderMetricsView()}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
