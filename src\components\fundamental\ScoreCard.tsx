
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface ScoreCardProps {
  title: string;
  score: number;
  description: string;
  icon: React.ReactNode;
}

export function ScoreCard({ title, score, description, icon }: ScoreCardProps) {
  const getScoreColor = (score: number) => {
    if (score >= 8) return "text-green-500";
    if (score >= 6) return "text-blue-500";
    if (score >= 4) return "text-amber-500";
    return "text-red-500";
  };
  
  const getProgressValue = (score: number) => score * 10;
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-md flex items-center gap-2">
          {icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">{description}</span>
            <span className={`text-xl font-bold ${getScoreColor(score)}`}>
              {score.toFixed(1)}
            </span>
          </div>
          <Progress value={getProgressValue(score)} />
        </div>
      </CardContent>
    </Card>
  );
}
