
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { useSearch } from '@/hooks/useSearch';
import { Loader2, Search, TrendingUp, ArrowUpRight } from 'lucide-react';

interface SearchCommandProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SearchCommand({ open, onOpenChange }: SearchCommandProps) {
  const navigate = useNavigate();
  const { 
    searchQuery, 
    searchResults, 
    isLoading, 
    handleSearch, 
    handleSelect 
  } = useSearch();
  
  // Handle keyboard shortcut (Ctrl+K or Cmd+K)
  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if ((e.key === 'k' && (e.metaKey || e.ctrlKey)) || e.key === '/') {
        e.preventDefault();
        onOpenChange(!open);
      }
    };
    
    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, [open, onOpenChange]);

  return (
    <CommandDialog open={open} onOpenChange={onOpenChange}>
      <CommandInput 
        placeholder="Search coins and tokens..." 
        value={searchQuery}
        onValueChange={handleSearch}
      />
      <CommandList>
        <CommandEmpty>
          {isLoading ? (
            <div className="flex items-center justify-center py-6">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span>Searching...</span>
            </div>
          ) : (
            "No results found."
          )}
        </CommandEmpty>
        
        {searchResults.length > 0 && (
          <CommandGroup heading="Coins & Tokens">
            {searchResults.map((coin: any) => (
              <CommandItem 
                key={coin.id}
                onSelect={() => handleSelect(coin.id, coin.name)}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-secondary rounded-full flex items-center justify-center overflow-hidden">
                    {coin.thumb ? (
                      <img src={coin.thumb} alt={coin.name} className="h-full w-full object-cover" />
                    ) : (
                      <span className="text-xs font-bold">{coin.symbol?.charAt(0).toUpperCase()}</span>
                    )}
                  </div>
                  <span>{coin.name}</span>
                  <span className="text-xs text-muted-foreground uppercase">{coin.symbol}</span>
                  
                  {coin.marketData?.price_change_percentage_24h && (
                    <span className={`text-xs font-medium ml-2 ${
                      coin.marketData.price_change_percentage_24h >= 0 
                        ? 'text-green-500' 
                        : 'text-red-500'
                    }`}>
                      {coin.marketData.price_change_percentage_24h >= 0 ? '↑' : '↓'}
                      {Math.abs(coin.marketData.price_change_percentage_24h).toFixed(2)}%
                    </span>
                  )}
                </div>
                <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
              </CommandItem>
            ))}
          </CommandGroup>
        )}
        
        <CommandGroup heading="Navigation">
          <CommandItem onSelect={() => { navigate('/forecasting'); onOpenChange(false); }}>
            <TrendingUp className="mr-2 h-4 w-4" />
            <span>Price Forecasting</span>
          </CommandItem>
          <CommandItem onSelect={() => { navigate('/fundamental'); onOpenChange(false); }}>
            <Search className="mr-2 h-4 w-4" />
            <span>Fundamental Analysis</span>
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  );
}
