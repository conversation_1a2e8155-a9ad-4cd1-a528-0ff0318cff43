
// Re-export all price history service functionality with proper types
export * from './historyClient';
export * from './technicalIndicators';
export * from './predictions';
export * from './forecastService';
export * from './predictionGenerator';
export * from './dataTransformer';
export * from './trendAnalysis';
export * from './types';

// Explicitly export the main functions to ensure they're available
export { getPriceForecastData, generatePriceForecast } from './forecastService';
export { fetchPriceHistory } from './historyClient';
export { generatePricePredictions, calculateForecastAccuracy } from './predictionGenerator';
export { combineHistoricalAndForecast, transformToCandlestickData } from './dataTransformer';
export { calculateTrend, calculateVolatility, calculateMovingAverage } from './trendAnalysis';
