
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FundamentalCoin } from "@/hooks/useFundamentalAnalysis";
import { 
  DetailHeader, 
  OverviewTab, 
  TokenomicsTab, 
  DevelopmentTab, 
  ChartsTab 
} from "./coinDetail";

interface CoinDetailCardProps {
  coin: FundamentalCoin;
  historicalData: any[];
  metricsData: any | null;
  isLoadingDetails: boolean;
}

export function CoinDetailCard({ 
  coin, 
  historicalData, 
  metricsData, 
  isLoadingDetails 
}: CoinDetailCardProps) {
  if (!coin) return null;
  
  return (
    <Card className="overflow-hidden">
      <CardHeader className="bg-primary/5">
        <DetailHeader coin={coin} />
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="w-full grid grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tokenomics">Tokenomics</TabsTrigger>
            <TabsTrigger value="development">Development</TabsTrigger>
            <TabsTrigger value="charts">Analysis Charts</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview">
            <OverviewTab 
              coin={coin} 
              historicalData={historicalData} 
              isLoadingDetails={isLoadingDetails} 
            />
          </TabsContent>
          
          <TabsContent value="tokenomics">
            <TokenomicsTab coin={coin} />
          </TabsContent>
          
          <TabsContent value="development">
            <DevelopmentTab coin={coin} />
          </TabsContent>
          
          <TabsContent value="charts">
            <ChartsTab 
              metricsData={metricsData} 
              isLoadingDetails={isLoadingDetails} 
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
