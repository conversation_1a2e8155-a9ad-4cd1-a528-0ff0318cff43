
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>itle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { ArrowRight, ArrowRightLeft } from "lucide-react";
import { <PERSON> } from "react-router-dom";

interface OverviewContentProps {
  onContinue: () => void;
}

export default function OverviewContent({ onContinue }: OverviewContentProps) {
  return (
    <div className="space-y-6">
      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <h2 className="text-2xl font-semibold mb-4">Getting Started with Crypto Purchases</h2>
          <p className="mb-4">Buying your first cryptocurrency can seem intimidating, but understanding the basics will help you navigate the process confidently.</p>

          <h3 className="text-xl font-medium mt-6 mb-2">What You'll Learn</h3>
          <ul className="list-disc pl-6 space-y-2">
            <li>The differences between centralized and decentralized exchanges</li>
            <li>How to understand and minimize trading fees</li>
            <li>What slippage is and how to manage it</li>
            <li>Different types of orders when buying crypto</li>
            <li>How to choose the right platform for your needs</li>
          </ul>

          <Button variant="outline" className="mt-6" onClick={onContinue}>
            Continue Learning <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        <div className="rounded-lg overflow-hidden h-[350px]">
          <img
            src="/education/crypto-trading-dashboard.svg"
            alt="Person analyzing cryptocurrency trading dashboard on a laptop"
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <ArrowRightLeft className="mr-2 h-5 w-5 text-primary" />
            Quick Start Guide
          </CardTitle>
          <CardDescription>Follow these steps to make your first crypto purchase</CardDescription>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal pl-6 space-y-3">
            <li>Choose a reputable exchange (CEX) or decentralized platform (DEX)</li>
            <li>Create and secure your account with strong authentication</li>
            <li>Complete any required identity verification (typically for CEX only)</li>
            <li>Connect a payment method (bank account, credit card, etc.)</li>
            <li>Research the cryptocurrency you wish to purchase</li>
            <li>Place your first order using market or limit orders</li>
            <li>Consider moving your purchased crypto to a secure wallet</li>
          </ol>
        </CardContent>
        <CardFooter>
          <Button variant="secondary" asChild className="w-full">
            <Link to="/market-insights">
              Explore Current Market Insights
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
