
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { WhaleMovementData } from "@/hooks/useOnChainAnalytics";
import { TrendingUp, TrendingDown, Wallet, ArrowRight } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface WhaleMovementsSectionProps {
  data: WhaleMovementData;
  isLoading: boolean;
}

export default function WhaleMovementsSection({ data, isLoading }: WhaleMovementsSectionProps) {
  const formatNumber = (value: number) => {
    if (value >= 1000000000) {
      return `${(value / 1000000000).toFixed(2)}B`;
    } else if (value >= 1000000) {
      return `${(value / 1000000).toFixed(2)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };
  
  const formatChange = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };
  
  const formatUsd = (value: number) => {
    return `$${formatNumber(value)}`;
  };
  
  const formatTimeSince = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else {
      const diffHours = Math.floor(diffMins / 60);
      return `${diffHours}h ${diffMins % 60}m ago`;
    }
  };
  
  const shortenAddress = (address: string) => {
    return address;
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">Large Transactions</CardTitle>
              <CardDescription>Last 24 hours</CardDescription>
            </div>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.largeTransactions.count}</div>
            <div className={`text-xs ${data.largeTransactions.change >= 0 ? 'text-green-500' : 'text-red-500'} flex items-center mt-1`}>
              {data.largeTransactions.change >= 0 ? <TrendingUp className="mr-1 h-3 w-3" /> : <TrendingDown className="mr-1 h-3 w-3" />}
              {formatChange(data.largeTransactions.change)} from yesterday
            </div>
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">Transaction Volume</CardTitle>
              <CardDescription>Whale transaction value</CardDescription>
            </div>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatUsd(data.largeTransactions.volume)}</div>
            <div className={`text-xs ${data.largeTransactions.change >= 0 ? 'text-green-500' : 'text-red-500'} flex items-center mt-1`}>
              {data.largeTransactions.change >= 0 ? <TrendingUp className="mr-1 h-3 w-3" /> : <TrendingDown className="mr-1 h-3 w-3" />}
              {formatChange(data.largeTransactions.change)} from yesterday
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Top Tokens Accumulated</CardTitle>
            <CardDescription>Most accumulated tokens by whales</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-[300px] flex items-center justify-center">
                <p className="text-muted-foreground">Loading data...</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Token</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Value (USD)</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.topTokensAccumulated.map((token, index) => (
                    <TableRow key={`${token.symbol}-${index}`}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                            {token.symbol.charAt(0)}
                          </div>
                          <div>
                            <div>{token.token}</div>
                            <div className="text-xs text-muted-foreground">{token.symbol}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatNumber(token.amount)}</TableCell>
                      <TableCell>{formatUsd(token.valueUsd)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Recent Whale Movements</CardTitle>
            <CardDescription>Large transactions in the past 24 hours</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-[300px] flex items-center justify-center">
                <p className="text-muted-foreground">Loading data...</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Transaction</TableHead>
                    <TableHead>Token</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Time</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.recentMovements.map((tx, index) => (
                    <TableRow key={`${tx.id}-${index}`}>
                      <TableCell className="font-medium">
                        <div className="flex items-center text-xs">
                          <span className="truncate max-w-[60px]">{shortenAddress(tx.from)}</span>
                          <ArrowRight className="mx-1 h-3 w-3" />
                          <span className="truncate max-w-[60px]">{shortenAddress(tx.to)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center mr-2 text-xs">
                            {tx.symbol.charAt(0)}
                          </div>
                          <div className="text-xs">{formatNumber(tx.amount)} {tx.symbol}</div>
                        </div>
                      </TableCell>
                      <TableCell>{formatUsd(tx.valueUsd)}</TableCell>
                      <TableCell>{formatTimeSince(tx.timestamp)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
