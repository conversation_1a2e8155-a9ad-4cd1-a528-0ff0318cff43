
// Define proper interfaces for our exchange types
export interface BaseExchange {
  name: string;
  type: 'CEX' | 'DEX';
  tradingFee: number;
  withdrawalFee: number;
  security: string;
  liquidity: string;
  description: string; // Added the description property to the BaseExchange interface
}

export interface CentralizedExchange extends BaseExchange {
  type: 'CEX';
}

export interface DecentralizedExchange extends BaseExchange {
  type: 'DEX';
  gasEstimate: number;
}

export type Exchange = CentralizedExchange | DecentralizedExchange;

export interface FeeCalculationResult {
  tradingFee: number;
  slippageCost: number;
  gasFee: number;
  withdrawalFee: number;
  totalCost: number;
  receivedCrypto: number;
}
