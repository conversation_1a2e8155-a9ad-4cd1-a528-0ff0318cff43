
import { coinGeckoAxios, handleApiError } from "./coinGeckoClient";
import { getCoinMarketData } from "./coinMarketData";

// Search for coins
export const searchCoins = async (query: string) => {
  if (!query || query.trim().length < 2) {
    return { coins: [] };
  }
  
  try {
    const response = await coinGeckoAxios.get("/search", {
      params: {
        query: query.trim()
      }
    });
    
    // Filter to only coins and tokens (not categories or exchanges)
    const coins = response.data.coins || [];
    
    // Limit results to avoid overwhelming the UI
    const limitedResults = coins.slice(0, 15);
    
    // If we have results, fetch their market data
    if (limitedResults.length > 0) {
      const coinIds = limitedResults.map((coin: any) => coin.id);
      const marketData = await getCoinMarketData(coinIds);
      
      // Combine search results with market data
      return {
        coins: limitedResults.map((coin: any) => ({
          ...coin,
          marketData: marketData[coin.id] || null
        }))
      };
    }
    
    return { coins: limitedResults };
  } catch (error) {
    console.error("Search error:", error);
    return handleApiError(error, {
      key: "search_results",
      data: { coins: [] }
    });
  }
};
