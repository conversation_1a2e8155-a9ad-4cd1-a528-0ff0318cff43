
export interface CoinData {
  id: string;
  name: string;
  symbol: string;
  image?: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  total_volume: number;
  price_change_percentage_24h: number;
  opportunity_score: number;
  momentum_score: number;
  market_sentiment: string;
  volatility_rating: string;
  trading_intensity: number;
  risk_level: 'low' | 'medium' | 'high';
}

export interface PriceHistoryPoint {
  date: string;
  price: number;
  volume: number;
  rsi: number;
  macd: number;
  sma20: number;
  sma50: number;
}

export interface FundamentalMetrics {
  tokenomics: { score: number };
  development: { score: number; stars?: string };
  adoption: { score: number };
  community: { score: number };
}

export interface PriceTargets {
  conservative: number;
  optimistic: number;
  bearish: number;
}

export interface TechnicalIndicator {
  name: string;
  value: number;
  signal: string;
}

export interface AnalysisProps {
  coinData: CoinData;
  isLoading: boolean;
}
