
// Process trending data for charts and market analysis
export const processTrendingData = (topCoins: any[]): any[] => {
  return topCoins.slice(0, 10).map((coin, index) => ({
    name: coin.symbol.toUpperCase(),
    momentum: coin.price_change_percentage_24h || 0,
    volume: coin.total_volume || 0,
    rank: index + 1
  }));
};

// Calculate market cap shifts
export const calculateMarketCapShifts = (topCoins: any[]): any[] => {
  return topCoins.slice(0, 10).map(coin => ({
    symbol: coin.symbol.toUpperCase(),
    name: coin.name,
    change: coin.market_cap_change_percentage_24h || 0,
    marketCap: coin.market_cap || 0
  })).sort((a, b) => Math.abs(b.change) - Math.abs(a.change));
};
