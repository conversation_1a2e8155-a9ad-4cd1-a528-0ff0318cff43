
import { SentimentSource } from "@/types/aiInsights";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from "recharts";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid } from "recharts";
import { MessageSquare, TrendingUp, TrendingDown, Newspaper } from "lucide-react";

interface SentimentAnalysisProps {
  sources: SentimentSource[];
  isLoading: boolean;
}

export default function SentimentAnalysis({ sources, isLoading }: SentimentAnalysisProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Sentiment Analysis
          </CardTitle>
          <CardDescription>Analysis of market sentiment from social and news sources</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <Skeleton className="h-[200px] w-full" />
            <Skeleton className="h-[200px] w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }
  
  if (!sources.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Sentiment Analysis
          </CardTitle>
          <CardDescription>Analysis of market sentiment from social and news sources</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-8 text-center text-muted-foreground">
            No sentiment data available for this asset
          </div>
        </CardContent>
      </Card>
    );
  }
  
  // Calculate overall sentiment score (weighted average based on volume)
  const totalVolume = sources.reduce((sum, source) => sum + source.volume, 0);
  const overallSentiment = sources.reduce(
    (sum, source) => sum + (source.sentimentScore * source.volume), 0
  ) / totalVolume;
  
  // Prepare data for pie chart
  const sentimentCounts = sources.reduce((acc, source) => {
    acc[source.sentiment] = (acc[source.sentiment] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const pieData = [
    { name: 'Positive', value: sentimentCounts.positive || 0, color: '#22c55e' },
    { name: 'Neutral', value: sentimentCounts.neutral || 0, color: '#f59e0b' },
    { name: 'Negative', value: sentimentCounts.negative || 0, color: '#ef4444' }
  ].filter(item => item.value > 0);
  
  // Prepare data for bar chart
  const barData = sources.map(source => ({
    name: source.name,
    score: source.sentimentScore,
    change: source.change24h,
    type: source.type
  }));
  
  const getScoreDescription = (score: number) => {
    if (score >= 75) return "Very Positive";
    if (score >= 60) return "Positive";
    if (score >= 40) return "Neutral";
    if (score >= 25) return "Negative";
    return "Very Negative";
  };
  
  const getScoreColor = (score: number) => {
    if (score >= 75) return "text-green-500";
    if (score >= 60) return "text-emerald-500";
    if (score >= 40) return "text-amber-500";
    if (score >= 25) return "text-orange-500";
    return "text-red-500";
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Sentiment Analysis
        </CardTitle>
        <CardDescription>Analysis of market sentiment from social and news sources</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-6">
          <div className="flex-1">
            <div className="mb-4">
              <h3 className="text-lg font-medium">Overall Sentiment</h3>
              <div className="flex items-center mt-2">
                <div className="text-4xl font-bold">
                  {Math.round(overallSentiment)}
                </div>
                <div className="ml-3">
                  <div className={getScoreColor(overallSentiment)}>
                    {getScoreDescription(overallSentiment)}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    Based on {sources.length} sources
                  </div>
                </div>
              </div>
            </div>
            
            <div className="h-[180px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    outerRadius={70}
                    innerRadius={40}
                    dataKey="value"
                    label={({name}) => name}
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value) => [`${value} sources`, 'Count']}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
          
          <div className="flex-1">
            <h3 className="text-lg font-medium mb-4">Sentiment by Source</h3>
            <div className="h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={barData}>
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis dataKey="name" tick={{fontSize: 11}} />
                  <YAxis domain={[0, 100]} />
                  <Tooltip 
                    formatter={(value, name) => [value, name === 'score' ? 'Sentiment Score' : '24h Change']}
                    labelFormatter={(label) => `${label}`}
                  />
                  <Bar 
                    dataKey="score" 
                    name="Sentiment Score"
                    fill="#3b82f6" 
                    radius={[4, 4, 0, 0]}
                    barSize={20}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
            
            <div className="mt-2 grid grid-cols-2 gap-2">
              {sources.map((source) => (
                <div key={source.id} className="flex items-center text-xs">
                  {source.type === 'social_media' ? (
                    <MessageSquare className="h-3 w-3 mr-1 text-blue-500" />
                  ) : (
                    <Newspaper className="h-3 w-3 mr-1 text-purple-500" />
                  )}
                  <span className="mr-1">{source.name}:</span>
                  <span className="font-medium">
                    {source.change24h > 0 ? (
                      <span className="text-green-500 flex items-center">
                        <TrendingUp className="h-3 w-3 inline mr-0.5" />
                        +{source.change24h.toFixed(1)}%
                      </span>
                    ) : source.change24h < 0 ? (
                      <span className="text-red-500 flex items-center">
                        <TrendingDown className="h-3 w-3 inline mr-0.5" />
                        {source.change24h.toFixed(1)}%
                      </span>
                    ) : (
                      <span className="text-muted-foreground">0%</span>
                    )}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
