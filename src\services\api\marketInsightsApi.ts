
import { coinGeckoAxios, handleApiError, cacheResponse } from "./coinGeckoClient";

// Fetch exchanges data
export const fetchExchanges = async () => {
  try {
    const response = await coinGeckoAxios.get("/exchanges", {
      params: {
        per_page: 50,
        page: 1
      }
    });
    
    cacheResponse("exchanges", response.data);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      key: "exchanges",
      data: []
    });
  }
};

// Fetch price history for a specific coin
export const fetchCoinPriceHistory = async (coinId: string, days: number = 30) => {
  try {
    const response = await coinGeckoAxios.get(`/coins/${coinId}/market_chart`, {
      params: {
        vs_currency: 'usd',
        days: days,
        interval: days > 90 ? 'daily' : undefined
      }
    });
    
    cacheResponse(`price_history_${coinId}_${days}`, response.data);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      key: `price_history_${coinId}_${days}`,
      data: { prices: [] }
    });
  }
};

// Generate correlation data between assets
export const generateCorrelationData = async (baseAssetId: string, days: number = 30) => {
  try {
    // Get base asset price history
    const baseAssetData = await fetchCoinPriceHistory(baseAssetId, days);
    
    if (!baseAssetData.prices || baseAssetData.prices.length === 0) {
      return [];
    }
    
    // Get top coins to compare with
    const topCoinsResponse = await coinGeckoAxios.get("/coins/markets", {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: 20,
        page: 1,
        sparkline: false,
      }
    });
    
    const topCoins = topCoinsResponse.data.filter((coin: any) => coin.id !== baseAssetId);
    
    // Calculate base asset percentage changes
    const baseStartPrice = baseAssetData.prices[0][1];
    const baseEndPrice = baseAssetData.prices[baseAssetData.prices.length - 1][1];
    const basePercentChange = ((baseEndPrice - baseStartPrice) / baseStartPrice) * 100;
    
    // Get correlation data for each coin
    const correlationPromises = topCoins.slice(0, 10).map(async (coin: any) => {
      try {
        const comparedData = await fetchCoinPriceHistory(coin.id, days);
        
        if (!comparedData.prices || comparedData.prices.length === 0) {
          return null;
        }
        
        // Calculate compared asset percentage changes
        const comparedStartPrice = comparedData.prices[0][1];
        const comparedEndPrice = comparedData.prices[comparedData.prices.length - 1][1];
        const comparedPercentChange = ((comparedEndPrice - comparedStartPrice) / comparedStartPrice) * 100;
        
        // Calculate correlation coefficient (simplified for demo)
        // In a real app, we'd use a more sophisticated method
        const minLength = Math.min(baseAssetData.prices.length, comparedData.prices.length);
        const baseReturns = [];
        const comparedReturns = [];
        
        // Calculate daily returns for correlation
        for (let i = 1; i < minLength; i++) {
          const baseReturn = (baseAssetData.prices[i][1] - baseAssetData.prices[i-1][1]) / baseAssetData.prices[i-1][1];
          const comparedReturn = (comparedData.prices[i][1] - comparedData.prices[i-1][1]) / comparedData.prices[i-1][1];
          
          baseReturns.push(baseReturn);
          comparedReturns.push(comparedReturn);
        }
        
        // Pearson correlation
        const correlation = calculateCorrelation(baseReturns, comparedReturns);
        
        // Market cap as z-axis for bubble size
        const marketCap = coin.market_cap / 1000000000; // in billions for better scaling
        
        return {
          assetId: coin.id,
          assetName: coin.name,
          assetSymbol: coin.symbol.toUpperCase(),
          baseChange: basePercentChange,
          comparedChange: comparedPercentChange,
          correlation: correlation,
          marketCap: coin.market_cap,
          z: marketCap > 100 ? 100 : marketCap < 1 ? 1 : marketCap // Normalize for bubble size
        };
        
      } catch (error) {
        console.error(`Error getting correlation data for ${coin.id}:`, error);
        return null;
      }
    });
    
    const results = await Promise.all(correlationPromises);
    const filteredResults = results.filter(item => item !== null);
    
    cacheResponse(`correlation_${baseAssetId}_${days}`, filteredResults);
    return filteredResults;
    
  } catch (error) {
    return handleApiError(error, {
      key: `correlation_${baseAssetId}_${days}`,
      data: []
    });
  }
};

// Helper function to calculate Pearson correlation coefficient
function calculateCorrelation(x: number[], y: number[]): number {
  const n = Math.min(x.length, y.length);
  
  if (n === 0) return 0;
  
  let sumX = 0;
  let sumY = 0;
  let sumXY = 0;
  let sumX2 = 0;
  let sumY2 = 0;
  
  for (let i = 0; i < n; i++) {
    sumX += x[i];
    sumY += y[i];
    sumXY += x[i] * y[i];
    sumX2 += x[i] * x[i];
    sumY2 += y[i] * y[i];
  }
  
  const numerator = n * sumXY - sumX * sumY;
  const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
  
  return denominator === 0 ? 0 : numerator / denominator;
}

// Fetch recently added coins
export const fetchRecentlyAdded = async (limit = 10) => {
  try {
    // First attempt: Try to use the created_desc parameter to get the newest coins
    const response = await coinGeckoAxios.get("/coins/markets", {
      params: {
        vs_currency: 'usd',
        order: 'created_desc',  // Sort by recently added
        per_page: limit * 3,    // Get more to filter out established coins
        page: 1,
        sparkline: false
      }
    });
    
    // Filter out established coins immediately
    const stablecoins = ['usdt', 'usdc', 'busd', 'dai', 'tusd', 'usdp', 'usdn'];
    const establishedCoins = [
      "bitcoin", "ethereum", "tether", "ripple", "xrp", "binancecoin", "bnb", 
      "solana", "cardano", "dogecoin", "polygon", "polkadot", "usd-coin", "usdc", "tron"
    ];
    
    // Filter out major coins and stablecoins
    const filteredData = response.data.filter((coin: any) => 
      !establishedCoins.includes(coin.id?.toLowerCase()) && 
      !establishedCoins.includes(coin.symbol?.toLowerCase()) &&
      !stablecoins.includes(coin.symbol?.toLowerCase())
    );
    
    // If we have enough filtered coins, return them
    if (filteredData.length >= 5) {
      console.log("Successfully fetched recently added coins:", filteredData.length);
      cacheResponse("recently_added", filteredData);
      return filteredData;
    }
    
    // If we don't have enough coins, try a second approach with market_cap_asc
    const fallbackResponse = await coinGeckoAxios.get("/coins/markets", {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_asc',  // Smallest market cap first (often newer)
        per_page: limit * 5,      // Get more to filter later
        page: 1,
        sparkline: false
      }
    });
    
    // More aggressive filtering for the fallback approach
    const fallbackFiltered = fallbackResponse.data.filter((coin: any) => {
      // Skip established coins and stablecoins
      if (establishedCoins.includes(coin.id?.toLowerCase()) || 
          establishedCoins.includes(coin.symbol?.toLowerCase()) ||
          stablecoins.includes(coin.symbol?.toLowerCase())) {
        return false;
      }
      
      // Focus on coins with very small market caps (likely newer)
      return coin.market_cap < 5000000; // Less than $5M market cap
    });
    
    // Log what we found
    console.log("Fallback approach for recently added coins found:", fallbackFiltered.length);
    
    cacheResponse("recently_added", fallbackFiltered);
    return fallbackFiltered;
  } catch (error) {
    console.error("Error fetching recently added coins:", error);
    return handleApiError(error, {
      key: "recently_added",
      data: []
    });
  }
};
