
import React from 'react';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Building2, Newspaper } from 'lucide-react';
import { FearGreedIndex } from '@/components/market/charts/FearGreedIndex';
import { MarketOverviewDashboard } from '@/components/market/charts/MarketOverviewDashboard';
import { SectorPerformanceChart } from '@/components/market/charts/SectorPerformanceChart';
import { MarketNewsPanel } from '@/components/market/charts/MarketNewsPanel';
import { LiquidityMetrics } from '@/components/market/charts/LiquidityMetrics';
import MarketTrends from "@/components/market/MarketTrends";
import ExchangesList from "@/components/market/ExchangesList";
import TopCoinsTable from './TopCoinsTable';

interface MarketInsightsTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  hasEnhancedData: boolean;
  metrics: any;
  volatility: any;
  overallMarketHealth: string;
  marketTrend: string;
  riskLevel: string;
  metricsLoading: boolean;
  volatilityLoading: boolean;
  topCoins: any[] | undefined;
  isLoadingCoins: boolean;
  sentiment: any;
  sentimentLoading: boolean;
  liquidity: any;
  liquidityLoading: boolean;
  sectors: any[];
  sectorsLoading: boolean;
  news: any[];
  newsLoading: boolean;
}

export default function MarketInsightsTabs({
  activeTab,
  setActiveTab,
  hasEnhancedData,
  metrics,
  volatility,
  overallMarketHealth,
  marketTrend,
  riskLevel,
  metricsLoading,
  volatilityLoading,
  topCoins,
  isLoadingCoins,
  sentiment,
  sentimentLoading,
  liquidity,
  liquidityLoading,
  sectors,
  sectorsLoading,
  news,
  newsLoading
}: MarketInsightsTabsProps) {
  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-6 lg:w-auto lg:grid-cols-6">
        <TabsTrigger value="overview" className="flex items-center gap-2">
          <BarChart3 className="w-4 h-4" />
          Overview
        </TabsTrigger>
        <TabsTrigger value="sentiment" className="flex items-center gap-2">
          <AlertTriangle className="w-4 h-4" />
          Sentiment
        </TabsTrigger>
        <TabsTrigger value="trending" className="flex items-center gap-2">
          <TrendingUp className="w-4 h-4" />
          Trending
        </TabsTrigger>
        <TabsTrigger value="sectors" className="flex items-center gap-2">
          <PieChart className="w-4 h-4" />
          Sectors
        </TabsTrigger>
        <TabsTrigger value="exchanges" className="flex items-center gap-2">
          <Building2 className="w-4 h-4" />
          Exchanges
        </TabsTrigger>
        <TabsTrigger value="news" className="flex items-center gap-2">
          <Newspaper className="w-4 h-4" />
          News
        </TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-6 mt-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {hasEnhancedData && metrics && volatility ? (
            <div className="lg:col-span-2">
              <MarketOverviewDashboard
                metrics={metrics}
                volatility={volatility}
                marketHealth={overallMarketHealth as "excellent" | "good" | "neutral" | "poor" | "critical"}
                marketTrend={marketTrend as "bullish" | "bearish" | "sideways"}
                riskLevel={riskLevel as "low" | "medium" | "high" | "extreme"}
                loading={metricsLoading || volatilityLoading}
              />
            </div>
          ) : null}

          {topCoins && !isLoadingCoins ? (
            <div className={hasEnhancedData && metrics && volatility ? "lg:col-span-2" : "lg:col-span-2"}>
              <TopCoinsTable coins={topCoins} />
            </div>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center min-h-[400px]">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      <TabsContent value="sentiment" className="space-y-6 mt-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {sentiment ? (
            <FearGreedIndex
              sentiment={sentiment}
              loading={sentimentLoading}
            />
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center min-h-[400px]">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                <span className="ml-2">Loading sentiment data...</span>
              </CardContent>
            </Card>
          )}

          {liquidity ? (
            <LiquidityMetrics
              liquidity={liquidity}
              loading={liquidityLoading}
            />
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center min-h-[400px]">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                <span className="ml-2">Loading liquidity data...</span>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      <TabsContent value="trending" className="mt-6">
        <MarketTrends />
      </TabsContent>

      <TabsContent value="sectors" className="space-y-6 mt-6">
        {sectors && sectors.length > 0 ? (
          <SectorPerformanceChart
            sectors={sectors}
            loading={sectorsLoading}
          />
        ) : (
          <Card>
            <CardContent className="flex items-center justify-center min-h-[400px]">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <span className="ml-2">Loading sector data...</span>
            </CardContent>
          </Card>
        )}
      </TabsContent>

      <TabsContent value="exchanges" className="mt-6">
        <ExchangesList />
      </TabsContent>

      <TabsContent value="news" className="space-y-6 mt-6">
        {news && news.length > 0 ? (
          <MarketNewsPanel
            news={news}
            loading={newsLoading}
          />
        ) : (
          <Card>
            <CardContent className="flex items-center justify-center min-h-[400px]">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <span className="ml-2">Loading news data...</span>
            </CardContent>
          </Card>
        )}
      </TabsContent>
    </Tabs>
  );
}
