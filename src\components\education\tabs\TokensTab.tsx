
import { <PERSON> } from "react-router-dom";
import { TokenDeepDive } from "@/hooks/useEducation.tsx";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface TokensTabProps {
  tokens: TokenDeepDive[];
  loading: boolean;
}

export default function TokensTab({ tokens, loading }: TokensTabProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <div className="rounded-full h-10 w-10 bg-muted"></div>
                <div>
                  <div className="h-5 w-24 bg-muted rounded mb-1"></div>
                  <div className="h-3 w-12 bg-muted rounded"></div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-24 bg-muted rounded"></div>
            </CardContent>
            <CardFooter>
              <div className="h-9 w-full bg-muted rounded"></div>
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      {tokens.map((token) => (
        <Card key={token.id} className="overflow-hidden hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center overflow-hidden">
                {token.logoUrl ? (
                  <img
                    src={token.logoUrl}
                    alt={token.name}
                    className="h-10 w-10 object-contain"
                  />
                ) : (
                  <span className="text-xl font-bold">{token.symbol?.charAt(0)}</span>
                )}
              </div>
              <div>
                <h3 className="font-medium text-lg">{token.name}</h3>
                <p className="text-sm text-muted-foreground">{token.symbol}</p>
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-2">
            <p className="text-sm line-clamp-3">{token.description}</p>
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline">{token.category}</Badge>
              <Badge variant="secondary">Founded {token.foundedIn}</Badge>
            </div>
          </CardContent>

          <CardFooter>
            <Link to={`/education/token/${token.id}`} className="w-full">
              <Button variant="outline" className="w-full">
                Explore Token <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
