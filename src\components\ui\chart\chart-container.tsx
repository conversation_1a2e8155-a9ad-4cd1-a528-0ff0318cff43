
import * as React from "react";
import { ResponsiveContainer } from "recharts";
import { ChartConfig } from "./types";

export interface ChartContainerProps
  extends React.ComponentProps<typeof ResponsiveContainer> {
  config: ChartConfig;
  children: React.ComponentProps<typeof ResponsiveContainer>["children"];
}

const ChartContainer = React.forwardRef<
  React.ElementRef<typeof ResponsiveContainer>,
  ChartContainerProps
>(({ config, children, className, ...props }, ref) => {
  return (
    <div className="chart-container">
      <style
        dangerouslySetInnerHTML={{
          __html: Object.entries(config)
            .map(
              ([key, value]) => `
              .chart-${key} {
                color: ${value.color || value.theme?.light || '#000'};
              }
            `
            )
            .join("\n"),
        }}
      />
      <ResponsiveContainer ref={ref} className={className} {...props}>
        {children}
      </ResponsiveContainer>
    </div>
  );
});

ChartContainer.displayName = "ChartContainer";

export { ChartContainer };
