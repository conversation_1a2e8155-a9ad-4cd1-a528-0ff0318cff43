
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Settings, Save, RefreshCw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface APIConfig {
  id: string;
  provider: string;
  rate_limit_per_minute: number;
  cache_duration_minutes: number;
  is_enabled: boolean;
  fallback_cache_enabled: boolean;
  updated_at: string;
}

export function APIConfigurationPanel() {
  const queryClient = useQueryClient();
  const [editingConfig, setEditingConfig] = useState<APIConfig | null>(null);

  // Get API configurations
  const { data: apiConfigs, isLoading } = useQuery({
    queryKey: ['apiConfigs'],
    queryFn: async () => {
      const { data } = await supabase
        .from('api_configurations')
        .select('*')
        .order('provider');
      return data || [];
    },
  });

  // Update API configuration
  const updateConfigMutation = useMutation({
    mutationFn: async (config: APIConfig) => {
      const { error } = await supabase
        .from('api_configurations')
        .update({
          rate_limit_per_minute: config.rate_limit_per_minute,
          cache_duration_minutes: config.cache_duration_minutes,
          is_enabled: config.is_enabled,
          fallback_cache_enabled: config.fallback_cache_enabled,
          updated_at: new Date().toISOString(),
        })
        .eq('id', config.id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['apiConfigs'] });
      setEditingConfig(null);
      toast({
        title: 'Configuration updated',
        description: 'API configuration has been successfully updated.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to update API configuration.',
        variant: 'destructive',
      });
    },
  });

  const handleConfigUpdate = (updates: Partial<APIConfig>) => {
    if (editingConfig) {
      setEditingConfig({ ...editingConfig, ...updates });
    }
  };

  const ConfigEditDialog = ({ config }: { config: APIConfig }) => (
    <Dialog>
      <DialogTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm"
          onClick={() => setEditingConfig(config)}
        >
          <Settings className="w-4 h-4" />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit API Configuration - {config.provider}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="rateLimit">Rate Limit (per minute)</Label>
              <Input
                id="rateLimit"
                type="number"
                value={editingConfig?.rate_limit_per_minute || config.rate_limit_per_minute}
                onChange={(e) => handleConfigUpdate({ 
                  rate_limit_per_minute: parseInt(e.target.value) 
                })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="cacheDuration">Cache Duration (minutes)</Label>
              <Input
                id="cacheDuration"
                type="number"
                value={editingConfig?.cache_duration_minutes || config.cache_duration_minutes}
                onChange={(e) => handleConfigUpdate({ 
                  cache_duration_minutes: parseInt(e.target.value) 
                })}
              />
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="enabled">API Enabled</Label>
            <Switch
              id="enabled"
              checked={editingConfig?.is_enabled ?? config.is_enabled}
              onCheckedChange={(checked) => handleConfigUpdate({ is_enabled: checked })}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="fallbackCache">Fallback Cache Enabled</Label>
            <Switch
              id="fallbackCache"
              checked={editingConfig?.fallback_cache_enabled ?? config.fallback_cache_enabled}
              onCheckedChange={(checked) => handleConfigUpdate({ fallback_cache_enabled: checked })}
            />
          </div>
          
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setEditingConfig(null)}>
              Cancel
            </Button>
            <Button 
              onClick={() => editingConfig && updateConfigMutation.mutate(editingConfig)}
              disabled={updateConfigMutation.isPending}
            >
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="space-y-6">
      {/* API Configuration Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              API Provider Configuration
            </CardTitle>
            <Button 
              onClick={() => queryClient.invalidateQueries({ queryKey: ['apiConfigs'] })}
              variant="outline" 
              size="sm"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Provider</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Rate Limit</TableHead>
                <TableHead>Cache Duration</TableHead>
                <TableHead>Fallback Cache</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {apiConfigs?.map((config) => (
                <TableRow key={config.id}>
                  <TableCell className="font-medium">{config.provider}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div 
                        className={`w-2 h-2 rounded-full ${
                          config.is_enabled ? 'bg-green-500' : 'bg-red-500'
                        }`} 
                      />
                      {config.is_enabled ? 'Enabled' : 'Disabled'}
                    </div>
                  </TableCell>
                  <TableCell>{config.rate_limit_per_minute}/min</TableCell>
                  <TableCell>{config.cache_duration_minutes}m</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div 
                        className={`w-2 h-2 rounded-full ${
                          config.fallback_cache_enabled ? 'bg-green-500' : 'bg-gray-400'
                        }`} 
                      />
                      {config.fallback_cache_enabled ? 'Yes' : 'No'}
                    </div>
                  </TableCell>
                  <TableCell className="text-xs">
                    {new Date(config.updated_at).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <ConfigEditDialog config={config} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Configuration Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle>Configuration Guidelines</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">Rate Limiting</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• CoinGecko: 30 requests/minute (free tier)</li>
                <li>• DeFiLlama: 60 requests/minute</li>
                <li>• News APIs: 100 requests/minute</li>
                <li>• Lower limits reduce risk of hitting API quotas</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Cache Duration</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Real-time data: 1-2 minutes</li>
                <li>• Market data: 5-10 minutes</li>
                <li>• News data: 30-60 minutes</li>
                <li>• Static data: 24+ hours</li>
              </ul>
            </div>
          </div>
          <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> Changes to rate limits and cache durations take effect immediately. 
              Fallback cache helps maintain app functionality when APIs are unavailable.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
