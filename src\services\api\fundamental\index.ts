
// Fundamental analysis service exports
export * from './coinAnalysisService';
export * from './tokenomicsService';
export * from './developerService';
export * from './onChainService';
export * from './scoringService';
export * from './metricsService';
export * from './historicalService';
export * from './types';

// Main analysis functions - export the actual functions that exist
export {
  fetchFundamentalAnalysis,
  fetchFundamentalAnalysis as analyzeFundamentals,
  getTopFundamentalCoins,
  calculateFundamentalScore
} from './coinAnalysisService';

export {
  fetchTokenomicsData,
  fetchTokenomicsData as analyzeTokenDistribution,
  fetchTokenomicsData as getTokenomicsData
} from './tokenomicsService';

export {
  fetchDevelopmentActivity,
  fetchDevelopmentActivity as getDeveloperActivity,
  fetchDevelopmentActivity as analyzeGithubActivity
} from './developerService';

export {
  fetchOnChainMetrics,
  fetchOnChainMetrics as getNetworkMetrics
} from './onChainService';

export {
  calculateFundamentalScore as calculateOverallScore,
  calculateFundamentalScore as getScoreBreakdown
} from './scoringService';
