
import { AlertRule } from "@/hooks/useNewsSentiment";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Bell, Trash2 } from "lucide-react";
import { getConditionText } from "./alertUtils";

interface AlertCardProps {
  rule: AlertRule;
  onToggle: (id: string) => void;
  onDelete: (id: string) => void;
}

export default function AlertCard({ rule, onToggle, onDelete }: AlertCardProps) {
  return (
    <Card key={rule.id} className={`border-l-4 ${rule.active ? 'border-l-primary' : 'border-l-muted'}`}>
      <CardContent className="p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex items-center">
            <Bell className={`h-5 w-5 mr-3 ${rule.active ? 'text-primary' : 'text-muted-foreground'}`} />
            <div>
              <h3 className="font-medium">{rule.name}</h3>
              <p className="text-sm text-muted-foreground">
                {rule.asset} • {getConditionText(rule)} • {rule.timeframe}
              </p>
            </div>
          </div>
          <div className="flex items-center mt-4 md:mt-0">
            <div className="flex gap-2 mr-4">
              {rule.notifications.includes('email') && (
                <span className="text-xs bg-secondary px-2 py-1 rounded">Email</span>
              )}
              {rule.notifications.includes('push') && (
                <span className="text-xs bg-secondary px-2 py-1 rounded">Push</span>
              )}
              {rule.notifications.includes('in_app') && (
                <span className="text-xs bg-secondary px-2 py-1 rounded">In-app</span>
              )}
            </div>
            <div className="flex items-center">
              <Switch
                checked={rule.active}
                onCheckedChange={() => onToggle(rule.id)}
                className="mr-4"
              />
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onDelete(rule.id)}
                className="h-8 w-8"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
