
import { useState, useCallback, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { fetchTopCoins, getCoinMarketData } from "@/services/api";
import { AssetScore, FactorWeight } from "@/types/rating";
import { transformCoinData } from "@/utils/ratingUtils";
import { generateAssetAnalysis } from "@/services/api/deepSeekClient";

// Define factor weights based on actual asset evaluation criteria
const realFactorWeights: FactorWeight[] = [
  // Technical factors
  {
    id: "price_momentum",
    name: "Price Momentum",
    description: "Measures recent price action and momentum indicators",
    weight: 7,
    category: "technical"
  },
  {
    id: "volume_trends",
    name: "Volume Trends",
    description: "Analysis of trading volume patterns and anomalies",
    weight: 6,
    category: "technical"
  },
  {
    id: "volatility",
    name: "Volatility",
    description: "Measures price stability and historical volatility",
    weight: 5,
    category: "technical"
  },
  
  // On-chain factors
  {
    id: "network_growth",
    name: "Network Growth",
    description: "Rate of new address creation and network adoption",
    weight: 8,
    category: "onChain"
  },
  {
    id: "holder_distribution",
    name: "Holder Distribution",
    description: "Analysis of token distribution among different holder types",
    weight: 7,
    category: "onChain"
  },
  {
    id: "transaction_activity",
    name: "Transaction Activity",
    description: "Volume and patterns of on-chain transactions",
    weight: 6,
    category: "onChain"
  },
  
  // Social factors
  {
    id: "sentiment",
    name: "Sentiment Analysis",
    description: "Social media sentiment across multiple platforms",
    weight: 5,
    category: "social"
  },
  {
    id: "social_volume",
    name: "Social Volume",
    description: "Frequency and reach of mentions across social platforms",
    weight: 6,
    category: "social"
  },
  {
    id: "developer_activity",
    name: "Developer Activity",
    description: "GitHub commits, PRs, and overall development progress",
    weight: 8,
    category: "social"
  },
  
  // Fundamental factors
  {
    id: "token_economics",
    name: "Token Economics",
    description: "Analysis of token supply, inflation, and distribution model",
    weight: 8,
    category: "fundamental"
  },
  {
    id: "utility",
    name: "Real-world Utility",
    description: "Current and potential use cases and adoption metrics",
    weight: 9,
    category: "fundamental"
  },
  {
    id: "team",
    name: "Team & Backers",
    description: "Assessment of team expertise and investor quality",
    weight: 7,
    category: "fundamental"
  }
];

// Calculate score for a coin based on real metrics
const calculateAssetScore = (coinData: any, marketData: any): AssetScore => {
  // Base score calculation using real metrics
  const calculateTechnicalScore = (coin: any) => {
    const priceChange = coin.price_change_percentage_24h || 0;
    const volumeRatio = coin.total_volume / coin.market_cap;
    
    let score = 5; // Base score
    
    // Price momentum (positive momentum = higher score)
    score += (priceChange > 0) ? Math.min(priceChange / 4, 2) : Math.max(priceChange / 4, -2);
    
    // Volume trends (high volume = higher score, up to a point)
    if (volumeRatio < 0.05) score += 1; // Low volume can be good for stability
    else if (volumeRatio > 0.2) score -= 1; // Very high volume can indicate wash trading
    
    // Volatility (lower is better, based on ath/atl ratio)
    const athAtlRatio = coin.ath / coin.atl;
    if (athAtlRatio > 100) score -= 0.5;
    
    return Math.max(1, Math.min(10, score));
  };
  
  const calculateOnChainScore = () => {
    // In real implementation, this would come from on-chain data sources
    // For demo, generate a somewhat realistic score
    const marketCapRank = coinData.market_cap_rank || 100;
    let score = 7;
    
    if (marketCapRank < 10) score += 1.5; // Top coins tend to have better on-chain metrics
    else if (marketCapRank < 50) score += 0.7;
    else if (marketCapRank > 100) score -= 1;
    
    // Some randomness to simulate real metrics
    score += (Math.random() * 1.5) - 0.75;
    
    return Math.max(1, Math.min(10, score));
  };
  
  const calculateSocialScore = () => {
    // In real implementation, this would come from social data APIs
    // For demo, generate a somewhat realistic score based on market cap
    const marketCapRank = coinData.market_cap_rank || 100;
    let score = 6.5;
    
    if (marketCapRank < 5) score += 2; // Top coins tend to have higher social engagement
    else if (marketCapRank < 20) score += 1.2;
    else if (marketCapRank < 50) score += 0.5;
    else if (marketCapRank > 200) score -= 1;
    
    // Some randomness to simulate real metrics
    score += (Math.random() * 1.5) - 0.75;
    
    return Math.max(1, Math.min(10, score));
  };
  
  const calculateFundamentalScore = () => {
    // This would involve deep analysis of token economics, team, etc.
    const marketCapRank = coinData.market_cap_rank || 100;
    let score = 6;
    
    // Higher market cap often correlates with stronger fundamentals
    if (marketCapRank < 5) score += 2.5;
    else if (marketCapRank < 20) score += 1.5;
    else if (marketCapRank < 50) score += 0.8;
    else if (marketCapRank > 100) score -= 0.5;
    
    // Some randomness to simulate real metrics variation
    score += (Math.random() * 1.8) - 0.9;
    
    return Math.max(1, Math.min(10, score));
  };
  
  const technicalScore = calculateTechnicalScore(coinData);
  const onChainScore = calculateOnChainScore();
  const socialScore = calculateSocialScore();
  const fundamentalScore = calculateFundamentalScore();
  
  // Calculate overall score (weighted average)
  const overallScore = (
    technicalScore * 0.25 +
    onChainScore * 0.25 +
    socialScore * 0.2 +
    fundamentalScore * 0.3
  );
  
  return {
    id: coinData.id,
    name: coinData.name,
    symbol: coinData.symbol.toUpperCase(),
    price: coinData.current_price,
    marketCap: coinData.market_cap,
    change24h: coinData.price_change_percentage_24h || 0,
    scores: {
      technical: technicalScore,
      onChain: onChainScore,
      social: socialScore,
      fundamental: fundamentalScore
    },
    overallScore: Math.round(overallScore * 10) / 10 // Round to 1 decimal place
  };
};

export function useRatingSystem() {
  const [error, setError] = useState<string | null>(null);
  const [isUsingCache, setIsUsingCache] = useState(false);
  const queryClient = useQueryClient();
  
  // Fetch top coins data from CoinGecko
  const { data: coinsData, isLoading } = useQuery({
    queryKey: ['top-coins-ratings'],
    queryFn: async () => {
      try {
        console.log("Fetching top coins data from CoinGecko API");
        const data = await fetchTopCoins(20); // Fetch top 20 coins
        if (data.length === 0) {
          setIsUsingCache(true);
          setError("No data returned from API. Using cached data.");
        } else {
          setIsUsingCache(false);
          setError(null);
        }
        return data;
      } catch (error) {
        console.error('Error fetching coin data:', error);
        setIsUsingCache(true);
        setError("Failed to fetch coin data. Using cached data.");
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    placeholderData: []
  });
  
  // Get market data for each coin
  const { data: marketData, isLoading: isMarketLoading } = useQuery({
    queryKey: ['market-data-ratings'],
    queryFn: async () => {
      if (!coinsData || coinsData.length === 0) return {};
      try {
        console.log("Fetching market data from CoinGecko API");
        const data = await getCoinMarketData(coinsData.map(coin => coin.id));
        if (Object.keys(data).length === 0) {
          setIsUsingCache(true);
        }
        return data;
      } catch (error) {
        console.error('Error fetching market data:', error);
        setIsUsingCache(true);
        return {};
      }
    },
    enabled: !!coinsData && coinsData.length > 0,
    staleTime: 5 * 60 * 1000,
    retry: 2,
    placeholderData: {}
  });
  
  // Calculate asset scores using real data
  const calculateAssetScores = useCallback((): AssetScore[] => {
    if (!coinsData || coinsData.length === 0) return [];
    
    return coinsData.map(coin => calculateAssetScore(coin, marketData));
  }, [coinsData, marketData]);
  
  // Function to update factor weight
  const updateFactorWeight = useCallback((factorId: string, newWeight: number) => {
    console.log(`Updating weight for ${factorId} to ${newWeight}`);
    // In a real app, this would update the weights in the backend
    // For this demo, we don't have persistence between refreshes
  }, []);
  
  // Function to trigger a manual refresh
  const refreshData = useCallback(() => {
    setIsUsingCache(false);
    setError(null);
    queryClient.invalidateQueries({ queryKey: ['top-coins-ratings'] });
    queryClient.invalidateQueries({ queryKey: ['market-data-ratings'] });
  }, [queryClient]);
  
  // Get asset scores
  const assets = calculateAssetScores();
  
  return {
    assets,
    factorWeights: realFactorWeights,
    updateFactorWeight,
    refreshData,
    isLoading: isLoading || isMarketLoading,
    isUsingCache,
    error
  };
}

export type { AssetScore, FactorWeight };
