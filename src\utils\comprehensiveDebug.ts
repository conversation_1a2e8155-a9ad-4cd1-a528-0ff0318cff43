
/**
 * Comprehensive debugging utility for API Provider Management
 */

import { supabase } from '@/integrations/supabase/client';

/**
 * Complete system diagnostic
 */
export async function runCompleteSystemDiagnostic(): Promise<void> {
  console.log('🔍 RUNNING COMPLETE SYSTEM DIAGNOSTIC...');
  console.log('='.repeat(50));

  try {
    // 1. Check database connection
    console.log('1️⃣ Testing database connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Database connection failed:', connectionError);
      return;
    }
    console.log('✅ Database connection successful');

    // 2. Check table existence
    console.log('\n2️⃣ Checking table existence...');
    const requiredTables = ['api_providers', 'api_costs', 'api_usage_logs', 'ai_prompt_templates'] as const;
    const tableResults: Record<string, boolean> = {};

    for (const table of requiredTables) {
      try {
        const { error } = await supabase.from(table).select('count').limit(1);
        tableResults[table] = !error;
        console.log(`${!error ? '✅' : '❌'} Table "${table}": ${!error ? 'EXISTS' : 'MISSING'}`);
        if (error) console.log(`   Error: ${error.message}`);
      } catch (e) {
        tableResults[table] = false;
        console.log(`❌ Table "${table}": MISSING`);
      }
    }

    const allTablesExist = Object.values(tableResults).every(exists => exists);
    
    if (!allTablesExist) {
      console.log('\n❌ MISSING TABLES DETECTED!');
      console.log('🔧 Solution: Run the migration script in Supabase SQL Editor');
      console.log('📋 Script location: database/migrations/001_create_api_providers.sql');
      return;
    }

    // 3. Check provider data
    console.log('\n3️⃣ Checking provider data...');
    const { data: providers, error: providerError } = await supabase
      .from('api_providers')
      .select('*');

    if (providerError) {
      console.error('❌ Provider query failed:', providerError);
      return;
    }

    console.log(`📊 Providers found: ${providers?.length || 0}`);
    providers?.forEach(p => {
      console.log(`   - ${p.name} (${p.type}) - Active: ${p.is_active} - ID: ${p.id}`);
    });

    if (!providers || providers.length === 0) {
      console.log('❌ No providers found! Running sample data generation...');
      await generateSampleData();
      return;
    }

    // 4. Check usage data
    console.log('\n4️⃣ Checking usage data...');
    const { data: costs, error: costError } = await supabase
      .from('api_costs')
      .select('*');

    console.log(`💰 Cost records: ${costs?.length || 0}`);
    if (costError) console.error('Cost error:', costError);

    const { data: logs, error: logError } = await supabase
      .from('api_usage_logs')
      .select('*')
      .limit(10);

    console.log(`📝 Usage logs: ${logs?.length || 0}`);
    if (logError) console.error('Log error:', logError);

    // 5. Check today's data
    console.log('\n5️⃣ Checking today\'s data...');
    const today = new Date().toISOString().split('T')[0];
    const { data: todayCosts } = await supabase
      .from('api_costs')
      .select('*')
      .eq('date', today);

    console.log(`📅 Today's cost records (${today}): ${todayCosts?.length || 0}`);
    
    if (!todayCosts || todayCosts.length === 0) {
      console.log('❌ No data for today! Generating sample data...');
      await generateSampleData();
      return;
    }

    // 6. Test API service
    console.log('\n6️⃣ Testing API service...');
    try {
      const { getProviderStatsWithUsage } = await import('@/services/api/providers/integratedApiProviderService');
      const stats = await getProviderStatsWithUsage();
      console.log(`📊 API service returned ${stats.length} provider stats`);
      stats.forEach(stat => {
        console.log(`   - ${stat.name}: ${stat.requestsToday} requests today, $${stat.estimatedCost.toFixed(4)} cost`);
      });
    } catch (error) {
      console.error('❌ API service test failed:', error);
    }

    console.log('\n✅ DIAGNOSTIC COMPLETE');
    console.log('='.repeat(50));

  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  }
}

/**
 * Generate sample data for testing
 */
async function generateSampleData(): Promise<void> {
  console.log('🔄 Generating sample data...');
  
  try {
    // Import and run the sample data generation
    const { generateRealisticSampleData } = await import('@/utils/databaseVerification');
    const success = await generateRealisticSampleData();
    
    if (success) {
      console.log('✅ Sample data generated successfully!');
      console.log('🔄 Please refresh your admin dashboard to see the data');
    } else {
      console.log('❌ Sample data generation failed');
    }
  } catch (error) {
    console.error('❌ Sample data generation error:', error);
  }
}

/**
 * Quick fix for common issues
 */
export async function quickFixAllIssues(): Promise<void> {
  console.log('🔧 RUNNING QUICK FIX FOR ALL ISSUES...');
  
  try {
    // Run diagnostic first
    await runCompleteSystemDiagnostic();
    
    // Generate sample data
    await generateSampleData();
    
    console.log('✅ Quick fix completed!');
    console.log('🔄 Please refresh your admin dashboard');
    
  } catch (error) {
    console.error('❌ Quick fix failed:', error);
  }
}

// Export for browser console
if (typeof window !== 'undefined') {
  (window as any).comprehensiveDebug = {
    runCompleteSystemDiagnostic,
    quickFixAllIssues,
    generateSampleData,
  };
  
  console.log('🔧 Comprehensive Debug Tools Available:');
  console.log('- window.comprehensiveDebug.runCompleteSystemDiagnostic() - Complete system check');
  console.log('- window.comprehensiveDebug.quickFixAllIssues() - Auto-fix all issues');
  console.log('- window.comprehensiveDebug.generateSampleData() - Generate sample data');
}
