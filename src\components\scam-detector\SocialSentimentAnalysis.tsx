
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { MessageCircle, Users, Heart, AlertTriangle, TrendingUp } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from "recharts";

interface SocialData {
  telegramMembers: number;
  discordMembers: number;
  twitterFollowers: number;
  sentimentScore: number;
  suspiciousActivity: string[];
}

interface SocialSentimentAnalysisProps {
  socialData: SocialData | null;
  isLoading: boolean;
}

export default function SocialSentimentAnalysis({ socialData, isLoading }: SocialSentimentAnalysisProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-64 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  if (!socialData) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-muted-foreground">No social data available</p>
        </CardContent>
      </Card>
    );
  }

  // Mock sentiment trend data
  const sentimentTrend = [
    { time: "7d", sentiment: socialData.sentimentScore - 15, mentions: 120 },
    { time: "6d", sentiment: socialData.sentimentScore - 10, mentions: 180 },
    { time: "5d", sentiment: socialData.sentimentScore - 5, mentions: 240 },
    { time: "4d", sentiment: socialData.sentimentScore, mentions: 300 },
    { time: "3d", sentiment: socialData.sentimentScore + 5, mentions: 280 },
    { time: "2d", sentiment: socialData.sentimentScore + 3, mentions: 220 },
    { time: "1d", sentiment: socialData.sentimentScore, mentions: 190 }
  ];

  const platformData = [
    { platform: "Telegram", members: socialData.telegramMembers, engagement: Math.random() * 30 + 10 },
    { platform: "Discord", members: socialData.discordMembers, engagement: Math.random() * 25 + 15 },
    { platform: "Twitter", members: socialData.twitterFollowers, engagement: Math.random() * 20 + 5 }
  ];

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getSentimentLevel = (score: number) => {
    if (score >= 70) return { level: "Positive", color: "default", bgColor: "bg-green-50 border-green-200" };
    if (score >= 40) return { level: "Neutral", color: "secondary", bgColor: "bg-yellow-50 border-yellow-200" };
    return { level: "Negative", color: "destructive", bgColor: "bg-red-50 border-red-200" };
  };

  const sentimentLevel = getSentimentLevel(socialData.sentimentScore);

  return (
    <div className="space-y-6">
      {/* Social Media Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <MessageCircle size={16} className="text-blue-500" />
              Telegram
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(socialData.telegramMembers)}</div>
            <p className="text-xs text-muted-foreground">Members</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users size={16} className="text-indigo-500" />
              Discord
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(socialData.discordMembers)}</div>
            <p className="text-xs text-muted-foreground">Members</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Heart size={16} className="text-pink-500" />
              Twitter
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(socialData.twitterFollowers)}</div>
            <p className="text-xs text-muted-foreground">Followers</p>
          </CardContent>
        </Card>

        <Card className={sentimentLevel.bgColor}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp size={16} className="text-green-500" />
              Sentiment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{socialData.sentimentScore.toFixed(1)}%</div>
            <Badge variant={sentimentLevel.color as any}>
              {sentimentLevel.level}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Sentiment Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-500" />
            Sentiment Trend Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Sentiment Chart */}
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={sentimentTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip 
                    formatter={(value) => [`${value}%`, "Sentiment Score"]}
                    labelFormatter={(label) => `${label} ago`}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="sentiment" 
                    stroke="#10b981" 
                    strokeWidth={2}
                    dot={{ fill: "#10b981", strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Sentiment Breakdown */}
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Overall Sentiment</label>
                <div className="mt-2">
                  <Progress value={socialData.sentimentScore} className="h-3" />
                  <span className="text-sm text-muted-foreground">{socialData.sentimentScore.toFixed(1)}/100</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 rounded-lg bg-green-50 border border-green-200">
                  <div className="text-lg font-bold text-green-700">
                    {Math.floor(Math.random() * 40 + 30)}%
                  </div>
                  <p className="text-xs text-green-600">Positive mentions</p>
                </div>
                <div className="p-3 rounded-lg bg-red-50 border border-red-200">
                  <div className="text-lg font-bold text-red-700">
                    {Math.floor(Math.random() * 25 + 10)}%
                  </div>
                  <p className="text-xs text-red-600">Negative mentions</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Platform Engagement */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Engagement Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={platformData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="platform" />
                <YAxis yAxisId="left" tickFormatter={formatNumber} />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip 
                  formatter={(value, name) => [
                    name === "members" ? formatNumber(value as number) : `${(value as number).toFixed(1)}%`,
                    name === "members" ? "Members" : "Engagement Rate"
                  ]}
                />
                <Bar yAxisId="left" dataKey="members" fill="#3b82f6" />
                <Bar yAxisId="right" dataKey="engagement" fill="#10b981" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Suspicious Activity */}
      {socialData.suspiciousActivity.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Suspicious Social Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {socialData.suspiciousActivity.map((activity, index) => (
                <div key={index} className="p-3 rounded-lg bg-red-50 border border-red-200">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <span className="text-sm font-medium">{activity}</span>
                    <Badge variant="destructive">High Risk</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Social Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5 text-blue-500" />
            AI Social Analysis Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">Community Health Indicators</h4>
              <div className="space-y-3">
                {[
                  { 
                    metric: "Organic Growth", 
                    score: socialData.telegramMembers > 10000 ? 85 : 45,
                    description: "Natural member acquisition rate"
                  },
                  { 
                    metric: "Engagement Quality", 
                    score: socialData.sentimentScore,
                    description: "Meaningful conversation vs spam"
                  },
                  { 
                    metric: "Bot Detection", 
                    score: socialData.suspiciousActivity.length === 0 ? 90 : 30,
                    description: "Automated account identification"
                  }
                ].map((indicator, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">{indicator.metric}</span>
                      <span className="text-muted-foreground">{indicator.score}/100</span>
                    </div>
                    <Progress value={indicator.score} className="h-2" />
                    <p className="text-xs text-muted-foreground">{indicator.description}</p>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-3">Risk Factors</h4>
              <div className="space-y-2">
                {[
                  {
                    factor: "Sudden member spikes",
                    detected: Math.random() > 0.6,
                    risk: "Medium"
                  },
                  {
                    factor: "Coordinated messaging",
                    detected: socialData.suspiciousActivity.includes("Coordinated posting patterns"),
                    risk: "High"
                  },
                  {
                    factor: "Censorship of criticism",
                    detected: socialData.suspiciousActivity.includes("Deleted negative comments"),
                    risk: "High"
                  },
                  {
                    factor: "Bot-like behavior",
                    detected: socialData.suspiciousActivity.includes("Bot-like engagement"),
                    risk: "Medium"
                  }
                ].map((factor, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded bg-muted/50">
                    <span className="text-sm">{factor.factor}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant={factor.detected ? (factor.risk === "High" ? "destructive" : "secondary") : "outline"}>
                        {factor.detected ? "Detected" : "Clear"}
                      </Badge>
                      {factor.detected && (
                        <Badge variant="outline" className="text-xs">
                          {factor.risk}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
