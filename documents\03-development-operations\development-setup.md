# 🔧 Development Setup

## Prerequisites

### **System Requirements**
- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 9.0.0 or higher (or yarn/pnpm equivalent)
- **Git**: Latest version for version control
- **VS Code**: Recommended IDE with extensions

### **Recommended VS Code Extensions**
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

## 🚀 Quick Start

### **1. Clone Repository**
```bash
git clone https://github.com/your-username/cryptovisionpro.git
cd cryptovisionpro
```

### **2. Install Dependencies**
```bash
# Using npm
npm install

# Using yarn
yarn install

# Using pnpm
pnpm install
```

### **3. Environment Configuration**
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### **4. Start Development Server**
```bash
# Using npm
npm run dev

# Using yarn
yarn dev

# Using pnpm
pnpm dev
```

The application will be available at `http://localhost:8081`

## 🔐 Environment Variables

### **Required Variables**
```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# CoinGecko API (Optional but recommended)
VITE_COINGECKO_API_KEY=your_coingecko_api_key

# DeepSeek AI API (Optional)
VITE_DEEPSEEK_API_KEY=your_deepseek_api_key

# Etherscan API (Optional)
VITE_ETHERSCAN_API_KEY=your_etherscan_api_key
```

### **Optional Variables**
```env
# Development Configuration
VITE_APP_ENV=development
VITE_DEBUG_MODE=true

# Analytics (Production)
VITE_GOOGLE_ANALYTICS_ID=your_ga_id
VITE_MIXPANEL_TOKEN=your_mixpanel_token

# Error Tracking (Production)
VITE_SENTRY_DSN=your_sentry_dsn
```

### **API Key Setup Instructions**

#### **Supabase Setup** (Required)
1. Visit [supabase.com](https://supabase.com)
2. Create a new project
3. Go to Settings → API
4. Copy the Project URL and anon public key
5. Add to `.env` file

#### **CoinGecko API** (Recommended)
1. Visit [coingecko.com/api](https://www.coingecko.com/en/api)
2. Sign up for a free account
3. Generate API key in dashboard
4. Add to `.env` file

#### **DeepSeek AI** (Optional)
1. Visit [deepseek.com](https://www.deepseek.com)
2. Create account and get API key
3. Add to `.env` file

## 📁 Project Structure

### **Development Workflow**
```
src/
├── 📂 pages/                    # Route components
├── 📂 components/               # Reusable UI components
├── 📂 hooks/                    # Custom React hooks
├── 📂 services/                 # API services and utilities
├── 📂 contexts/                 # React contexts
├── 📂 lib/                      # Utility libraries
├── 📂 types/                    # TypeScript type definitions
└── 📂 utils/                    # Helper functions
```

### **Key Configuration Files**
```
├── vite.config.ts              # Vite build configuration
├── tsconfig.json               # TypeScript configuration
├── tailwind.config.ts          # Tailwind CSS configuration
├── components.json             # Shadcn/ui configuration
├── package.json                # Dependencies and scripts
└── .env                        # Environment variables
```

## 🛠️ Development Scripts

### **Available Scripts**
```json
{
  "scripts": {
    "dev": "vite --port 8081",           // Start development server
    "build": "tsc && vite build",        // Build for production
    "preview": "vite preview",           // Preview production build
    "lint": "eslint . --ext ts,tsx",     // Run ESLint
    "lint:fix": "eslint . --ext ts,tsx --fix", // Fix ESLint issues
    "type-check": "tsc --noEmit",        // Type checking only
    "format": "prettier --write .",      // Format code with Prettier
    "clean": "rm -rf dist node_modules", // Clean build artifacts
    "analyze": "npm run build && npx vite-bundle-analyzer dist" // Bundle analysis
  }
}
```

### **Development Commands**
```bash
# Start development with specific port
npm run dev -- --port 3000

# Build with type checking
npm run type-check && npm run build

# Lint and fix issues
npm run lint:fix

# Format all files
npm run format

# Clean and reinstall
npm run clean && npm install
```

## 🔍 Code Quality Tools

### **ESLint Configuration**
```json
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react-hooks/recommended",
    "plugin:react/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "react-hooks/exhaustive-deps": "warn",
    "react/prop-types": "off"
  }
}
```

### **Prettier Configuration**
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### **TypeScript Configuration**
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

## 🧪 Testing Setup

### **Testing Framework**
```bash
# Install testing dependencies
npm install --save-dev @testing-library/react @testing-library/jest-dom vitest jsdom

# Add test script to package.json
"test": "vitest",
"test:ui": "vitest --ui",
"test:coverage": "vitest --coverage"
```

### **Test Configuration** (`vitest.config.ts`)
```typescript
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
  },
});
```

### **Example Test Structure**
```
src/
├── 📂 __tests__/               # Test files
│   ├── components/
│   ├── hooks/
│   └── services/
├── 📂 test/                    # Test utilities
│   ├── setup.ts
│   ├── mocks/
│   └── utils/
```

## 🔧 Development Tools

### **Vite Configuration** (`vite.config.ts`)
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  server: {
    port: 8081,
    host: true,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          charts: ['recharts', 'framer-motion'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-tabs'],
        },
      },
    },
  },
});
```

### **Path Aliases**
```typescript
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/services/*": ["./src/services/*"],
      "@/types/*": ["./src/types/*"]
    }
  }
}
```

## 🐛 Debugging

### **Browser DevTools**
- **React Developer Tools** - Component inspection
- **Redux DevTools** - State management debugging
- **Network Tab** - API request monitoring
- **Console** - Error tracking and logging

### **VS Code Debugging**
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Chrome",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:8081",
      "webRoot": "${workspaceFolder}/src",
      "sourceMapPathOverrides": {
        "webpack:///src/*": "${webRoot}/*"
      }
    }
  ]
}
```

### **Error Tracking**
```typescript
// Error boundary for development
const ErrorBoundary = ({ children }: { children: React.ReactNode }) => {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Global error:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <div>Something went wrong. Check console for details.</div>;
  }

  return <>{children}</>;
};
```

## 🚀 Performance Optimization

### **Development Performance**
```typescript
// Vite HMR optimization
if (import.meta.hot) {
  import.meta.hot.accept();
}

// React development tools
if (process.env.NODE_ENV === 'development') {
  import('@welldone-software/why-did-you-render').then((whyDidYouRender) => {
    whyDidYouRender.default(React, {
      trackAllPureComponents: true,
    });
  });
}
```

### **Bundle Analysis**
```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer dist

# Check for duplicate dependencies
npx duplicate-package-checker-webpack-plugin
```

## 📝 Git Workflow

### **Branch Strategy**
```bash
# Feature development
git checkout -b feature/new-feature
git commit -m "feat: add new feature"
git push origin feature/new-feature

# Bug fixes
git checkout -b fix/bug-description
git commit -m "fix: resolve bug description"
git push origin fix/bug-description

# Hotfixes
git checkout -b hotfix/critical-fix
git commit -m "hotfix: critical issue fix"
git push origin hotfix/critical-fix
```

### **Commit Convention**
```
feat: new feature
fix: bug fix
docs: documentation
style: formatting
refactor: code restructuring
test: adding tests
chore: maintenance
```

This development setup ensures a productive, maintainable, and scalable development environment for CryptoVision Pro.
