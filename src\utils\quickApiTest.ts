/**
 * Quick API Test Utility
 * Simple functions to test API connectivity from browser console
 */

import { coinGeckoAxios } from '@/services/api/coinGeckoClient';
import { validateCoinGeckoKey } from '@/services/api/apiKeyValidation';

export class QuickApiTest {
  /**
   * Test CoinGecko API with the current configuration
   */
  static async testCoinGecko() {
    console.log('🧪 Testing CoinGecko API...');
    
    const apiKey = import.meta.env.VITE_COINGECKO_API_KEY;
    console.log('🔑 API Key:', apiKey ? `${apiKey.substring(0, 8)}...` : 'Not configured');
    
    try {
      // Test 1: Simple price request
      console.log('📊 Test 1: Simple price request...');
      const priceResponse = await coinGeckoAxios.get('/simple/price', {
        params: {
          ids: 'bitcoin,ethereum',
          vs_currencies: 'usd',
          include_24hr_change: true
        }
      });
      
      console.log('✅ Price request successful:', priceResponse.data);
      console.log('📈 Bitcoin price:', priceResponse.data.bitcoin?.usd);
      console.log('📈 Ethereum price:', priceResponse.data.ethereum?.usd);
      
      // Test 2: Global market data
      console.log('🌍 Test 2: Global market data...');
      const globalResponse = await coinGeckoAxios.get('/global');
      console.log('✅ Global data successful:', {
        totalMarketCap: globalResponse.data.data?.total_market_cap?.usd,
        btcDominance: globalResponse.data.data?.market_cap_percentage?.btc
      });
      
      // Test 3: API key validation
      console.log('🔐 Test 3: API key validation...');
      const validation = await validateCoinGeckoKey();
      console.log('✅ Validation result:', validation);
      
      return {
        success: true,
        message: 'All CoinGecko API tests passed!',
        data: {
          prices: priceResponse.data,
          global: globalResponse.data.data,
          validation
        }
      };
      
    } catch (error: any) {
      console.error('❌ CoinGecko API test failed:', error);
      
      const errorInfo = {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers
      };
      
      console.error('🔍 Error details:', errorInfo);
      
      return {
        success: false,
        message: 'CoinGecko API test failed',
        error: errorInfo
      };
    }
  }

  /**
   * Test direct fetch to CoinGecko (bypass axios configuration)
   */
  static async testDirectFetch() {
    console.log('🌐 Testing direct fetch to CoinGecko...');
    
    const apiKey = import.meta.env.VITE_COINGECKO_API_KEY;
    
    try {
      const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...(apiKey && apiKey !== 'demo_key' ? {
            'x-cg-demo-api-key': apiKey
          } : {})
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('✅ Direct fetch successful:', data);
      
      return {
        success: true,
        message: 'Direct fetch to CoinGecko successful',
        data
      };
      
    } catch (error: any) {
      console.error('❌ Direct fetch failed:', error);
      
      return {
        success: false,
        message: 'Direct fetch to CoinGecko failed',
        error: error.message
      };
    }
  }

  /**
   * Test network connectivity
   */
  static async testNetwork() {
    console.log('🌐 Testing network connectivity...');
    
    const testUrls = [
      'https://httpbin.org/get',
      'https://api.github.com',
      'https://jsonplaceholder.typicode.com/posts/1'
    ];
    
    const results = [];
    
    for (const url of testUrls) {
      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: { 'Accept': 'application/json' }
        });
        
        results.push({
          url,
          success: response.ok,
          status: response.status
        });
        
        console.log(`✅ ${url}: ${response.status}`);
        
      } catch (error: any) {
        results.push({
          url,
          success: false,
          error: error.message
        });
        
        console.log(`❌ ${url}: ${error.message}`);
      }
    }
    
    const allSuccessful = results.every(r => r.success);
    
    return {
      success: allSuccessful,
      message: allSuccessful ? 'Network connectivity is working' : 'Some network tests failed',
      results
    };
  }

  /**
   * Run all tests
   */
  static async runAllTests() {
    console.log('🚀 Running all API tests...');
    
    const network = await this.testNetwork();
    const directFetch = await this.testDirectFetch();
    const coinGecko = await this.testCoinGecko();
    
    const summary = {
      network: network.success,
      directFetch: directFetch.success,
      coinGecko: coinGecko.success,
      overall: network.success && directFetch.success && coinGecko.success
    };
    
    console.log('📊 Test Summary:', summary);
    
    if (summary.overall) {
      console.log('🎉 All tests passed! CoinGecko API is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Check the details above.');
    }
    
    return {
      summary,
      details: {
        network,
        directFetch,
        coinGecko
      }
    };
  }
}

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).quickApiTest = QuickApiTest;
  
  // Add convenient shortcuts
  (window as any).testCoinGecko = () => QuickApiTest.testCoinGecko();
  (window as any).testDirectFetch = () => QuickApiTest.testDirectFetch();
  (window as any).testNetwork = () => QuickApiTest.testNetwork();
  (window as any).runAllApiTests = () => QuickApiTest.runAllTests();
}
