
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Filter, Settings, TrendingUp, Star, Code, Users } from "lucide-react";

interface DiscoveryFiltersProps {
  activeFilter: string;
  onFilterChange: (filter: string) => void;
  riskProfile: string;
  onRiskProfileChange: (profile: string) => void;
}

export default function DiscoveryFilters({ 
  activeFilter, 
  onFilterChange, 
  riskProfile, 
  onRiskProfileChange 
}: DiscoveryFiltersProps) {
  const filters = [
    { id: 'trending', label: 'Trending', icon: TrendingUp, description: 'High momentum coins' },
    { id: 'emerging', label: 'Emerging', icon: Star, description: 'Low cap with potential' },
    { id: 'development', label: 'Dev Active', icon: Code, description: 'High development activity' },
    { id: 'personalized', label: 'For You', icon: Users, description: 'AI personalized picks' }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Discovery Filters
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-wrap gap-2">
            {filters.map((filter) => {
              const IconComponent = filter.icon;
              return (
                <Button
                  key={filter.id}
                  variant={activeFilter === filter.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => onFilterChange(filter.id)}
                  className="flex items-center gap-2"
                >
                  <IconComponent className="h-4 w-4" />
                  {filter.label}
                </Button>
              );
            })}
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Risk Profile:</span>
            </div>
            <Select value={riskProfile} onValueChange={onRiskProfileChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="conservative">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    Conservative
                  </div>
                </SelectItem>
                <SelectItem value="moderate">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                    Moderate
                  </div>
                </SelectItem>
                <SelectItem value="aggressive">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-red-500"></div>
                    Aggressive
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Active Filter Description */}
        <div className="mt-4 p-3 bg-muted/50 rounded-lg">
          <p className="text-sm text-muted-foreground">
            {filters.find(f => f.id === activeFilter)?.description || 'Filter description'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
