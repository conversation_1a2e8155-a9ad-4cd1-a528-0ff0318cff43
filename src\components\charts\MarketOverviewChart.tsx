import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>ltip,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';

interface MarketDataPoint {
  date: string;
  btcPrice: number;
  ethPrice: number;
  totalMarketCap: number;
  volume: number;
}

interface MarketOverviewChartProps {
  data: MarketDataPoint[];
  isLoading?: boolean;
}

const MarketOverviewChart: React.FC<MarketOverviewChartProps> = ({ data, isLoading }) => {
  if (isLoading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500 mb-2">No market data available</p>
          <p className="text-sm text-gray-400">Please check your API connection</p>
        </div>
      </div>
    );
  }

  // Format data for display
  const formatPrice = (value: number) => {
    if (value >= 1000000000) {
      return `$${(value / 1000000000).toFixed(2)}B`;
    } else if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`;
    }
    return `$${value.toFixed(2)}`;
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
          <XAxis 
            dataKey="date" 
            tickFormatter={formatDate}
            tick={{ fontSize: 12 }}
          />
          <YAxis 
            yAxisId="price"
            orientation="left"
            tickFormatter={formatPrice}
            tick={{ fontSize: 12 }}
          />
          <YAxis 
            yAxisId="marketcap"
            orientation="right"
            tickFormatter={formatPrice}
            tick={{ fontSize: 12 }}
          />
          <Tooltip 
            formatter={(value: number, name: string) => {
              const formattedValue = formatPrice(value);
              const label = name === 'btcPrice' ? 'Bitcoin Price' :
                           name === 'ethPrice' ? 'Ethereum Price' :
                           name === 'totalMarketCap' ? 'Total Market Cap' :
                           name === 'volume' ? 'Volume' : name;
              return [formattedValue, label];
            }}
            labelFormatter={(label) => `Date: ${formatDate(label)}`}
            contentStyle={{
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              border: 'none',
              borderRadius: '8px',
              color: 'white'
            }}
          />
          <Legend />
          <Line 
            yAxisId="price"
            type="monotone" 
            dataKey="btcPrice" 
            stroke="#f7931a" 
            strokeWidth={2}
            name="Bitcoin Price"
            dot={false}
          />
          <Line 
            yAxisId="price"
            type="monotone" 
            dataKey="ethPrice" 
            stroke="#627eea" 
            strokeWidth={2}
            name="Ethereum Price"
            dot={false}
          />
          <Line 
            yAxisId="marketcap"
            type="monotone" 
            dataKey="totalMarketCap" 
            stroke="#10b981" 
            strokeWidth={2}
            name="Total Market Cap"
            dot={false}
            strokeDasharray="5 5"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default MarketOverviewChart;
