// AI Services Diagnostic Tool
import { 
  generatePatternRecognitionData,
  generateAnomalyAlerts,
  generateSentimentAnalysis,
  generatePriceForecasts,
  generateRiskAssessment
} from '@/services/api/aiInsightsService';

export class AIDiagnostic {
  
  /**
   * Run complete AI services diagnostic
   */
  static async runCompleteAIDiagnostic() {
    console.log('🔍 RUNNING AI SERVICES DIAGNOSTIC...');
    console.log('==================================================');
    
    const results = {
      patternRecognition: { status: 'pending', data: null, error: null },
      anomalyDetection: { status: 'pending', data: null, error: null },
      sentimentAnalysis: { status: 'pending', data: null, error: null },
      priceForecasts: { status: 'pending', data: null, error: null },
      riskAssessment: { status: 'pending', data: null, error: null }
    };

    // Test Pattern Recognition
    console.log('1️⃣ Testing Pattern Recognition...');
    try {
      const patterns = await generatePatternRecognitionData('bitcoin', 30);
      results.patternRecognition.status = 'success';
      results.patternRecognition.data = patterns;
      console.log(`✅ Pattern Recognition: ${patterns.length} patterns detected`);
      patterns.forEach(p => console.log(`   - ${p.name} (${p.confidence}% confidence)`));
    } catch (error) {
      results.patternRecognition.status = 'error';
      results.patternRecognition.error = error.message;
      console.error('❌ Pattern Recognition failed:', error.message);
    }

    // Test Anomaly Detection
    console.log('\n2️⃣ Testing Anomaly Detection...');
    try {
      const anomalies = await generateAnomalyAlerts(5);
      results.anomalyDetection.status = 'success';
      results.anomalyDetection.data = anomalies;
      console.log(`✅ Anomaly Detection: ${anomalies.length} anomalies found`);
      anomalies.forEach(a => console.log(`   - ${a.type}: ${a.asset} (${a.severity})`));
    } catch (error) {
      results.anomalyDetection.status = 'error';
      results.anomalyDetection.error = error.message;
      console.error('❌ Anomaly Detection failed:', error.message);
    }

    // Test Sentiment Analysis
    console.log('\n3️⃣ Testing Sentiment Analysis...');
    try {
      const sentiment = await generateSentimentAnalysis('bitcoin');
      results.sentimentAnalysis.status = 'success';
      results.sentimentAnalysis.data = sentiment;
      console.log(`✅ Sentiment Analysis: ${sentiment.length} sources analyzed`);
      sentiment.forEach(s => console.log(`   - ${s.name}: ${s.sentiment} (${s.sentimentScore}%)`));
    } catch (error) {
      results.sentimentAnalysis.status = 'error';
      results.sentimentAnalysis.error = error.message;
      console.error('❌ Sentiment Analysis failed:', error.message);
    }

    // Test Price Forecasts
    console.log('\n4️⃣ Testing Price Forecasts...');
    try {
      const forecast = await generatePriceForecasts('bitcoin', 30);
      results.priceForecasts.status = 'success';
      results.priceForecasts.data = forecast;
      console.log(`✅ Price Forecasts: ${forecast.forecast.length} day forecast generated`);
      console.log(`   - R² Score: ${(forecast.metrics.r2Score * 100).toFixed(1)}%`);
      console.log(`   - Confidence: ${(forecast.metrics.confidence * 100).toFixed(1)}%`);
    } catch (error) {
      results.priceForecasts.status = 'error';
      results.priceForecasts.error = error.message;
      console.error('❌ Price Forecasts failed:', error.message);
    }

    // Test Risk Assessment
    console.log('\n5️⃣ Testing Risk Assessment...');
    try {
      const risks = await generateRiskAssessment(['bitcoin', 'ethereum']);
      results.riskAssessment.status = 'success';
      results.riskAssessment.data = risks;
      console.log(`✅ Risk Assessment: ${risks.length} assets analyzed`);
      risks.forEach(r => console.log(`   - ${r.name}: ${r.overallRisk.label} (${r.overallRisk.score})`));
    } catch (error) {
      results.riskAssessment.status = 'error';
      results.riskAssessment.error = error.message;
      console.error('❌ Risk Assessment failed:', error.message);
    }

    // Summary
    console.log('\n📊 DIAGNOSTIC SUMMARY:');
    console.log('==================================================');
    const successCount = Object.values(results).filter(r => r.status === 'success').length;
    const totalTests = Object.keys(results).length;
    
    console.log(`✅ Successful: ${successCount}/${totalTests}`);
    console.log(`❌ Failed: ${totalTests - successCount}/${totalTests}`);
    
    if (successCount === totalTests) {
      console.log('🎉 ALL AI SERVICES ARE WORKING!');
    } else {
      console.log('⚠️ Some AI services need attention');
    }

    return results;
  }

  /**
   * Quick test for AI Market Intelligence page
   */
  static async quickAITest() {
    console.log('🚀 Quick AI Test for Market Intelligence...');
    
    try {
      const [patterns, anomalies, sentiment] = await Promise.all([
        generatePatternRecognitionData('bitcoin', 7),
        generateAnomalyAlerts(3),
        generateSentimentAnalysis('bitcoin')
      ]);

      console.log('✅ Quick test results:');
      console.log(`   - Patterns: ${patterns.length}`);
      console.log(`   - Anomalies: ${anomalies.length}`);
      console.log(`   - Sentiment sources: ${sentiment.length}`);
      
      return { patterns, anomalies, sentiment };
    } catch (error) {
      console.error('❌ Quick test failed:', error);
      throw error;
    }
  }

  /**
   * Test API connectivity
   */
  static async testAPIConnectivity() {
    console.log('🌐 Testing API Connectivity...');
    
    const apis = [
      { name: 'CoinGecko', url: 'https://api.coingecko.com/api/v3/ping' },
      { name: 'CoinGecko Market', url: 'https://api.coingecko.com/api/v3/coins/bitcoin' }
    ];

    for (const api of apis) {
      try {
        const response = await fetch(api.url, { 
          method: 'GET',
          headers: { 'Accept': 'application/json' }
        });
        
        if (response.ok) {
          console.log(`✅ ${api.name}: Connected`);
        } else {
          console.log(`⚠️ ${api.name}: HTTP ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ ${api.name}: ${error.message}`);
      }
    }
  }
}

// Make it available globally for browser console testing
declare global {
  interface Window {
    aiDiagnostic: typeof AIDiagnostic;
  }
}

if (typeof window !== 'undefined') {
  window.aiDiagnostic = AIDiagnostic;
}

export default AIDiagnostic;
