import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  BarChart3, 
  Activity,
  Users,
  Zap,
  Shield,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';
import { MarketMetrics, VolatilityData } from '@/services/api/enhancedMarketInsights';

interface MarketOverviewDashboardProps {
  metrics: MarketMetrics;
  volatility: VolatilityData;
  marketHealth: 'excellent' | 'good' | 'neutral' | 'poor' | 'critical';
  marketTrend: 'bullish' | 'bearish' | 'sideways';
  riskLevel: 'low' | 'medium' | 'high' | 'extreme';
  loading?: boolean;
}

export const MarketOverviewDashboard: React.FC<MarketOverviewDashboardProps> = ({
  metrics,
  volatility,
  marketHealth,
  marketTrend,
  riskLevel,
  loading
}) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
                <div className="w-6 h-6 bg-gray-200 rounded animate-pulse" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="w-24 h-8 bg-gray-200 rounded animate-pulse mb-2" />
              <div className="w-16 h-4 bg-gray-200 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    return `$${value.toLocaleString()}`;
  };

  const formatNumber = (value: number) => {
    if (value >= 1e6) return `${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `${(value / 1e3).toFixed(1)}K`;
    return value.toLocaleString();
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? 
      <TrendingUp className="w-4 h-4 text-green-600" /> : 
      <TrendingDown className="w-4 h-4 text-red-600" />;
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'text-emerald-600 bg-emerald-50 border-emerald-200';
      case 'good': return 'text-green-600 bg-green-50 border-green-200';
      case 'neutral': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'poor': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'bullish': return 'text-green-600 bg-green-50 border-green-200';
      case 'bearish': return 'text-red-600 bg-red-50 border-red-200';
      case 'sideways': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'extreme': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const marketOverviewCards = [
    {
      title: 'Total Market Cap',
      value: formatCurrency(metrics.totalMarketCap),
      change: metrics.marketCapChange24h,
      icon: <DollarSign className="w-5 h-5" />,
      description: '24h change'
    },
    {
      title: 'Total Volume',
      value: formatCurrency(metrics.totalVolume),
      change: metrics.volumeChange24h,
      icon: <BarChart3 className="w-5 h-5" />,
      description: '24h change'
    },
    {
      title: 'BTC Dominance',
      value: `${metrics.btcDominance.toFixed(1)}%`,
      change: null,
      icon: <Activity className="w-5 h-5" />,
      description: 'Market share'
    },
    {
      title: 'ETH Dominance',
      value: `${metrics.ethDominance.toFixed(1)}%`,
      change: null,
      icon: <Activity className="w-5 h-5" />,
      description: 'Market share'
    },
    {
      title: 'DeFi TVL',
      value: formatCurrency(metrics.defiTvl),
      change: null,
      icon: <Zap className="w-5 h-5" />,
      description: 'Total value locked'
    },
    {
      title: 'Active Addresses',
      value: formatNumber(metrics.activeAddresses),
      change: null,
      icon: <Users className="w-5 h-5" />,
      description: 'Daily active'
    },
    {
      title: 'Market Volatility',
      value: `${volatility.marketVolatility.toFixed(1)}%`,
      change: null,
      icon: <Activity className="w-5 h-5" />,
      description: 'Average 24h'
    },
    {
      title: 'VIX Equivalent',
      value: volatility.vixEquivalent.toFixed(1),
      change: null,
      icon: <AlertCircle className="w-5 h-5" />,
      description: 'Crypto fear index'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Market Health Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Market Health</p>
                <Badge className={getHealthColor(marketHealth)}>
                  {marketHealth.charAt(0).toUpperCase() + marketHealth.slice(1)}
                </Badge>
              </div>
              <CheckCircle2 className={`w-8 h-8 ${
                marketHealth === 'excellent' || marketHealth === 'good' ? 'text-green-500' : 
                marketHealth === 'neutral' ? 'text-yellow-500' : 'text-red-500'
              }`} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Market Trend</p>
                <Badge className={getTrendColor(marketTrend)}>
                  {marketTrend.charAt(0).toUpperCase() + marketTrend.slice(1)}
                </Badge>
              </div>
              {marketTrend === 'bullish' ? 
                <TrendingUp className="w-8 h-8 text-green-500" /> :
                marketTrend === 'bearish' ? 
                <TrendingDown className="w-8 h-8 text-red-500" /> :
                <Activity className="w-8 h-8 text-gray-500" />
              }
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Risk Level</p>
                <Badge className={getRiskColor(riskLevel)}>
                  {riskLevel.charAt(0).toUpperCase() + riskLevel.slice(1)}
                </Badge>
              </div>
              <Shield className={`w-8 h-8 ${
                riskLevel === 'low' ? 'text-green-500' : 
                riskLevel === 'medium' ? 'text-yellow-500' : 
                riskLevel === 'high' ? 'text-orange-500' : 'text-red-500'
              }`} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Market Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {marketOverviewCards.map((card, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center justify-between text-sm font-medium">
                <span className="text-gray-600 dark:text-gray-400">{card.title}</span>
                <div className="text-gray-400">{card.icon}</div>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-1">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {card.value}
                </div>
                <div className="flex items-center gap-1 text-sm">
                  {card.change !== null ? (
                    <>
                      {getChangeIcon(card.change)}
                      <span className={getChangeColor(card.change)}>
                        {card.change > 0 ? '+' : ''}{card.change.toFixed(2)}%
                      </span>
                      <span className="text-gray-500">{card.description}</span>
                    </>
                  ) : (
                    <span className="text-gray-500">{card.description}</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Volatility Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Volatility Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Week</span>
                <span className="text-sm font-medium">{volatility.historicalComparison.week.toFixed(1)}%</span>
              </div>
              <Progress value={volatility.historicalComparison.week * 5} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Month</span>
                <span className="text-sm font-medium">{volatility.historicalComparison.month.toFixed(1)}%</span>
              </div>
              <Progress value={volatility.historicalComparison.month * 5} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Quarter</span>
                <span className="text-sm font-medium">{volatility.historicalComparison.quarter.toFixed(1)}%</span>
              </div>
              <Progress value={volatility.historicalComparison.quarter * 5} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Year</span>
                <span className="text-sm font-medium">{volatility.historicalComparison.year.toFixed(1)}%</span>
              </div>
              <Progress value={volatility.historicalComparison.year * 5} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
