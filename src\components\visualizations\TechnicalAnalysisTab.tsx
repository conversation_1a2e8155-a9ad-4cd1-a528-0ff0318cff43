
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import CandlestickChart from "@/components/CandlestickChart";
import HeatMapChart from "@/components/HeatMapChart";
import { Loader2 } from "lucide-react";

interface TechnicalAnalysisTabProps {
  selectedCoin: {
    id: string;
    name: string;
    symbol: string;
  };
  chartResponse: any;
  correlationData: any;
  isLoadingChart: boolean;
  isLoadingCorrelation: boolean;
  selectedTimeframe: string;
  setSelectedTimeframe: (timeframe: string) => void;
}

export default function TechnicalAnalysisTab({
  selectedCoin,
  chartResponse,
  correlationData,
  isLoadingChart,
  isLoadingCorrelation,
  selectedTimeframe,
  setSelectedTimeframe
}: TechnicalAnalysisTabProps) {
  const timeframeMap: Record<string, number> = {
    "1D": 1,
    "7D": 7,
    "30D": 30,
    "90D": 90,
    "1Y": 365
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Advanced Technical Analysis</CardTitle>
          <CardDescription>
            Interactive candlestick chart with technical indicators and price predictions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CandlestickChart 
            data={chartResponse?.data || []}
            title={`${selectedCoin.name} (${selectedCoin.symbol})`}
            description="Price chart with technical indicators and forecast"
            timeframes={Object.keys(timeframeMap)}
            onTimeframeChange={setSelectedTimeframe}
            isLoading={isLoadingChart}
            height={500}
            showTechnicalIndicators={true}
          />
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Asset Correlation Analysis</CardTitle>
          <CardDescription>
            Visualizing price correlations between {selectedCoin.name} and other major assets
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingCorrelation ? (
            <div className="flex items-center justify-center h-[300px]">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : correlationData && correlationData.length > 0 ? (
            <HeatMapChart 
              title="Asset Correlation Heatmap"
              data={correlationData.map((item: any) => ({
                name: item.assetSymbol,
                value: item.correlation,
                percentChange: item.comparedChange,
                category: "Correlation with " + selectedCoin.symbol
              }))}
              isLoading={isLoadingCorrelation}
              colorScale="correlation"
            />
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No correlation data available for this asset
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
