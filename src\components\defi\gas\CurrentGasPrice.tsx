
import React from "react";
import { Badge } from "@/components/ui/badge";

interface CurrentGasPriceProps {
  currentGwei: number;
  baseFee: number;
  priorityFee: number;
  networkId: string;
  formatGasPrice: (price: number, networkId: string) => string;
}

const CurrentGasPrice = ({ 
  currentGwei, 
  baseFee, 
  priorityFee, 
  networkId,
  formatGasPrice 
}: CurrentGasPriceProps) => {
  return (
    <div className="p-4 border rounded-lg bg-secondary/50">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-medium">Current Gas Price</h3>
        <Badge>{formatGasPrice(currentGwei, networkId)}</Badge>
      </div>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <p className="text-muted-foreground">Base Fee</p>
          <p className="font-medium">{formatGasPrice(baseFee, networkId)}</p>
        </div>
        <div>
          <p className="text-muted-foreground">Priority Fee</p>
          <p className="font-medium">{formatGasPrice(priorityFee, networkId)}</p>
        </div>
      </div>
    </div>
  );
};

export default CurrentGasPrice;
