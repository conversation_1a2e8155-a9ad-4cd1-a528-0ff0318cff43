
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart, ReferenceLine } from "recharts";
import { Target, TrendingUp, <PERSON><PERSON><PERSON>riangle, Brain } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { CoinData, PriceTargets, AnalysisProps } from "./types";

interface PricePredictionChartProps extends AnalysisProps {
  priceTargets: PriceTargets;
}

export function PricePredictionChart({ coinData, priceTargets, isLoading }: PricePredictionChartProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse h-80 bg-gray-200 rounded-lg"></div>
      </div>
    );
  }

  const currentPrice = coinData.current_price;
  
  // Generate prediction timeline (next 90 days)
  const predictionData = Array.from({ length: 90 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() + i);
    
    const timeProgress = i / 90;
    const conservativePrice = currentPrice + (priceTargets.conservative - currentPrice) * timeProgress;
    const optimisticPrice = currentPrice + (priceTargets.optimistic - currentPrice) * timeProgress;
    const bearishPrice = currentPrice + (priceTargets.bearish - currentPrice) * timeProgress;
    
    const volatility = 0.05;
    const noise = (Math.random() - 0.5) * volatility;
    
    return {
      date: date.toISOString().split('T')[0],
      conservative: conservativePrice * (1 + noise * 0.5),
      optimistic: optimisticPrice * (1 + noise),
      bearish: bearishPrice * (1 + noise * 0.3),
      current: i === 0 ? currentPrice : null,
    };
  });

  // Calculate probability scores
  const marketCap = coinData.market_cap;
  const volume = coinData.total_volume;
  const volatility = Math.abs(coinData.price_change_percentage_24h || 0);
  
  const bullishProbability = Math.min(90, Math.max(10, 
    50 + (coinData.momentum_score - 50) * 0.8 - volatility + (volume / marketCap) * 100
  ));
  
  const bearishProbability = 100 - bullishProbability;

  return (
    <div className="space-y-6">
      {/* Price Target Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-red-200">
          <CardContent className="p-4 text-center">
            <AlertTriangle className="h-6 w-6 text-red-500 mx-auto mb-2" />
            <div className="text-sm text-muted-foreground">Bearish Target</div>
            <div className="text-2xl font-bold text-red-600">
              ${priceTargets.bearish.toFixed(4)}
            </div>
            <div className="text-sm text-red-600">
              {(((priceTargets.bearish - currentPrice) / currentPrice) * 100).toFixed(1)}%
            </div>
          </CardContent>
        </Card>

        <Card className="border-blue-200">
          <CardContent className="p-4 text-center">
            <Target className="h-6 w-6 text-blue-500 mx-auto mb-2" />
            <div className="text-sm text-muted-foreground">Conservative Target</div>
            <div className="text-2xl font-bold text-blue-600">
              ${priceTargets.conservative.toFixed(4)}
            </div>
            <div className="text-sm text-blue-600">
              {(((priceTargets.conservative - currentPrice) / currentPrice) * 100).toFixed(1)}%
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-200">
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-6 w-6 text-green-500 mx-auto mb-2" />
            <div className="text-sm text-muted-foreground">Optimistic Target</div>
            <div className="text-2xl font-bold text-green-600">
              ${priceTargets.optimistic.toFixed(4)}
            </div>
            <div className="text-sm text-green-600">
              {(((priceTargets.optimistic - currentPrice) / currentPrice) * 100).toFixed(1)}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Price Prediction Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Price Prediction (90 Days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={predictionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={(value) => new Date(value).toLocaleDateString()}
              />
              <YAxis tickFormatter={(value) => `$${value.toFixed(4)}`} />
              <Tooltip 
                formatter={(value, name) => [
                  `$${Number(value).toFixed(4)}`, 
                  name === 'conservative' ? 'Conservative' : 
                  name === 'optimistic' ? 'Optimistic' : 'Bearish'
                ]}
                labelFormatter={(label) => new Date(label).toLocaleDateString()}
              />
              <Area 
                type="monotone" 
                dataKey="optimistic" 
                stackId="1"
                stroke="#10b981" 
                fill="#10b981" 
                fillOpacity={0.1}
              />
              <Area 
                type="monotone" 
                dataKey="conservative" 
                stackId="2"
                stroke="#3b82f6" 
                fill="#3b82f6" 
                fillOpacity={0.2}
              />
              <Area 
                type="monotone" 
                dataKey="bearish" 
                stackId="3"
                stroke="#ef4444" 
                fill="#ef4444" 
                fillOpacity={0.1}
              />
              <ReferenceLine y={currentPrice} stroke="#6b7280" strokeDasharray="5 5" />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Market Sentiment & Probability */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Market Sentiment Analysis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm">Bullish Probability</span>
                <span className="text-sm font-medium">{bullishProbability.toFixed(1)}%</span>
              </div>
              <Progress value={bullishProbability} className="h-3" />
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm">Bearish Probability</span>
                <span className="text-sm font-medium">{bearishProbability.toFixed(1)}%</span>
              </div>
              <Progress value={bearishProbability} className="h-3" />
            </div>
            <div className="pt-2">
              <Badge variant={bullishProbability > 60 ? 'default' : bearishProbability > 60 ? 'destructive' : 'secondary'}>
                {bullishProbability > 60 ? 'Bullish Outlook' : bearishProbability > 60 ? 'Bearish Outlook' : 'Neutral Outlook'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>AI Analysis Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              <p className="text-sm leading-relaxed">
                Based on comprehensive analysis, {coinData.name} shows 
                <strong> {coinData.momentum_score > 60 ? 'strong' : 'moderate'} momentum</strong> with 
                current sentiment trending <strong>{coinData.market_sentiment}</strong>. 
                Risk level: <strong>{coinData.risk_level}</strong>.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
