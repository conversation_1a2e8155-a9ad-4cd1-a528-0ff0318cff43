
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import HeaderBar from "@/components/HeaderBar";
import RatingTable from "@/components/RatingTable";
import RatingFactors from "@/components/RatingFactors";
import { useRatingSystem } from "@/hooks/useRatingSystem";
import { COINGECKO_ATTRIBUTION } from "@/services/api/coinGeckoClient";
import { Card, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Home } from "lucide-react";

export default function Ratings() {
  const navigate = useNavigate();
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();
  const { 
    assets, 
    factorWeights,
    updateFactorWeight,
    refreshData,
    isLoading,
    error,
    isUsingCache
  } = useRatingSystem();

  const handleRefresh = () => {
    setRefreshing(true);
    refreshData();
    
    // Simulate minimum loading time for better UX
    setTimeout(() => {
      setRefreshing(false);
      toast({
        title: "Data refreshed",
        description: error 
          ? "Using cached data due to API limitations." 
          : "All rating data has been updated from CoinGecko.",
      });
    }, 1500);
  };
  
  // Combine loading states for UI
  const displayLoading = isLoading || refreshing;
  
  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar 
        title="Multi-Factor Rating System" 
        description="Comprehensive cryptocurrency growth potential scoring"
        onRefresh={handleRefresh}
        isLoading={displayLoading}
        actions={
          <Button variant="outline" size="sm" onClick={() => navigate("/")} className="flex items-center gap-1">
            <Home className="h-4 w-4" />
            Market Overview
          </Button>
        }
      />
      
      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          <RatingFactors 
            factorWeights={factorWeights} 
            updateWeight={updateFactorWeight}
          />
          
          <RatingTable 
            assets={assets} 
            isLoading={displayLoading}
          />

          <Card>
            <CardFooter className="text-xs text-muted-foreground py-4">
              {COINGECKO_ATTRIBUTION}
              {isUsingCache && <span className="text-amber-500 ml-4">Using cached data</span>}
              {error && <span className="text-amber-500 ml-4">{error}</span>}
            </CardFooter>
          </Card>
        </div>
      </main>
    </div>
  );
}
