
/**
 * Centralized type definitions for price history and forecasting
 */

export interface PriceDataPoint {
  date: string;
  price: number;
  volume?: number;
  marketCap?: number;
  timestamp?: number;
}

export interface CandleData {
  time: string;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
  predicted?: boolean;
}

export interface PriceForecast {
  predictions: Array<{
    date: string;
    predictedPrice: number;
    confidence: number;
    upperBound: number;
    lowerBound: number;
  }>;
  accuracy: number;
  model: string;
  features: string[];
}

export interface ForecastMetrics {
  rmse: number;
  mae: number;
  rSquared: number;
  mape: number;
}

export interface ForecastDataResponse {
  success: boolean;
  data: CandleData[];
  error?: string;
  metrics?: ForecastMetrics;
}

export interface PriceHistoryResponse {
  data: PriceDataPoint[];
  success: boolean;
  error?: string;
}

export interface ForecastResponse {
  success: boolean;
  data?: PriceForecast;
  error?: string;
  metrics?: ForecastMetrics;
}
