import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  AlertTriangle,
  Database,
  Copy,
  CheckCircle,
  ExternalLink,
  Play,
  FileText,
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

export function DatabaseSetup() {
  const [isChecking, setIsChecking] = useState(false);
  const [setupStatus, setSetupStatus] = useState<'unknown' | 'missing' | 'complete'>('unknown');
  const [showMigration, setShowMigration] = useState(false);

  const migrationScript = `-- Migration: Create API Providers Management Tables
-- Description: Creates tables for managing API providers, costs, and cache entries
-- Version: 1.0.0

-- Create API providers table
CREATE TABLE IF NOT EXISTS api_providers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL UNIQUE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('market', 'onchain', 'defi', 'ai')),
  priority INTEGER NOT NULL CHECK (priority >= 1 AND priority <= 100),
  rate_limit_per_minute INTEGER CHECK (rate_limit_per_minute >= 0),
  monthly_quota INTEGER CHECK (monthly_quota >= 0),
  cost_per_request DECIMAL(10,6) CHECK (cost_per_request >= 0),
  cost_per_token DECIMAL(10,8) CHECK (cost_per_token >= 0),
  is_active BOOLEAN DEFAULT true,
  config JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_api_providers_type ON api_providers(type);
CREATE INDEX IF NOT EXISTS idx_api_providers_priority ON api_providers(type, priority);
CREATE INDEX IF NOT EXISTS idx_api_providers_active ON api_providers(is_active);

-- Create API costs tracking table
CREATE TABLE IF NOT EXISTS api_costs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID REFERENCES api_providers(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  requests_count INTEGER DEFAULT 0 CHECK (requests_count >= 0),
  tokens_used INTEGER DEFAULT 0 CHECK (tokens_used >= 0),
  estimated_cost DECIMAL(10,4) DEFAULT 0 CHECK (estimated_cost >= 0),
  actual_cost DECIMAL(10,4) CHECK (actual_cost >= 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(provider_id, date)
);

-- Create enhanced cache management table
CREATE TABLE IF NOT EXISTS cache_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cache_key VARCHAR(255) NOT NULL UNIQUE,
  provider VARCHAR(50) NOT NULL,
  data_type VARCHAR(50) NOT NULL,
  data JSONB NOT NULL,
  ttl_seconds INTEGER NOT NULL CHECK (ttl_seconds > 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  hit_count INTEGER DEFAULT 0 CHECK (hit_count >= 0),
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create AI prompt templates table
CREATE TABLE IF NOT EXISTS ai_prompt_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id VARCHAR(50) NOT NULL UNIQUE,
  type VARCHAR(20) NOT NULL,
  template TEXT NOT NULL,
  max_tokens INTEGER NOT NULL CHECK (max_tokens > 0),
  temperature DECIMAL(3,2) NOT NULL CHECK (temperature >= 0 AND temperature <= 2),
  cache_ttl INTEGER NOT NULL CHECK (cache_ttl >= 0),
  cost_tier VARCHAR(10) NOT NULL CHECK (cost_tier IN ('low', 'medium', 'high')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE api_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_costs ENABLE ROW LEVEL SECURITY;
ALTER TABLE cache_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_prompt_templates ENABLE ROW LEVEL SECURITY;

-- Create policies for admin access
CREATE POLICY "Admin can manage api_providers" ON api_providers
    FOR ALL USING (true);

CREATE POLICY "Admin can view api_costs" ON api_costs
    FOR SELECT USING (true);

CREATE POLICY "System can insert api_costs" ON api_costs
    FOR INSERT WITH CHECK (true);

CREATE POLICY "System can manage cache_entries" ON cache_entries
    FOR ALL USING (true);

CREATE POLICY "Admin can manage ai_prompt_templates" ON ai_prompt_templates
    FOR ALL USING (true);

-- Insert default API providers
INSERT INTO api_providers (name, type, priority, rate_limit_per_minute, monthly_quota, cost_per_request, is_active, config) VALUES
('CoinGecko', 'market', 1, 30, 10000, 0.001, true, '{"baseUrl": "https://api.coingecko.com/api/v3", "timeout": 15000}'),
('DeepSeek', 'ai', 1, 100, 1000000, null, true, '{"baseUrl": "https://api.deepseek.com", "timeout": 60000}'),
('GeckoTerminal', 'onchain', 2, 30, 50000, 0, true, '{"baseUrl": "https://api.geckoterminal.com/api/v2", "timeout": 15000}'),
('DeFi Llama', 'defi', 1, 300, 999999, 0, true, '{"baseUrl": "https://api.llama.fi", "timeout": 10000}')
ON CONFLICT (name) DO NOTHING;

-- Update AI providers with token costs
UPDATE api_providers SET cost_per_token = 0.00000014 WHERE name = 'DeepSeek';

SELECT 'API Providers management tables created successfully' AS status;`;

  const checkDatabaseSetup = async () => {
    setIsChecking(true);
    try {
      const { data, error } = await supabase
        .from('api_providers')
        .select('count')
        .limit(1);

      if (error) {
        if (error.code === '42P01' || error.message.includes('does not exist')) {
          setSetupStatus('missing');
        } else {
          console.error('Database check error:', error);
          setSetupStatus('missing');
        }
      } else {
        setSetupStatus('complete');
      }
    } catch (error) {
      console.error('Database check failed:', error);
      setSetupStatus('missing');
    } finally {
      setIsChecking(false);
    }
  };

  const copyMigrationScript = () => {
    navigator.clipboard.writeText(migrationScript);
    toast({
      title: 'Copied!',
      description: 'Migration script copied to clipboard.',
    });
  };

  const openSupabaseDashboard = () => {
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    if (supabaseUrl) {
      const projectRef = supabaseUrl.split('//')[1].split('.')[0];
      window.open(`https://supabase.com/dashboard/project/${projectRef}/editor`, '_blank');
    } else {
      window.open('https://supabase.com/dashboard', '_blank');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Setup Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span>API Provider Tables:</span>
              {setupStatus === 'complete' && (
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Ready
                </Badge>
              )}
              {setupStatus === 'missing' && (
                <Badge variant="destructive">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Missing
                </Badge>
              )}
              {setupStatus === 'unknown' && (
                <Badge variant="secondary">Unknown</Badge>
              )}
            </div>
            <Button
              onClick={checkDatabaseSetup}
              disabled={isChecking}
              variant="outline"
              size="sm"
            >
              {isChecking ? 'Checking...' : 'Check Status'}
            </Button>
          </div>

          {setupStatus === 'missing' && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                The API provider management tables are not set up in your database. 
                You need to run the migration script to enable full functionality.
              </AlertDescription>
            </Alert>
          )}

          {setupStatus === 'complete' && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                Database tables are properly configured. API Provider management is ready to use.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {setupStatus === 'missing' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Setup Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">1</span>
                <span>Copy the migration script below</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">2</span>
                <span>Open your Supabase SQL Editor</span>
                <Button
                  onClick={openSupabaseDashboard}
                  variant="outline"
                  size="sm"
                  className="ml-auto"
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Open Supabase
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">3</span>
                <span>Paste and run the migration script</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">4</span>
                <span>Return here and check the status</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">Migration Script:</span>
                <div className="flex gap-2">
                  <Button
                    onClick={() => setShowMigration(!showMigration)}
                    variant="outline"
                    size="sm"
                  >
                    <FileText className="h-3 w-3 mr-1" />
                    {showMigration ? 'Hide' : 'Show'} Script
                  </Button>
                  <Button
                    onClick={copyMigrationScript}
                    variant="outline"
                    size="sm"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </Button>
                </div>
              </div>

              {showMigration && (
                <Textarea
                  value={migrationScript}
                  readOnly
                  className="font-mono text-xs h-64 resize-none"
                />
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
