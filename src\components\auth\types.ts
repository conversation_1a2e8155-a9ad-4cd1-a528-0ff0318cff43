
// Auth component interfaces and types

export interface AuthLayoutProps {
  title: string;
  description: string;
  children: React.ReactNode;
}

export interface AuthTabsProps {
  value: 'sign_in' | 'sign_up';
  onValueChange: (value: string) => void;
  signInContent: React.ReactNode;
  signUpContent: React.ReactNode;
}

export interface AuthAlertsProps {
  error: string | null;
  successMessage: string | null;
}

export interface SignInFormProps {
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
  isSubmitting: boolean;
  onSubmit: (e: React.FormEvent) => Promise<void>;
  onPasswordReset: () => Promise<void>;
}

export interface SignUpFormProps {
  email: string;
  setEmail: (email: string) => void;
  username: string;
  setUsername: (username: string) => void;
  fullName: string;
  setFullName: (fullName: string) => void;
  password: string;
  setPassword: (password: string) => void;
  isSubmitting: boolean;
  onSubmit: (e: React.FormEvent) => Promise<void>;
}

export interface SocialAuthProps {
  onSocialAuth: (provider: 'google' | 'apple' | 'facebook' | 'github') => Promise<void>;
  isDisabled?: boolean;
}
