
/**
 * Format date for display in charts and tooltips
 */
export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
};

/**
 * Format price for display in charts and tooltips
 */
export const formatPrice = (price: number | null | undefined) => {
  if (price === null || price === undefined || isNaN(Number(price))) {
    return 'N/A';
  }
  
  const numPrice = Number(price);
  if (!isFinite(numPrice)) {
    return 'N/A';
  }
  
  if (numPrice > 1000) {
    return `$${numPrice.toLocaleString(undefined, { maximumFractionDigits: 0 })}`;
  } else if (numPrice > 1) {
    return `$${numPrice.toLocaleString(undefined, { maximumFractionDigits: 2 })}`;
  } else {
    return `$${numPrice.toLocaleString(undefined, { maximumFractionDigits: 6 })}`;
  }
};

/**
 * Calculate potential return percentage
 */
export const calculatePotentialReturn = (lastHistoricalPrice: number | null | undefined, lastForecastPrice: number | null | undefined): number => {
  if (!lastHistoricalPrice || !lastForecastPrice || isNaN(Number(lastHistoricalPrice)) || isNaN(Number(lastForecastPrice))) {
    return 0;
  }
  
  const historical = Number(lastHistoricalPrice);
  const forecast = Number(lastForecastPrice);
  
  if (!isFinite(historical) || !isFinite(forecast) || historical === 0) {
    return 0;
  }
  
  return ((forecast - historical) / historical) * 100;
};
