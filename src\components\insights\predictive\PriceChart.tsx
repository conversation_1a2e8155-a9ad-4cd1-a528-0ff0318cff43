
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, Area, ReferenceLine } from "recharts";
import { formatDate, formatPrice } from "./formatters";

export interface EnhancedDataPoint {
  date: string;
  price: number;
  upperBound?: number;
  lowerBound?: number;
}

interface PriceChartProps {
  enhancedChartData: EnhancedDataPoint[];
  currentDate: string | null;
}

export default function PriceChart({ enhancedChartData, currentDate }: PriceChartProps) {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={enhancedChartData}>
          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
          <XAxis 
            dataKey="date" 
            tick={{ fontSize: 12 }}
            tickFormatter={formatDate}
          />
          <YAxis 
            tickFormatter={formatPrice}
            width={80}
          />
          <Tooltip 
            formatter={(value: number) => [formatPrice(value), '']}
            labelFormatter={(label) => `Date: ${formatDate(label)}`}
          />
          
          {currentDate && (
            <ReferenceLine 
              x={currentDate} 
              stroke="#64748b" 
              strokeDasharray="3 3" 
              label={{ 
                value: 'Current', 
                position: 'top', 
                fill: '#64748b',
                fontSize: 12 
              }} 
            />
          )}
          
          <defs>
            <linearGradient id="colorUpper" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#22c55e" stopOpacity={0.2}/>
              <stop offset="95%" stopColor="#22c55e" stopOpacity={0.05}/>
            </linearGradient>
            <linearGradient id="colorLower" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#ef4444" stopOpacity={0.05}/>
              <stop offset="95%" stopColor="#ef4444" stopOpacity={0.2}/>
            </linearGradient>
          </defs>

          <Area 
            type="monotone" 
            dataKey="upperBound" 
            stroke="none" 
            fill="url(#colorUpper)" 
            activeDot={false} 
            isAnimationActive={false}
          />
          <Area 
            type="monotone" 
            dataKey="lowerBound" 
            stroke="none" 
            fill="url(#colorLower)" 
            activeDot={false} 
            isAnimationActive={false}
          />
          
          <Line 
            type="monotone" 
            dataKey="price" 
            stroke="#3b82f6"
            strokeWidth={2}
            dot={{ r: 2 }}
            name="Price"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
