
import { useState, useEffect } from "react";
import { Clock, TrendingUp, AlertTriangle } from "lucide-react";

interface NewsItem {
  id: number;
  title: string;
  time: string;
  type: 'bullish' | 'bearish' | 'neutral';
  source: string;
}

const mockNews: NewsItem[] = [
  {
    id: 1,
    title: "Bitcoin ETF sees $2.1B inflow in largest week on record",
    time: "14:32",
    type: "bullish",
    source: "Bloomberg"
  },
  {
    id: 2,
    title: "Ethereum Shanghai upgrade completed successfully",
    time: "13:45",
    type: "bullish",
    source: "CoinDesk"
  },
  {
    id: 3,
    title: "SEC announces new crypto regulation framework",
    time: "12:18",
    type: "neutral",
    source: "Reuters"
  },
  {
    id: 4,
    title: "Major DeFi protocol exploit leads to $50M loss",
    time: "11:52",
    type: "bearish",
    source: "The Block"
  }
];

export default function NewsTerminal() {
  const [currentNews, setCurrentNews] = useState(mockNews);

  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate new news items
      const newItem: NewsItem = {
        id: Date.now(),
        title: "Market volatility increases amid global uncertainty",
        time: new Date().toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' }),
        type: "neutral",
        source: "Financial Times"
      };
      
      setCurrentNews(prev => [newItem, ...prev.slice(0, 5)]);
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'bullish':
        return <TrendingUp className="h-3 w-3 text-green-500" />;
      case 'bearish':
        return <AlertTriangle className="h-3 w-3 text-red-500" />;
      default:
        return <Clock className="h-3 w-3 text-blue-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'bullish':
        return 'border-l-green-500';
      case 'bearish':
        return 'border-l-red-500';
      default:
        return 'border-l-blue-500';
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-white font-semibold text-sm">Market News</h4>
        <div className="text-xs text-gray-400">Live Feed</div>
      </div>
      
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {currentNews.map((item) => (
          <div 
            key={item.id} 
            className={`bg-gray-800 border-l-2 ${getTypeColor(item.type)} p-3 rounded-r hover:bg-gray-700 transition-colors cursor-pointer`}
          >
            <div className="flex items-start justify-between space-x-2">
              <div className="flex-1">
                <p className="text-white text-xs leading-relaxed">{item.title}</p>
                <div className="flex items-center space-x-3 mt-2">
                  <div className="flex items-center space-x-1">
                    {getTypeIcon(item.type)}
                    <span className="text-gray-400 text-xs">{item.source}</span>
                  </div>
                  <span className="text-gray-500 text-xs font-mono">{item.time}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
