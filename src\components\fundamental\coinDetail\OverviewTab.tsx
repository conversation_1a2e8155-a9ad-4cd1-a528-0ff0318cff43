
import { Scale, Coins, Code, LineChart } from "lucide-react";
import { ScoreCard } from "../ScoreCard";
import { HistoricalMetricsChart } from "../HistoricalMetricsChart";
import { FundamentalCoin } from "@/hooks/useFundamentalAnalysis";

interface OverviewTabProps {
  coin: FundamentalCoin;
  historicalData: any[];
  isLoadingDetails: boolean;
}

export function OverviewTab({ coin, historicalData, isLoadingDetails }: OverviewTabProps) {
  const { fundamentalScore } = coin;

  return (
    <div className="p-4 space-y-4">
      {fundamentalScore ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <ScoreCard 
            title="Overall" 
            score={fundamentalScore.total} 
            description="Combined fundamental rating" 
            icon={<Scale className="h-4 w-4" />} 
          />
          <ScoreCard 
            title="Tokenomics" 
            score={fundamentalScore.tokenomics} 
            description="Supply distribution & economics" 
            icon={<Coins className="h-4 w-4" />} 
          />
          <ScoreCard 
            title="Development" 
            score={fundamentalScore.development} 
            description="GitHub activity & team" 
            icon={<Code className="h-4 w-4" />} 
          />
          <ScoreCard 
            title="Utility" 
            score={fundamentalScore.utility} 
            description="Network usage & adoption" 
            icon={<LineChart className="h-4 w-4" />} 
          />
        </div>
      ) : (
        <div className="text-center py-12 text-muted-foreground">
          No fundamental score available
        </div>
      )}
      
      <div className="grid grid-cols-1 gap-4 mt-6">
        <HistoricalMetricsChart 
          data={historicalData} 
          coinId={coin.id} 
          coinName={coin.name}
          isLoading={isLoadingDetails} 
        />
      </div>
    </div>
  );
}
