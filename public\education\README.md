# Educational Assets

This directory contains all the educational illustrations and diagrams used in the CryptoVision Pro Education Hub.

## 📚 Tutorial Images

### Blockchain Fundamentals
- `blockchain-fundamentals.svg` - Interactive blockchain structure diagram showing blocks, hashing, and consensus

### DeFi Ecosystem
- `defi-ecosystem.svg` - Comprehensive DeFi ecosystem overview with protocols and connections
- `uniswap-diagram.svg` - Detailed Uniswap AMM protocol explanation
- `aave-diagram.svg` - Aave lending protocol architecture
- `compound-diagram.svg` - Compound money markets visualization

### Investment & Trading
- `crypto-investing.svg` - Investment strategies and portfolio allocation
- `trading-basics.svg` - Market dynamics, bull/bear markets, and trading strategies
- `trading-fees-analysis.svg` - Fee breakdown and optimization tips
- `order-types-chart.svg` - Different order types with visual examples

### Security & Wallets
- `wallet-security.svg` - Wallet types and security best practices

### Exchange Education
- `exchange-comparison.svg` - CEX vs DEX comparison
- `crypto-trading-dashboard.svg` - Trading interface overview
- `investment-strategies.svg` - Portfolio allocation and strategy comparison

## 🪙 Token Logos

### Major Cryptocurrencies
- `bitcoin-logo.svg` - Bitcoin (BTC) logo
- `ethereum-logo.svg` - Ethereum (ETH) logo  
- `cardano-logo.svg` - Cardano (ADA) logo

## 🎨 Design Principles

All educational assets follow these design principles:

1. **Consistency**: Unified color scheme using the app's design tokens
2. **Accessibility**: High contrast ratios and clear typography
3. **Scalability**: SVG format for crisp rendering at any size
4. **Educational Focus**: Clear visual hierarchy and informative content
5. **Dark Theme**: Optimized for the app's dark theme interface

## 🔧 Technical Details

- **Format**: SVG (Scalable Vector Graphics)
- **Color Scheme**: Matches app's Tailwind CSS design tokens
- **Optimization**: Lightweight and fast-loading
- **Fallback**: All images have proper error handling with `/placeholder.svg`

## 📝 Usage

These assets are referenced throughout the education components:

- Tutorial thumbnails and step illustrations
- Protocol diagrams and explanations
- Token deep-dive visuals
- Interactive learning materials

All images are served locally to ensure:
- ✅ Fast loading times
- ✅ Reliable availability
- ✅ Consistent user experience
- ✅ No external dependencies
- ✅ Privacy compliance
