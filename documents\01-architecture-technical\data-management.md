# 📊 Data Management

## Overview

CryptoVision Pro implements a sophisticated data management strategy that combines React Query for server state, React Context for global application state, and intelligent caching mechanisms to deliver optimal performance and user experience.

## 🔄 State Management Architecture

### **Multi-Layer State Management**
```
┌─────────────────────────────────────────────────────────────┐
│                    Application State                        │
├─────────────────────────────────────────────────────────────┤
│ Server State (React Query)  │  Global State (Context)      │
│ • API data caching          │  • User authentication       │
│ • Background refetching     │  • Theme preferences         │
│ • Optimistic updates        │  • UI state                  │
│ • Error handling            │  • Navigation state          │
├─────────────────────────────────────────────────────────────┤
│ Local State (useState/useReducer)                          │
│ • Component-specific state                                 │
│ • Form inputs and validation                               │
│ • UI interactions                                          │
└─────────────────────────────────────────────────────────────┘
```

### **React Query Configuration**
```typescript
// Query client configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,        // 5 minutes
      cacheTime: 30 * 60 * 1000,       // 30 minutes
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
      onError: (error) => {
        console.error('Mutation error:', error);
        toast.error('Operation failed. Please try again.');
      },
    },
  },
});
```

## 🗄️ Caching Strategy

### **Multi-Level Caching System**
```typescript
// Cache hierarchy
const cacheStrategy = {
  // Level 1: Memory Cache (React Query)
  memory: {
    marketData: { staleTime: 2 * 60 * 1000 },      // 2 minutes
    globalStats: { staleTime: 5 * 60 * 1000 },     // 5 minutes
    historicalData: { staleTime: 30 * 60 * 1000 }, // 30 minutes
    staticData: { staleTime: 24 * 60 * 60 * 1000 } // 24 hours
  },

  // Level 2: Local Storage Cache
  localStorage: {
    userPreferences: { duration: Infinity },
    educationProgress: { duration: 30 * 24 * 60 * 60 * 1000 }, // 30 days
    portfolioData: { duration: 7 * 24 * 60 * 60 * 1000 }       // 7 days
  },

  // Level 3: Service Worker Cache
  serviceWorker: {
    staticAssets: { duration: 30 * 24 * 60 * 60 * 1000 },      // 30 days
    apiResponses: { duration: 24 * 60 * 60 * 1000 }            // 24 hours
  }
};
```

### **Cache Implementation**
```typescript
// Smart cache utility
class SmartCache {
  private memoryCache = new Map<string, CacheEntry>();
  private localStorage = window.localStorage;

  async get<T>(key: string): Promise<T | null> {
    // Check memory cache first
    const memoryEntry = this.memoryCache.get(key);
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      return memoryEntry.data;
    }

    // Check localStorage
    const localEntry = this.getFromLocalStorage(key);
    if (localEntry && !this.isExpired(localEntry)) {
      // Promote to memory cache
      this.memoryCache.set(key, localEntry);
      return localEntry.data;
    }

    return null;
  }

  async set<T>(key: string, data: T, ttl: number): Promise<void> {
    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      ttl
    };

    // Store in memory
    this.memoryCache.set(key, entry);

    // Store in localStorage for persistence
    this.setInLocalStorage(key, entry);
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }
}
```

## 🔗 Data Flow Patterns

### **Query Hook Pattern**
```typescript
// Custom hook for market data
export const useMarketData = (coinIds: string[]) => {
  return useQuery({
    queryKey: ['marketData', coinIds],
    queryFn: () => fetchMarketData(coinIds),
    staleTime: 2 * 60 * 1000,
    select: (data) => {
      // Transform and normalize data
      return data.map(coin => ({
        ...coin,
        priceChangeFormatted: formatPriceChange(coin.price_change_percentage_24h),
        marketCapFormatted: formatMarketCap(coin.market_cap)
      }));
    },
    onError: (error) => {
      console.error('Market data fetch failed:', error);
      // Fallback to cached data
      return getCachedMarketData(coinIds);
    }
  });
};
```

### **Mutation Pattern**
```typescript
// Portfolio update mutation
export const useUpdatePortfolio = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updatePortfolioData,
    onMutate: async (newData) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['portfolio'] });

      // Snapshot previous value
      const previousPortfolio = queryClient.getQueryData(['portfolio']);

      // Optimistically update
      queryClient.setQueryData(['portfolio'], (old: any) => ({
        ...old,
        ...newData
      }));

      return { previousPortfolio };
    },
    onError: (err, newData, context) => {
      // Rollback on error
      queryClient.setQueryData(['portfolio'], context?.previousPortfolio);
    },
    onSettled: () => {
      // Refetch after mutation
      queryClient.invalidateQueries({ queryKey: ['portfolio'] });
    }
  });
};
```

### **Background Sync Pattern**
```typescript
// Background data synchronization
export const useBackgroundSync = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    const syncInterval = setInterval(() => {
      // Sync critical data in background
      queryClient.refetchQueries({
        queryKey: ['marketData'],
        type: 'active'
      });

      queryClient.refetchQueries({
        queryKey: ['globalStats'],
        type: 'active'
      });
    }, 30000); // Every 30 seconds

    return () => clearInterval(syncInterval);
  }, [queryClient]);
};
```

## 🎯 Data Normalization

### **Data Transformation Pipeline**
```typescript
// Data normalization utilities
export const normalizeMarketData = (rawData: any[]) => {
  return rawData.map(coin => ({
    id: coin.id,
    symbol: coin.symbol.toUpperCase(),
    name: coin.name,
    price: parseFloat(coin.current_price),
    priceChange24h: parseFloat(coin.price_change_percentage_24h),
    marketCap: parseInt(coin.market_cap),
    volume24h: parseInt(coin.total_volume),
    rank: parseInt(coin.market_cap_rank),
    image: coin.image,
    lastUpdated: new Date(coin.last_updated)
  }));
};

// Type-safe data validation
export const validateMarketData = (data: any): MarketData[] => {
  const schema = z.array(z.object({
    id: z.string(),
    symbol: z.string(),
    name: z.string(),
    price: z.number().positive(),
    priceChange24h: z.number(),
    marketCap: z.number().positive(),
    volume24h: z.number().positive(),
    rank: z.number().positive(),
    image: z.string().url(),
    lastUpdated: z.date()
  }));

  return schema.parse(data);
};
```

### **Data Aggregation**
```typescript
// Market statistics aggregation
export const aggregateMarketStats = (coins: MarketData[]) => {
  return {
    totalMarketCap: coins.reduce((sum, coin) => sum + coin.marketCap, 0),
    totalVolume24h: coins.reduce((sum, coin) => sum + coin.volume24h, 0),
    averagePriceChange: coins.reduce((sum, coin) => sum + coin.priceChange24h, 0) / coins.length,
    topGainers: coins
      .filter(coin => coin.priceChange24h > 0)
      .sort((a, b) => b.priceChange24h - a.priceChange24h)
      .slice(0, 10),
    topLosers: coins
      .filter(coin => coin.priceChange24h < 0)
      .sort((a, b) => a.priceChange24h - b.priceChange24h)
      .slice(0, 10)
  };
};
```

## 🔄 Real-Time Data Management

### **WebSocket Integration**
```typescript
// Real-time price updates
export const useRealTimePrices = (coinIds: string[]) => {
  const [prices, setPrices] = useState<Record<string, number>>({});
  const queryClient = useQueryClient();

  useEffect(() => {
    const ws = new WebSocket('wss://ws.coincap.io/prices?assets=' + coinIds.join(','));

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setPrices(prevPrices => ({ ...prevPrices, ...data }));

      // Update React Query cache
      Object.entries(data).forEach(([coinId, price]) => {
        queryClient.setQueryData(['coinPrice', coinId], price);
      });
    };

    return () => ws.close();
  }, [coinIds, queryClient]);

  return prices;
};
```

### **Polling Strategy**
```typescript
// Intelligent polling based on user activity
export const useAdaptivePolling = (queryKey: string[], baseInterval = 30000) => {
  const [isActive, setIsActive] = useState(true);
  const [interval, setInterval] = useState(baseInterval);

  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;
      setIsActive(isVisible);
      setInterval(isVisible ? baseInterval : baseInterval * 4); // Slower when hidden
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [baseInterval]);

  return useQuery({
    queryKey,
    queryFn: fetchData,
    refetchInterval: interval,
    enabled: isActive
  });
};
```

## 🛡️ Error Handling & Recovery

### **Error Boundary for Data**
```typescript
// Data error boundary
export const DataErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={<DataErrorFallback />}
      onError={(error, errorInfo) => {
        console.error('Data error:', error, errorInfo);
        // Report to error tracking service
        reportError(error, errorInfo);
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

const DataErrorFallback = () => (
  <div className="p-4 border border-red-200 rounded-lg bg-red-50">
    <h3 className="text-red-800 font-semibold">Data Loading Error</h3>
    <p className="text-red-600">Unable to load data. Using cached information.</p>
    <button 
      onClick={() => window.location.reload()}
      className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
    >
      Retry
    </button>
  </div>
);
```

### **Graceful Degradation**
```typescript
// Data with fallback
export const useDataWithFallback = <T>(
  queryKey: string[],
  queryFn: () => Promise<T>,
  fallbackData: T
) => {
  const query = useQuery({
    queryKey,
    queryFn,
    onError: (error) => {
      console.warn('Query failed, using fallback data:', error);
    }
  });

  return {
    ...query,
    data: query.data ?? fallbackData,
    isUsingFallback: !query.data && !!fallbackData
  };
};
```

## 📊 Performance Monitoring

### **Cache Performance Metrics**
```typescript
// Cache hit rate monitoring
export const useCacheMetrics = () => {
  const [metrics, setMetrics] = useState({
    hitRate: 0,
    missRate: 0,
    totalRequests: 0
  });

  const trackCacheHit = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      hitRate: prev.hitRate + 1,
      totalRequests: prev.totalRequests + 1
    }));
  }, []);

  const trackCacheMiss = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      missRate: prev.missRate + 1,
      totalRequests: prev.totalRequests + 1
    }));
  }, []);

  return { metrics, trackCacheHit, trackCacheMiss };
};
```

This data management strategy ensures optimal performance, reliability, and user experience while maintaining data consistency across the application.
