
import { TableRow, TableCell } from "@/components/ui/table";
import { ScoreCell } from "./ScoreCell";
import { PriceChangeIndicator } from "./PriceChangeIndicator";
import type { AssetScore } from "@/types/rating";

interface AssetTableRowProps {
  asset: AssetScore;
}

export function AssetTableRow({ asset }: AssetTableRowProps) {
  return (
    <TableRow>
      <TableCell>
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
            {asset.symbol.charAt(0)}
          </div>
          <div>
            <div className="font-medium">{asset.name}</div>
            <div className="text-xs text-muted-foreground">{asset.symbol}</div>
          </div>
        </div>
      </TableCell>
      <TableCell>${asset.price.toLocaleString()}</TableCell>
      <TableCell>
        <PriceChangeIndicator changePercentage={asset.change24h} />
      </TableCell>
      <TableCell><ScoreCell score={asset.scores.technical} /></TableCell>
      <TableCell><ScoreCell score={asset.scores.onChain} /></TableCell>
      <TableCell><ScoreCell score={asset.scores.social} /></TableCell>
      <TableCell><ScoreCell score={asset.scores.fundamental} /></TableCell>
      <TableCell>
        <span className="font-bold px-3 py-1 rounded-full bg-primary/10 text-primary">
          {asset.overallScore.toFixed(1)}
        </span>
      </TableCell>
    </TableRow>
  );
}
