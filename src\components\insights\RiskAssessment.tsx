
import { RiskAssessment } from "@/types/aiInsights";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ShieldAlert, Check, AlertTriangle, Info, Shield } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface RiskAssessmentProps {
  assessments: RiskAssessment[];
  isLoading: boolean;
}

export default function RiskAssessmentCard({ assessments, isLoading }: RiskAssessmentProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShieldAlert className="h-5 w-5" />
            Risk Assessment
          </CardTitle>
          <CardDescription>AI-powered risk scoring and analysis</CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!assessments.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShieldAlert className="h-5 w-5" />
            Risk Assessment
          </CardTitle>
          <CardDescription>AI-powered risk scoring and analysis</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-8 text-center text-muted-foreground">
            No risk assessment data available for this asset
          </div>
        </CardContent>
      </Card>
    );
  }

  // Display the first assessment
  const assessment = assessments[0];
  
  const getColorForScore = (score: number) => {
    if (score < 25) return "text-green-500";
    if (score < 40) return "text-emerald-500";
    if (score < 60) return "text-yellow-500";
    if (score < 75) return "text-orange-500";
    return "text-red-500";
  };
  
  const getProgressColor = (score: number) => {
    if (score < 25) return "bg-green-500";
    if (score < 40) return "bg-emerald-500";
    if (score < 60) return "bg-yellow-500";
    if (score < 75) return "bg-orange-500";
    return "bg-red-500";
  };
  
  const getRiskIcon = (score: number) => {
    if (score < 40) {
      return <Check className="h-5 w-5 text-green-500" />;
    } else if (score < 70) {
      return <Info className="h-5 w-5 text-yellow-500" />;
    } else {
      return <AlertTriangle className="h-5 w-5 text-red-500" />;
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShieldAlert className="h-5 w-5" />
          Risk Assessment
        </CardTitle>
        <CardDescription>AI-powered risk scoring and analysis for {assessment.name} ({assessment.symbol})</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-6 items-center sm:items-start">
          <div className="flex flex-col items-center">
            <div className="relative h-40 w-40">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-5xl font-bold">
                  {assessment.overallRisk.score}
                </div>
              </div>
              <svg 
                className="h-40 w-40" 
                viewBox="0 0 100 100"
              >
                <circle 
                  cx="50" 
                  cy="50" 
                  r="45" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="10" 
                  strokeDasharray={`${assessment.overallRisk.score * 2.83} 283`} 
                  strokeDashoffset="-70.75" 
                  transform="rotate(-90, 50, 50)"
                  className={cn(
                    "opacity-20",
                    assessment.overallRisk.color === "green" && "text-green-500",
                    assessment.overallRisk.color === "emerald" && "text-emerald-500",
                    assessment.overallRisk.color === "yellow" && "text-yellow-500",
                    assessment.overallRisk.color === "orange" && "text-orange-500",
                    assessment.overallRisk.color === "red" && "text-red-500"
                  )}
                />
              </svg>
            </div>
            <div className="mt-2 text-xl font-medium">
              <span className={cn(
                getColorForScore(assessment.overallRisk.score)
              )}>
                {assessment.overallRisk.label} Risk
              </span>
            </div>
          </div>
          
          <div className="flex-1">
            <h3 className="text-lg font-medium mb-4">Risk Factors</h3>
            <div className="space-y-4">
              {assessment.factors.map((factor, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between">
                    <div className="text-sm font-medium">{factor.name}</div>
                    <div className={cn("text-sm", getColorForScore(factor.score))}>
                      {factor.score}/100
                    </div>
                  </div>
                  <Progress 
                    value={factor.score} 
                    max={100} 
                    className={cn("h-2", getProgressColor(factor.score))}
                  />
                  <div className="text-xs text-muted-foreground">{factor.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-2">Recommendations</h3>
          <div className="space-y-2">
            {assessment.recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start gap-2">
                {getRiskIcon(assessment.overallRisk.score)}
                <div>{recommendation}</div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
