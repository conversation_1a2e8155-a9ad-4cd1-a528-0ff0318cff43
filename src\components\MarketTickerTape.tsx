
import { useEffect, useState } from "react";
import { TrendingUp, TrendingDown } from "lucide-react";

interface TickerItem {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
}

export default function MarketTickerTape() {
  const [tickerData, setTickerData] = useState<TickerItem[]>([
    { symbol: "BTC", price: 67420, change: 1240, changePercent: 1.87 },
    { symbol: "ETH", price: 3845, change: -125, changePercent: -3.15 },
    { symbol: "BNB", price: 635, change: 18, changePercent: 2.91 },
    { symbol: "SOL", price: 178, change: 8.5, changePercent: 5.01 },
    { symbol: "ADA", price: 0.89, change: -0.03, changePercent: -3.27 },
    { symbol: "DOT", price: 7.65, change: 0.45, changePercent: 6.25 },
    { symbol: "MATIC", price: 1.23, change: 0.08, changePercent: 6.96 },
    { symbol: "LINK", price: 15.47, change: -0.67, changePercent: -4.15 }
  ]);

  useEffect(() => {
    const interval = setInterval(() => {
      setTickerData(prev => prev.map(item => ({
        ...item,
        price: item.price + (Math.random() - 0.5) * item.price * 0.001,
        change: item.change + (Math.random() - 0.5) * 10,
        changePercent: item.changePercent + (Math.random() - 0.5) * 0.5
      })));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="bg-black border-b border-gray-800 overflow-hidden">
      <div className="flex animate-scroll whitespace-nowrap py-2">
        {[...tickerData, ...tickerData].map((item, index) => (
          <div key={`${item.symbol}-${index}`} className="inline-flex items-center space-x-4 px-8 flex-shrink-0">
            <span className="text-orange-500 font-bold text-sm">{item.symbol}</span>
            <span className="text-white font-mono text-sm">
              ${item.price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
            <div className={`flex items-center space-x-1 ${item.changePercent >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {item.changePercent >= 0 ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              <span className="font-mono text-xs">
                {item.changePercent >= 0 ? '+' : ''}{item.changePercent.toFixed(2)}%
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
