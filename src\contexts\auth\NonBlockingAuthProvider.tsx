import React, { createContext, useState, useEffect, ReactNode } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { AuthContextType, Profile } from './types';
import { fetchUserProfile, updateUserProfile } from './profileUtils';
import { useAuthActions } from './authActions';

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const NonBlockingAuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(false); // Start with false to prevent blocking
  const { toast } = useToast();
  const { signIn, signUp, signOut } = useAuthActions();

  useEffect(() => {
    let isMounted = true;

    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, newSession) => {
        console.log("Auth state change event:", event);
        
        if (!isMounted) return;
        
        setSession(newSession);
        setUser(newSession?.user ?? null);
        
        if (newSession?.user) {
          // Fetch profile asynchronously without blocking
          handleFetchProfile(newSession.user.id).catch(error => {
            console.error("Error fetching profile:", error);
          });
        } else {
          setProfile(null);
        }
      }
    );

    // Check for existing session asynchronously without blocking
    const checkSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        console.log("Initial session check:", session ? "Found session" : "No session");
        
        if (!isMounted) return;
        
        setSession(session);
        setUser(session?.user ?? null);
        
        if (session?.user) {
          handleFetchProfile(session.user.id).catch(error => {
            console.error("Error fetching profile:", error);
          });
        }
      } catch (error) {
        console.error("Error checking auth session:", error);
      }
    };
    
    // Don't await this - let it run in background
    checkSession();

    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const handleFetchProfile = async (userId: string) => {
    try {
      const profileData = await fetchUserProfile(userId);
      if (profileData) {
        setProfile(profileData);
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    try {
      if (!user) throw new Error('User not authenticated');
      
      await updateUserProfile(user.id, updates);
      setProfile(prev => prev ? { ...prev, ...updates } : null);
      
      toast({
        title: 'Profile updated',
        description: 'Your profile has been successfully updated.',
      });
    } catch (error: any) {
      toast({
        title: 'Update failed',
        description: error.message || 'An error occurred while updating your profile.',
        variant: 'destructive',
      });
    }
  };

  const value = {
    session,
    user,
    profile,
    isLoading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
