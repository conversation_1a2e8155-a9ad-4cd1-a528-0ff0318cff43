
import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>H<PERSON>er, Card<PERSON><PERSON>le, CardContent, CardDescription, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts";
import { <PERSON><PERSON><PERSON>, <PERSON>tings, <PERSON>rkles, Refresh<PERSON>w, Save, <PERSON>R<PERSON>, Calculator } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface StrategyParams {
  initialInvestment: number;
  investmentPeriod: number;
  investmentFrequency: string;
  riskTolerance: number;
  rebalancingPeriod: string;
  includeDCA: boolean;
  includeStaking: boolean;
  stakingAPR: number;
  assetAllocation: {
    bitcoin: number;
    ethereum: number;
    altcoins: number;
    stablecoins: number;
  };
  takeProfit: number;
  stopLoss: number;
}

interface BacktestResult {
  date: string;
  portfolioValue: number;
  benchmark: number;
}

export default function StrategyBuilder() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("parameters");
  const [strategy, setStrategy] = useState<StrategyParams>({
    initialInvestment: 10000,
    investmentPeriod: 12,
    investmentFrequency: "monthly",
    riskTolerance: 3,
    rebalancingPeriod: "quarterly",
    includeDCA: true,
    includeStaking: false,
    stakingAPR: 5,
    assetAllocation: {
      bitcoin: 40,
      ethereum: 30,
      altcoins: 20,
      stablecoins: 10,
    },
    takeProfit: 20,
    stopLoss: 10,
  });

  const [backtestResults, setBacktestResults] = useState<BacktestResult[]>([]);
  const [backtestSummary, setBacktestSummary] = useState({
    totalReturn: 0,
    annualizedReturn: 0,
    maxDrawdown: 0,
    volatility: 0,
    sharpeRatio: 0,
    winRate: 0,
  });

  const [isLoading, setIsLoading] = useState(false);

  // Generate mock backtest data when parameters change
  useEffect(() => {
    if (activeTab === "backtest" && backtestResults.length === 0) {
      generateBacktestData();
    }
  }, [activeTab]);

  const handleAssetAllocationChange = (asset: keyof typeof strategy.assetAllocation, value: number) => {
    const newAllocation = { ...strategy.assetAllocation, [asset]: value };
    
    // Calculate the total
    const total = Object.values(newAllocation).reduce((sum, val) => sum + val, 0);
    
    // If total is greater than 100, adjust other allocations proportionally
    if (total > 100) {
      const excessPercentage = total - 100;
      const otherAssets = Object.keys(newAllocation).filter(a => a !== asset) as Array<keyof typeof strategy.assetAllocation>;
      
      const currentOtherTotal = otherAssets.reduce((sum, a) => sum + newAllocation[a], 0);
      
      if (currentOtherTotal > 0) {
        otherAssets.forEach(a => {
          const proportion = newAllocation[a] / currentOtherTotal;
          newAllocation[a] = Math.max(0, Math.round(newAllocation[a] - (excessPercentage * proportion)));
        });
      } else {
        newAllocation[asset] = 100;
      }
    }
    
    setStrategy({
      ...strategy,
      assetAllocation: newAllocation
    });
  };

  const generateBacktestData = () => {
    setIsLoading(true);
    
    // Simulate API delay
    setTimeout(() => {
      const results: BacktestResult[] = [];
      let currentPortfolioValue = strategy.initialInvestment;
      let benchmarkValue = strategy.initialInvestment;
      
      // Generate some mock data
      const months = strategy.investmentPeriod;
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);
      
      for (let i = 0; i <= months; i++) {
        const currentDate = new Date(startDate);
        currentDate.setMonth(startDate.getMonth() + i);
        
        // Random growth factor based on risk tolerance and asset allocation
        const growthFactor = (1 + (Math.random() * 0.05 - 0.01) * strategy.riskTolerance);
        const benchmarkGrowthFactor = 1 + (Math.random() * 0.03); // Market average
        
        // Apply DCA if enabled
        if (strategy.includeDCA && i > 0) {
          const monthlyContribution = strategy.initialInvestment * 0.05; // 5% monthly contribution
          currentPortfolioValue += monthlyContribution;
        }
        
        // Apply staking rewards if enabled
        if (strategy.includeStaking) {
          const stakingReward = (currentPortfolioValue * (strategy.stakingAPR / 100)) / 12;
          currentPortfolioValue += stakingReward;
        }
        
        // Calculate new values
        currentPortfolioValue = Math.round(currentPortfolioValue * growthFactor);
        benchmarkValue = Math.round(benchmarkValue * benchmarkGrowthFactor);
        
        results.push({
          date: currentDate.toISOString().slice(0, 7), // YYYY-MM format
          portfolioValue: currentPortfolioValue,
          benchmark: benchmarkValue
        });
      }
      
      setBacktestResults(results);
      
      // Calculate summary statistics
      const initialValue = results[0].portfolioValue;
      const finalValue = results[results.length - 1].portfolioValue;
      const totalReturn = ((finalValue - initialValue) / initialValue) * 100;
      const annualizedReturn = ((Math.pow(finalValue / initialValue, 1 / (months/12))) - 1) * 100;
      
      // Calculate max drawdown
      let maxDrawdown = 0;
      let peak = results[0].portfolioValue;
      
      for (const result of results) {
        if (result.portfolioValue > peak) {
          peak = result.portfolioValue;
        }
        
        const drawdown = (peak - result.portfolioValue) / peak * 100;
        if (drawdown > maxDrawdown) {
          maxDrawdown = drawdown;
        }
      }
      
      // Calculate other metrics
      setBacktestSummary({
        totalReturn,
        annualizedReturn,
        maxDrawdown,
        volatility: Math.random() * 15 + 5, // Mock volatility between 5-20%
        sharpeRatio: (annualizedReturn - 2) / (Math.random() * 10 + 5), // Mock Sharpe ratio
        winRate: Math.random() * 30 + 50, // Mock win rate between 50-80%
      });
      
      setIsLoading(false);
      
      toast({
        title: "Backtest Complete",
        description: `Your strategy has been tested over ${months} months with a ${totalReturn.toFixed(2)}% total return.`,
      });
    }, 1500);
  };

  const saveStrategy = () => {
    toast({
      title: "Strategy Saved",
      description: "Your investment strategy has been saved successfully.",
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl">Investment Strategy Builder</CardTitle>
              <CardDescription>Build, test, and optimize your crypto investment strategy</CardDescription>
            </div>
            <Badge variant="outline" className="ml-2">Educational</Badge>
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="parameters">
                <Settings className="mr-2 h-4 w-4" />
                Parameters
              </TabsTrigger>
              <TabsTrigger value="backtest">
                <ChartLine className="mr-2 h-4 w-4" />
                Backtest
              </TabsTrigger>
              <TabsTrigger value="insights">
                <Sparkles className="mr-2 h-4 w-4" />
                Insights
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="parameters" className="space-y-6">
              {/* Initial Investment */}
              <div>
                <Label htmlFor="initialInvestment">Initial Investment ($)</Label>
                <Input
                  id="initialInvestment"
                  type="number"
                  value={strategy.initialInvestment}
                  onChange={(e) => setStrategy({
                    ...strategy, 
                    initialInvestment: parseInt(e.target.value) || 0
                  })}
                  className="mt-1"
                />
              </div>
              
              {/* Investment Period */}
              <div>
                <Label htmlFor="investmentPeriod">Investment Period (months)</Label>
                <Input
                  id="investmentPeriod"
                  type="number"
                  min={1}
                  max={60}
                  value={strategy.investmentPeriod}
                  onChange={(e) => setStrategy({
                    ...strategy, 
                    investmentPeriod: parseInt(e.target.value) || 1
                  })}
                  className="mt-1"
                />
              </div>
              
              {/* Risk Tolerance */}
              <div>
                <Label>Risk Tolerance (1-5)</Label>
                <div className="flex items-center gap-4 mt-1">
                  <span className="text-sm">Conservative</span>
                  <Slider 
                    value={[strategy.riskTolerance]}
                    min={1} 
                    max={5} 
                    step={1} 
                    onValueChange={(value) => setStrategy({
                      ...strategy,
                      riskTolerance: value[0]
                    })}
                    className="flex-1"
                  />
                  <span className="text-sm">Aggressive</span>
                </div>
              </div>

              {/* Asset Allocation */}
              <div className="space-y-4">
                <Label>Asset Allocation</Label>
                
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Bitcoin</span>
                      <span className="text-sm font-medium">{strategy.assetAllocation.bitcoin}%</span>
                    </div>
                    <Slider 
                      value={[strategy.assetAllocation.bitcoin]}
                      min={0} 
                      max={100} 
                      step={5}
                      onValueChange={(value) => handleAssetAllocationChange("bitcoin", value[0])}
                    />
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Ethereum</span>
                      <span className="text-sm font-medium">{strategy.assetAllocation.ethereum}%</span>
                    </div>
                    <Slider 
                      value={[strategy.assetAllocation.ethereum]}
                      min={0} 
                      max={100} 
                      step={5}
                      onValueChange={(value) => handleAssetAllocationChange("ethereum", value[0])}
                    />
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Altcoins</span>
                      <span className="text-sm font-medium">{strategy.assetAllocation.altcoins}%</span>
                    </div>
                    <Slider 
                      value={[strategy.assetAllocation.altcoins]}
                      min={0} 
                      max={100} 
                      step={5}
                      onValueChange={(value) => handleAssetAllocationChange("altcoins", value[0])}
                    />
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Stablecoins</span>
                      <span className="text-sm font-medium">{strategy.assetAllocation.stablecoins}%</span>
                    </div>
                    <Slider 
                      value={[strategy.assetAllocation.stablecoins]}
                      min={0} 
                      max={100} 
                      step={5}
                      onValueChange={(value) => handleAssetAllocationChange("stablecoins", value[0])}
                    />
                  </div>
                </div>
                
                <div className="text-sm text-right">
                  Total: {Object.values(strategy.assetAllocation).reduce((sum, val) => sum + val, 0)}%
                </div>
              </div>
              
              <Separator />
              
              {/* Advanced Options */}
              <div>
                <h3 className="text-sm font-medium mb-3">Advanced Options</h3>
                
                <div className="space-y-4">
                  {/* Investment Frequency */}
                  <div className="flex items-center justify-between">
                    <Label htmlFor="investmentFrequency">Investment Frequency</Label>
                    <Select 
                      value={strategy.investmentFrequency}
                      onValueChange={(value) => setStrategy({
                        ...strategy,
                        investmentFrequency: value
                      })}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="biweekly">Bi-weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Rebalancing Period */}
                  <div className="flex items-center justify-between">
                    <Label htmlFor="rebalancingPeriod">Rebalancing Period</Label>
                    <Select 
                      value={strategy.rebalancingPeriod}
                      onValueChange={(value) => setStrategy({
                        ...strategy,
                        rebalancingPeriod: value
                      })}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="biannually">Bi-annually</SelectItem>
                        <SelectItem value="annually">Annually</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Dollar-Cost Averaging */}
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="includeDCA">Dollar-Cost Averaging</Label>
                      <p className="text-sm text-muted-foreground">
                        Regular investment at fixed intervals
                      </p>
                    </div>
                    <Switch 
                      id="includeDCA"
                      checked={strategy.includeDCA}
                      onCheckedChange={(checked) => setStrategy({
                        ...strategy,
                        includeDCA: checked
                      })}
                    />
                  </div>
                  
                  {/* Staking */}
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="includeStaking">Include Staking</Label>
                      <p className="text-sm text-muted-foreground">
                        Earn passive income through staking
                      </p>
                    </div>
                    <Switch 
                      id="includeStaking"
                      checked={strategy.includeStaking}
                      onCheckedChange={(checked) => setStrategy({
                        ...strategy,
                        includeStaking: checked
                      })}
                    />
                  </div>
                  
                  {/* Staking APR */}
                  {strategy.includeStaking && (
                    <div>
                      <Label htmlFor="stakingAPR">Staking APR (%)</Label>
                      <Input
                        id="stakingAPR"
                        type="number"
                        min={0}
                        max={30}
                        value={strategy.stakingAPR}
                        onChange={(e) => setStrategy({
                          ...strategy,
                          stakingAPR: parseFloat(e.target.value) || 0
                        })}
                        className="mt-1"
                      />
                    </div>
                  )}
                  
                  {/* Take Profit */}
                  <div>
                    <Label htmlFor="takeProfit">Take Profit (%)</Label>
                    <Input
                      id="takeProfit"
                      type="number"
                      min={0}
                      max={100}
                      value={strategy.takeProfit}
                      onChange={(e) => setStrategy({
                        ...strategy,
                        takeProfit: parseFloat(e.target.value) || 0
                      })}
                      className="mt-1"
                    />
                  </div>
                  
                  {/* Stop Loss */}
                  <div>
                    <Label htmlFor="stopLoss">Stop Loss (%)</Label>
                    <Input
                      id="stopLoss"
                      type="number"
                      min={0}
                      max={100}
                      value={strategy.stopLoss}
                      onChange={(e) => setStrategy({
                        ...strategy,
                        stopLoss: parseFloat(e.target.value) || 0
                      })}
                      className="mt-1"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="backtest" className="space-y-6">
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-10">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
                  <p className="text-muted-foreground">Running backtest simulation...</p>
                </div>
              ) : backtestResults.length > 0 ? (
                <>
                  {/* Results Chart */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Performance Chart</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={backtestResults}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip formatter={(value) => [`$${value}`, '']} />
                            <Legend />
                            <Line 
                              type="monotone" 
                              dataKey="portfolioValue" 
                              name="Your Strategy" 
                              stroke="#8884d8" 
                              activeDot={{ r: 8 }}
                              strokeWidth={2} 
                            />
                            <Line 
                              type="monotone" 
                              dataKey="benchmark" 
                              name="Market Benchmark" 
                              stroke="#82ca9d" 
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* Performance Metrics */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Performance Metrics</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="p-4 border rounded-lg">
                          <p className="text-sm text-muted-foreground">Total Return</p>
                          <p className={`text-xl font-semibold ${backtestSummary.totalReturn >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {backtestSummary.totalReturn.toFixed(2)}%
                          </p>
                        </div>
                        
                        <div className="p-4 border rounded-lg">
                          <p className="text-sm text-muted-foreground">Annualized Return</p>
                          <p className={`text-xl font-semibold ${backtestSummary.annualizedReturn >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {backtestSummary.annualizedReturn.toFixed(2)}%
                          </p>
                        </div>
                        
                        <div className="p-4 border rounded-lg">
                          <p className="text-sm text-muted-foreground">Max Drawdown</p>
                          <p className="text-xl font-semibold text-red-500">
                            {backtestSummary.maxDrawdown.toFixed(2)}%
                          </p>
                        </div>
                        
                        <div className="p-4 border rounded-lg">
                          <p className="text-sm text-muted-foreground">Volatility</p>
                          <p className="text-xl font-semibold">
                            {backtestSummary.volatility.toFixed(2)}%
                          </p>
                        </div>
                        
                        <div className="p-4 border rounded-lg">
                          <p className="text-sm text-muted-foreground">Sharpe Ratio</p>
                          <p className="text-xl font-semibold">
                            {backtestSummary.sharpeRatio.toFixed(2)}
                          </p>
                        </div>
                        
                        <div className="p-4 border rounded-lg">
                          <p className="text-sm text-muted-foreground">Win Rate</p>
                          <p className="text-xl font-semibold">
                            {backtestSummary.winRate.toFixed(2)}%
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <div className="flex justify-center">
                    <Button 
                      variant="outline" 
                      className="flex items-center gap-2"
                      onClick={generateBacktestData}
                    >
                      <RefreshCw className="h-4 w-4" />
                      Run Backtest Again
                    </Button>
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center py-10 text-center">
                  <Calculator className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Backtest Data</h3>
                  <p className="text-muted-foreground mb-6">
                    Run a backtest to see how your strategy would have performed.
                  </p>
                  <Button onClick={generateBacktestData}>
                    Run Backtest
                  </Button>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="insights" className="space-y-6">
              {backtestResults.length > 0 ? (
                <>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Strategy Insights</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="p-4 bg-muted rounded-lg">
                        <h3 className="font-medium mb-2">Performance Summary</h3>
                        <p className="text-sm">
                          {backtestSummary.totalReturn > 0 
                            ? `Your strategy has outperformed the market benchmark by ${(backtestSummary.totalReturn - (backtestResults[backtestResults.length-1].benchmark - backtestResults[0].benchmark) / backtestResults[0].benchmark * 100).toFixed(2)}%.` 
                            : "Your strategy has underperformed compared to the market benchmark."}
                        </p>
                      </div>
                      
                      <div className="p-4 bg-muted rounded-lg">
                        <h3 className="font-medium mb-2">Risk Assessment</h3>
                        <p className="text-sm">
                          With a maximum drawdown of {backtestSummary.maxDrawdown.toFixed(2)}% and volatility of {backtestSummary.volatility.toFixed(2)}%, 
                          {backtestSummary.volatility > 15 
                            ? " this strategy shows high volatility and might be suitable for investors with higher risk tolerance." 
                            : " this strategy shows moderate volatility and could be suitable for balanced portfolios."}
                        </p>
                      </div>
                      
                      <div className="p-4 bg-muted rounded-lg">
                        <h3 className="font-medium mb-2">Asset Allocation Analysis</h3>
                        <p className="text-sm">
                          Your current allocation of {strategy.assetAllocation.bitcoin}% Bitcoin, {strategy.assetAllocation.ethereum}% Ethereum, 
                          {strategy.assetAllocation.altcoins}% altcoins, and {strategy.assetAllocation.stablecoins}% stablecoins 
                          {strategy.assetAllocation.stablecoins > 20 
                            ? " provides good stability but might limit upside potential." 
                            : strategy.assetAllocation.bitcoin + strategy.assetAllocation.ethereum > 70 
                              ? " focuses heavily on blue-chip cryptocurrencies, which may reduce diversification benefits." 
                              : " provides a balanced exposure to the cryptocurrency market."}
                        </p>
                      </div>
                      
                      <div className="p-4 bg-muted rounded-lg">
                        <h3 className="font-medium mb-2">Improvement Suggestions</h3>
                        <ul className="text-sm space-y-2 ml-4 list-disc">
                          {strategy.includeDCA ? (
                            <li>Your dollar-cost averaging strategy helps reduce market timing risk.</li>
                          ) : (
                            <li>Consider enabling dollar-cost averaging to reduce market timing risk.</li>
                          )}
                          
                          {strategy.includeStaking ? (
                            <li>Staking at {strategy.stakingAPR}% APR provides additional passive income.</li>
                          ) : (
                            <li>Consider staking to generate additional passive income.</li>
                          )}
                          
                          {strategy.rebalancingPeriod === "quarterly" || strategy.rebalancingPeriod === "biannually" || strategy.rebalancingPeriod === "annually" ? (
                            <li>Your {strategy.rebalancingPeriod} rebalancing schedule helps maintain your target allocation.</li>
                          ) : (
                            <li>Consider less frequent rebalancing to reduce transaction costs.</li>
                          )}
                          
                          {strategy.assetAllocation.stablecoins < 10 && (
                            <li>Consider increasing your stablecoin allocation to have dry powder for market downturns.</li>
                          )}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Educational Concepts</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h3 className="font-medium mb-2">Dollar-Cost Averaging (DCA)</h3>
                        <p className="text-sm text-muted-foreground">
                          An investment strategy where you divide your total investment amount into periodic purchases, 
                          reducing the impact of volatility on the overall purchase.
                        </p>
                      </div>
                      
                      <div>
                        <h3 className="font-medium mb-2">Portfolio Rebalancing</h3>
                        <p className="text-sm text-muted-foreground">
                          The process of realigning the weightings of a portfolio's assets to maintain 
                          the original or desired level of asset allocation or risk.
                        </p>
                      </div>
                      
                      <div>
                        <h3 className="font-medium mb-2">Sharpe Ratio</h3>
                        <p className="text-sm text-muted-foreground">
                          Measures the performance of an investment compared to a risk-free asset, 
                          after adjusting for its risk. A higher Sharpe ratio indicates better risk-adjusted performance.
                        </p>
                      </div>
                      
                      <div>
                        <h3 className="font-medium mb-2">Maximum Drawdown</h3>
                        <p className="text-sm text-muted-foreground">
                          The maximum observed loss from a peak to a trough of a portfolio, before a new peak is attained. 
                          It's an indicator of downside risk over a specified time period.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center py-10 text-center">
                  <Sparkles className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Insights Available</h3>
                  <p className="text-muted-foreground mb-6">
                    Run a backtest first to generate insights and recommendations for your strategy.
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setActiveTab("backtest");
                    }}
                  >
                    Go to Backtest <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
        
        <CardFooter>
          <div className="flex justify-between w-full">
            <Button variant="outline">
              Reset
            </Button>
            <Button onClick={saveStrategy}>
              <Save className="mr-2 h-4 w-4" />
              Save Strategy
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
