
import { Lightbulb } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function PredictionLoadingState() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5" />
          Price Prediction
        </CardTitle>
        <CardDescription>ML-generated price forecast with confidence intervals</CardDescription>
      </CardHeader>
      <CardContent>
        <Skeleton className="h-[300px] w-full" />
      </CardContent>
    </Card>
  );
}
