
import { fetchTopCoins, fetchGlobalData, fetchTrendingCoins } from "./coinMarketData";
import { cacheResponse, handleApiError } from "./coinGeckoClient";
import { generateStories } from "./newsUtils";

// Calculate market sentiment based on top coins' performance
const calculateMarketSentiment = (topCoins: any[], globalData: any) => {
  // Calculate overall sentiment based on top coins' performance
  const avgPriceChange = topCoins.reduce((sum, coin) => 
    sum + (coin.price_change_percentage_24h || 0), 0) / topCoins.length;
  
  // Market cap changes from global data
  const marketCapChange = globalData?.data?.market_cap_change_percentage_24h_usd || 0;
  
  // Weighted average with some randomness for realism
  return (avgPriceChange * 0.6 + marketCapChange * 0.4) / 20 + (Math.random() * 0.2 - 0.1);
};

// Calculate sentiment for a specific coin
const calculateCoinSentiment = (coin: any) => {
  const priceChange = coin.price_change_percentage_24h || 0;
  const volumeRatio = coin.total_volume / (coin.market_cap || 1); // Volume to market cap ratio
  
  // Calculate sentiment based on price action and volume
  return priceChange / 20 + (volumeRatio - 0.1) + (Math.random() * 0.3 - 0.15);
};

// Generate sentiment analysis data (mock+real hybrid)
export const generateSentimentData = async () => {
  try {
    // Get real market data to inform our sentiment generation
    const topCoins = await fetchTopCoins(10);
    const globalData = await fetchGlobalData();
    const trendingCoins = await fetchTrendingCoins();
    
    // Use real data to generate semi-realistic sentiment metrics
    const overallMarketSentiment = calculateMarketSentiment(topCoins, globalData);
    
    const sentimentByAsset = topCoins.map((coin: any) => {
      const sentiment = calculateCoinSentiment(coin);
      return {
        asset: coin.symbol.toUpperCase(),
        current: sentiment,
        change24h: (coin.price_change_percentage_24h || 0) / 10,
        sentiment: sentiment > 0.2 ? 'positive' : sentiment < -0.2 ? 'negative' : 'neutral'
      };
    });
    
    // Get the last 14 days for the sentiment trend (mock data)
    const sentimentTrend = Array(14).fill(0).map((_, i) => {
      // Slight upward trend overall
      const baseValue = -0.1 + (i / 14) * 0.5;
      return {
        date: new Date(Date.now() - (13-i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: baseValue + (Math.random() * 0.4 - 0.2) // Add some randomness
      };
    });
    
    const data = {
      overallMarketSentiment,
      sentimentByAsset,
      topPositiveStories: generateStories(3, 'positive', topCoins, trendingCoins),
      topNegativeStories: generateStories(3, 'negative', topCoins, trendingCoins),
      sentimentTrend
    };
    
    cacheResponse("sentiment", data);
    return data;
  } catch (error) {
    return handleApiError(error, {
      key: "sentiment",
      data: null
    });
  }
};
