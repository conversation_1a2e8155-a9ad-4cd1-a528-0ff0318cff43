
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/auth/useAuth';
import { supabase } from '@/integrations/supabase/client';

export function useAdminAuth() {
  const { user } = useAuth();

  const { data: isAdmin, isLoading } = useQuery({
    queryKey: ['isAdmin', user?.id],
    queryFn: async () => {
      if (!user) return false;
      const { data } = await supabase.rpc('is_admin', { user_uuid: user.id });
      return data;
    },
    enabled: !!user,
  });

  const { data: adminLevel } = useQuery({
    queryKey: ['adminLevel', user?.id],
    queryFn: async () => {
      if (!user || !isAdmin) return null;
      const { data } = await supabase
        .from('admin_users')
        .select('admin_level')
        .eq('user_id', user.id)
        .single();
      return data?.admin_level || null;
    },
    enabled: !!user && isAdmin,
  });

  return {
    isAdmin: isAdmin || false,
    adminLevel,
    isLoading,
    user
  };
}
