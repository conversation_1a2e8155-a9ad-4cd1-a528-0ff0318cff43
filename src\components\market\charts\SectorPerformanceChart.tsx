import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { TrendingUp, TrendingDown, DollarSign, BarChart3 } from 'lucide-react';
import { SectorPerformance } from '@/services/api/enhancedMarketInsights';

interface SectorPerformanceChartProps {
  sectors: SectorPerformance[];
  loading?: boolean;
}

export const SectorPerformanceChart: React.FC<SectorPerformanceChartProps> = ({ 
  sectors, 
  loading 
}) => {
  const [selectedSector, setSelectedSector] = useState<SectorPerformance | null>(null);

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle>Sector Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96 bg-gray-200 rounded animate-pulse" />
        </CardContent>
      </Card>
    );
  }

  if (!sectors || sectors.length === 0) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle>Sector Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96 text-gray-500">
            No sector data available
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (value: number) => {
    if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    return `$${value.toLocaleString()}`;
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? '#10b981' : '#ef4444';
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? 
      <TrendingUp className="w-4 h-4 text-green-600" /> : 
      <TrendingDown className="w-4 h-4 text-red-600" />;
  };

  // Prepare data for charts
  const chartData = sectors.map(sector => ({
    name: sector.name,
    marketCap: sector.marketCap / 1e9, // Convert to billions
    change24h: sector.change24h,
    change7d: sector.change7d,
    volume: sector.volume24h / 1e9 // Convert to billions
  }));

  // Colors for pie chart
  const COLORS = [
    '#8884d8', '#82ca9d', '#ffc658', '#ff7300', 
    '#00ff00', '#ff00ff', '#00ffff', '#ffff00'
  ];

  // Custom tooltip for bar chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg">
          <p className="font-semibold">{label}</p>
          <p className="text-blue-600">
            Market Cap: ${payload[0].value.toFixed(2)}B
          </p>
          <p className={`${payload[0].payload.change24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            24h Change: {payload[0].payload.change24h.toFixed(2)}%
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5" />
          Sector Performance
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="distribution">Distribution</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {sectors.map((sector) => (
                <Card 
                  key={sector.id} 
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => setSelectedSector(sector)}
                >
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-sm">{sector.name}</h4>
                        <Badge variant={sector.change24h >= 0 ? "default" : "destructive"}>
                          {sector.change24h >= 0 ? '+' : ''}{sector.change24h.toFixed(1)}%
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Market Cap</span>
                          <span className="font-medium">{formatCurrency(sector.marketCap)}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">24h Volume</span>
                          <span className="font-medium">{formatCurrency(sector.volume24h)}</span>
                        </div>
                      </div>

                      <div className="space-y-1">
                        <p className="text-xs text-gray-500">Top Assets:</p>
                        <div className="flex flex-wrap gap-1">
                          {sector.topCoins.slice(0, 3).map((coin) => (
                            <Badge key={coin.id} variant="outline" className="text-xs">
                              {coin.symbol}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="name" 
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    fontSize={12}
                  />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar 
                    dataKey="marketCap" 
                    fill="#8884d8"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="distribution" className="space-y-4">
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="marketCap"
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value: number) => [`$${value.toFixed(2)}B`, 'Market Cap']}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>

        {/* Sector Detail Modal */}
        {selectedSector && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto m-4">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="w-5 h-5" />
                    {selectedSector.name} Sector
                  </CardTitle>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => setSelectedSector(null)}
                  >
                    ✕
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">Market Cap</p>
                    <p className="text-2xl font-bold">{formatCurrency(selectedSector.marketCap)}</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">24h Change</p>
                    <div className="flex items-center gap-2">
                      {getChangeIcon(selectedSector.change24h)}
                      <span className={`text-2xl font-bold ${
                        selectedSector.change24h >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {selectedSector.change24h >= 0 ? '+' : ''}{selectedSector.change24h.toFixed(2)}%
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3">Top Assets in {selectedSector.name}</h4>
                  <div className="space-y-2">
                    {selectedSector.topCoins.map((coin) => (
                      <div key={coin.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div>
                            <p className="font-medium">{coin.name}</p>
                            <p className="text-sm text-gray-600">{coin.symbol}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">${coin.price.toLocaleString()}</p>
                          <div className="flex items-center gap-1">
                            {getChangeIcon(coin.change24h)}
                            <span className={`text-sm ${
                              coin.change24h >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {coin.change24h >= 0 ? '+' : ''}{coin.change24h.toFixed(2)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
