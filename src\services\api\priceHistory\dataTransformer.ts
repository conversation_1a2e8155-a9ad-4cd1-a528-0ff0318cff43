
/**
 * Data Transformer
 * Transforms data between different formats (historical, candlestick, forecast)
 */

import type { PriceDataPoint, CandleData, PriceForecast } from './types';

/**
 * Transform historical price data to candlestick format
 * @param historicalData - Historical price data points
 * @returns Array of candlestick data
 */
export function transformToCandlestickData(historicalData: PriceDataPoint[]): CandleData[] {
  return historicalData.map(point => ({
    time: point.date,
    date: point.date,
    open: point.price,
    high: point.price * 1.02, // Mock high with small variance
    low: point.price * 0.98,  // Mock low with small variance
    close: point.price,
    volume: point.volume || 0,
    predicted: false
  }));
}

/**
 * Transform forecast predictions to candlestick format
 * @param predictions - Forecast predictions
 * @returns Array of candlestick data for predictions
 */
export function transformForecastToCandlestick(predictions: PriceForecast['predictions']): CandleData[] {
  return predictions.map(prediction => ({
    time: prediction.date,
    date: prediction.date,
    open: prediction.predictedPrice,
    high: prediction.upperBound,
    low: prediction.lowerBound,
    close: prediction.predictedPrice,
    volume: 0,
    predicted: true
  }));
}

/**
 * Combine historical and forecast data into a single candlestick dataset
 * @param historicalData - Historical price data
 * @param forecastData - Forecast predictions
 * @param historicalDaysToInclude - Number of recent historical days to include
 * @returns Combined candlestick data array
 */
export function combineHistoricalAndForecast(
  historicalData: PriceDataPoint[],
  forecastData: PriceForecast['predictions'],
  historicalDaysToInclude: number = 14
): CandleData[] {
  const recentHistorical = historicalData.slice(-historicalDaysToInclude);
  const historicalCandles = transformToCandlestickData(recentHistorical);
  const forecastCandles = transformForecastToCandlestick(forecastData);
  
  return [...historicalCandles, ...forecastCandles];
}
