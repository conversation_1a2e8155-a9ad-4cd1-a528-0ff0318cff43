-- =====================================================
-- COMPLETE DATABASE MIGRATION SCRIPT
-- Run this in Supabase SQL Editor to fix all issues
-- =====================================================

-- 1. Create API Providers Table
CREATE TABLE IF NOT EXISTS public.api_providers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    type TEXT NOT NULL CHECK (type IN ('market', 'onchain', 'defi', 'ai')),
    priority INTEGER NOT NULL DEFAULT 1,
    rate_limit_per_minute INTEGER,
    monthly_quota INTEGER,
    cost_per_request DECIMAL(10,8),
    cost_per_token DECIMAL(10,8),
    is_active BOOLEAN NOT NULL DEFAULT true,
    config JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create API Costs Table
CREATE TABLE IF NOT EXISTS public.api_costs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    provider_id UUID REFERENCES public.api_providers(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    requests_count INTEGER DEFAULT 0,
    tokens_used INTEGER DEFAULT 0,
    estimated_cost DECIMAL(10,6) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(provider_id, date)
);

-- 3. Create API Usage Logs Table
CREATE TABLE IF NOT EXISTS public.api_usage_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    provider TEXT NOT NULL,
    endpoint TEXT,
    user_id UUID,
    response_time_ms INTEGER,
    status_code INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create Admin Users Table
CREATE TABLE IF NOT EXISTS public.admin_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    admin_level TEXT NOT NULL DEFAULT 'admin' CHECK (admin_level IN ('admin', 'super_admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 5. Create App Error Logs Table
CREATE TABLE IF NOT EXISTS public.app_error_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    error_type TEXT NOT NULL,
    error_message TEXT,
    stack_trace TEXT,
    user_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create User Activity Logs Table
CREATE TABLE IF NOT EXISTS public.user_activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create Cache Entries Table
CREATE TABLE IF NOT EXISTS public.cache_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    cache_key TEXT NOT NULL UNIQUE,
    cache_value JSONB NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Create AI Prompt Templates Table (drop and recreate to fix column names)
DROP TABLE IF EXISTS public.ai_prompt_templates CASCADE;
CREATE TABLE public.ai_prompt_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    template_name TEXT NOT NULL UNIQUE,
    template_content TEXT NOT NULL,
    category TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Create Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_api_costs_provider_date ON public.api_costs(provider_id, date);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_provider ON public.api_usage_logs(provider);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_created_at ON public.api_usage_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_cache_entries_key ON public.cache_entries(cache_key);
CREATE INDEX IF NOT EXISTS idx_cache_entries_expires ON public.cache_entries(expires_at);

-- 10. Enable Row Level Security
ALTER TABLE public.api_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_costs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.app_error_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cache_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_prompt_templates ENABLE ROW LEVEL SECURITY;

-- 11. Create RLS Policies (Allow authenticated users to read, admins to write)
-- API Providers policies
CREATE POLICY "Allow authenticated users to read api_providers" ON public.api_providers
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to write api_providers" ON public.api_providers
    FOR ALL TO authenticated USING (true);

-- API Costs policies
CREATE POLICY "Allow authenticated users to read api_costs" ON public.api_costs
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to write api_costs" ON public.api_costs
    FOR ALL TO authenticated USING (true);

-- API Usage Logs policies
CREATE POLICY "Allow authenticated users to read api_usage_logs" ON public.api_usage_logs
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to write api_usage_logs" ON public.api_usage_logs
    FOR ALL TO authenticated USING (true);

-- Admin Users policies
CREATE POLICY "Allow authenticated users to read admin_users" ON public.admin_users
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to write admin_users" ON public.admin_users
    FOR ALL TO authenticated USING (true);

-- App Error Logs policies
CREATE POLICY "Allow authenticated users to read app_error_logs" ON public.app_error_logs
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to write app_error_logs" ON public.app_error_logs
    FOR ALL TO authenticated USING (true);

-- User Activity Logs policies
CREATE POLICY "Allow authenticated users to read user_activity_logs" ON public.user_activity_logs
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to write user_activity_logs" ON public.user_activity_logs
    FOR ALL TO authenticated USING (true);

-- Cache Entries policies
CREATE POLICY "Allow authenticated users to read cache_entries" ON public.cache_entries
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to write cache_entries" ON public.cache_entries
    FOR ALL TO authenticated USING (true);

-- AI Prompt Templates policies
CREATE POLICY "Allow authenticated users to read ai_prompt_templates" ON public.ai_prompt_templates
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to write ai_prompt_templates" ON public.ai_prompt_templates
    FOR ALL TO authenticated USING (true);

-- 12. Create is_admin function
CREATE OR REPLACE FUNCTION public.is_admin(user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.admin_users
        WHERE user_id = user_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. Insert Default API Providers
INSERT INTO public.api_providers (name, type, priority, rate_limit_per_minute, monthly_quota, cost_per_request, cost_per_token, is_active, config) VALUES
('CoinGecko', 'market', 1, 30, 10000, 0.001, NULL, true, '{"baseUrl": "https://api.coingecko.com/api/v3", "timeout": 15000}'),
('CoinMarketCap', 'market', 2, 333, 10000, 0.002, NULL, true, '{"baseUrl": "https://pro-api.coinmarketcap.com", "timeout": 15000}'),
('CryptoCompare', 'market', 3, 100, 100000, 0.0005, NULL, true, '{"baseUrl": "https://min-api.cryptocompare.com", "timeout": 15000}'),
('Glassnode', 'onchain', 1, 60, 5000, 0.01, NULL, true, '{"baseUrl": "https://api.glassnode.com", "timeout": 30000}'),
('GeckoTerminal', 'onchain', 2, 30, 50000, 0, NULL, true, '{"baseUrl": "https://api.geckoterminal.com/api/v2", "timeout": 15000}'),
('Etherscan', 'onchain', 3, 200, 100000, 0, NULL, true, '{"baseUrl": "https://api.etherscan.io/api", "timeout": 15000}'),
('DeFi Llama', 'defi', 1, 300, 999999, 0, NULL, true, '{"baseUrl": "https://api.llama.fi", "timeout": 10000}'),
('DeepSeek', 'ai', 1, 100, 1000000, NULL, 0.00000014, true, '{"baseUrl": "https://api.deepseek.com", "timeout": 60000}'),
('OpenAI', 'ai', 2, 500, 1000000, NULL, 0.0000015, false, '{"baseUrl": "https://api.openai.com", "timeout": 60000}'),
('Claude', 'ai', 3, 100, 1000000, NULL, 0.000008, false, '{"baseUrl": "https://api.anthropic.com", "timeout": 60000}')
ON CONFLICT (name) DO NOTHING;

-- 14. Make current user an admin (replace with your user ID if needed)
INSERT INTO public.admin_users (user_id, admin_level)
SELECT auth.uid(), 'super_admin'
WHERE auth.uid() IS NOT NULL
ON CONFLICT (user_id) DO NOTHING;

-- 15. Insert Sample AI Prompt Templates
INSERT INTO public.ai_prompt_templates (template_name, template_content, category, is_active) VALUES
('market_analysis', 'Analyze the current market conditions for {symbol} considering: price action, volume, market sentiment, and technical indicators. Provide a concise summary with key insights.', 'analysis', true),
('risk_assessment', 'Evaluate the risk factors for {symbol} including: volatility, liquidity, regulatory concerns, and technical risks. Rate the overall risk level and provide recommendations.', 'risk', true),
('price_prediction', 'Based on current market data for {symbol}, provide a short-term price prediction considering: technical analysis, market trends, and fundamental factors.', 'prediction', true)
ON CONFLICT (template_name) DO NOTHING;

-- Success message
SELECT 'Database migration completed successfully! All tables created with proper permissions.' as result;
