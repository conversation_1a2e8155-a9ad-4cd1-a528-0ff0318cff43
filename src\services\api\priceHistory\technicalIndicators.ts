
// Calculate Simple Moving Average
export const calculateSMA = (prices: number[], period: number): number[] => {
  const sma: number[] = [];
  
  for (let i = 0; i < prices.length; i++) {
    if (i < period - 1) {
      sma.push(NaN); // Not enough data for calculation
      continue;
    }
    
    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += prices[i - j];
    }
    
    sma.push(sum / period);
  }
  
  return sma;
};

// Calculate Exponential Moving Average (EMA)
export const calculateEMA = (prices: number[], period: number): number[] => {
  if (prices.length === 0) {
    return [];
  }
  
  const ema: number[] = Array(prices.length).fill(NaN);
  
  // Initial SMA
  let sum = 0;
  for (let i = 0; i < period; i++) {
    if (i >= prices.length) break;
    sum += prices[i];
  }
  
  if (period <= prices.length) {
    ema[period - 1] = sum / period;
  }
  
  // Calculate multiplier
  const multiplier = 2 / (period + 1);
  
  // Calculate EMAs
  for (let i = period; i < prices.length; i++) {
    ema[i] = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier));
  }
  
  return ema;
};

// Calculate Relative Strength Index (RSI)
export const calculateRSI = (prices: number[], period: number = 14): number[] => {
  if (prices.length <= period) {
    return Array(prices.length).fill(NaN);
  }
  
  const changes: number[] = [];
  for (let i = 1; i < prices.length; i++) {
    changes.push(prices[i] - prices[i - 1]);
  }
  
  const rsi: number[] = [NaN]; // First value is NaN as we need at least one change
  
  for (let i = 1; i < prices.length; i++) {
    if (i < period) {
      rsi.push(NaN);
      continue;
    }
    
    let gains = 0;
    let losses = 0;
    
    for (let j = i - period; j < i; j++) {
      const change = changes[j];
      if (change > 0) {
        gains += change;
      } else {
        losses -= change;
      }
    }
    
    const avgGain = gains / period;
    const avgLoss = losses / period;
    
    if (avgLoss === 0) {
      rsi.push(100);
    } else {
      const rs = avgGain / avgLoss;
      rsi.push(100 - (100 / (1 + rs)));
    }
  }
  
  return rsi;
};

// Calculate Bollinger Bands
export const calculateBollingerBands = (prices: number[], period: number = 20, stdDev: number = 2): { 
  upper: number[], 
  middle: number[], 
  lower: number[] 
} => {
  const sma = calculateSMA(prices, period);
  const upper: number[] = [];
  const lower: number[] = [];
  
  for (let i = 0; i < prices.length; i++) {
    if (i < period - 1) {
      upper.push(NaN);
      lower.push(NaN);
      continue;
    }
    
    let sumSquaredDiff = 0;
    for (let j = 0; j < period; j++) {
      sumSquaredDiff += Math.pow(prices[i - j] - sma[i], 2);
    }
    
    const stdDeviation = Math.sqrt(sumSquaredDiff / period);
    upper.push(sma[i] + (stdDev * stdDeviation));
    lower.push(sma[i] - (stdDev * stdDeviation));
  }
  
  return { upper, middle: sma, lower };
};

// Calculate MACD (Moving Average Convergence Divergence)
export const calculateMACD = (prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): { 
  macd: number[], 
  signal: number[], 
  histogram: number[] 
} => {
  const ema12 = calculateEMA(prices, fastPeriod);
  const ema26 = calculateEMA(prices, slowPeriod);
  
  const macdLine: number[] = [];
  for (let i = 0; i < prices.length; i++) {
    if (isNaN(ema12[i]) || isNaN(ema26[i])) {
      macdLine.push(NaN);
    } else {
      macdLine.push(ema12[i] - ema26[i]);
    }
  }
  
  // Calculate signal line (9-day EMA of MACD)
  const signalLine = calculateEMA(macdLine.filter(val => !isNaN(val)), signalPeriod);
  
  // Pad signal line with NaN values
  const paddedSignalLine: number[] = Array(prices.length).fill(NaN);
  const nonNanCount = macdLine.filter(val => !isNaN(val)).length;
  for (let i = 0; i < signalLine.length; i++) {
    paddedSignalLine[prices.length - nonNanCount + i] = signalLine[i];
  }
  
  // Calculate histogram
  const histogram: number[] = [];
  for (let i = 0; i < prices.length; i++) {
    if (isNaN(macdLine[i]) || isNaN(paddedSignalLine[i])) {
      histogram.push(NaN);
    } else {
      histogram.push(macdLine[i] - paddedSignalLine[i]);
    }
  }
  
  return { macd: macdLine, signal: paddedSignalLine, histogram };
};
