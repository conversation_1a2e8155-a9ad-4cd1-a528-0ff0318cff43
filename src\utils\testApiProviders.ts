/**
 * Test utilities for API Provider Management
 * Use these functions to test the API provider functionality
 */

import {
  getAllApiProviders,
  createApiProvider,
  updateApiProvider,
  deleteApiProvider,
  getApiProviderStats,
  validateApiProviderConfig,
  CreateApiProviderRequest,
} from '@/services/api/providers/apiProviderService';

/**
 * Test creating a new API provider
 */
export async function testCreateProvider(): Promise<void> {
  console.log('🧪 Testing API Provider Creation...');
  
  const testProvider: CreateApiProviderRequest = {
    name: 'Test Provider',
    type: 'market',
    priority: 10,
    rate_limit_per_minute: 60,
    monthly_quota: 5000,
    cost_per_request: 0.002,
    is_active: true,
    config: {
      baseUrl: 'https://api.test.com',
      apiKey: 'test-key',
      timeout: 15000,
    },
  };

  try {
    // Validate configuration first
    const validationErrors = validateApiProviderConfig(testProvider);
    if (validationErrors.length > 0) {
      console.error('❌ Validation failed:', validationErrors);
      return;
    }

    // Create the provider
    const createdProvider = await createApiProvider(testProvider);
    console.log('✅ Provider created successfully:', createdProvider);

    // Clean up - delete the test provider
    await deleteApiProvider(createdProvider.id);
    console.log('🧹 Test provider cleaned up');
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

/**
 * Test fetching all providers
 */
export async function testFetchProviders(): Promise<void> {
  console.log('🧪 Testing Fetch All Providers...');
  
  try {
    const providers = await getAllApiProviders();
    console.log(`✅ Fetched ${providers.length} providers:`, providers);
    
    // Test statistics
    const stats = await getApiProviderStats();
    console.log('📊 Provider statistics:', stats);
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

/**
 * Test updating a provider
 */
export async function testUpdateProvider(): Promise<void> {
  console.log('🧪 Testing Provider Update...');
  
  try {
    // First, get all providers
    const providers = await getAllApiProviders();
    if (providers.length === 0) {
      console.log('⚠️ No providers found to test update');
      return;
    }

    const testProvider = providers[0];
    console.log('📝 Updating provider:', testProvider.name);

    // Update the provider
    const updatedProvider = await updateApiProvider(testProvider.id, {
      priority: testProvider.priority + 1,
    });

    console.log('✅ Provider updated successfully:', updatedProvider);

    // Revert the change
    await updateApiProvider(testProvider.id, {
      priority: testProvider.priority,
    });

    console.log('🔄 Reverted changes');
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

/**
 * Test provider validation
 */
export async function testProviderValidation(): Promise<void> {
  console.log('🧪 Testing Provider Validation...');

  const invalidProviders: CreateApiProviderRequest[] = [
    {
      name: '', // Invalid: empty name
      type: 'market',
      priority: 1,
    },
    {
      name: 'Test Provider',
      type: 'invalid' as any, // Invalid: wrong type
      priority: 1,
    },
    {
      name: 'Test Provider',
      type: 'market',
      priority: 0, // Invalid: priority too low
    },
    {
      name: 'Test Provider',
      type: 'market',
      priority: 1,
      rate_limit_per_minute: -1, // Invalid: negative rate limit
    },
  ];

  invalidProviders.forEach((provider, index) => {
    const errors = validateApiProviderConfig(provider);
    console.log(`Test ${index + 1} - Validation errors:`, errors);
    
    if (errors.length === 0) {
      console.error(`❌ Expected validation errors for test ${index + 1}`);
    } else {
      console.log(`✅ Validation correctly caught errors for test ${index + 1}`);
    }
  });
}

/**
 * Test complete CRUD operations
 */
export async function testCRUDOperations(): Promise<void> {
  console.log('🧪 Testing Complete CRUD Operations...');

  const testProvider: CreateApiProviderRequest = {
    name: 'CRUD Test Provider',
    type: 'ai',
    priority: 5,
    rate_limit_per_minute: 100,
    monthly_quota: 10000,
    cost_per_token: 0.000001,
    is_active: false,
    config: {
      baseUrl: 'https://api.crudtest.com',
      apiKey: 'crud-test-key',
      model: 'test-model',
    },
  };

  let createdProviderId: string | null = null;

  try {
    // CREATE
    console.log('📝 Creating provider...');
    const createdProvider = await createApiProvider(testProvider);
    createdProviderId = createdProvider.id;
    console.log('✅ Created:', createdProvider.name);

    // READ
    console.log('📖 Reading provider...');
    const providers = await getAllApiProviders();
    const foundProvider = providers.find(p => p.id === createdProviderId);
    if (!foundProvider) {
      throw new Error('Created provider not found in list');
    }
    console.log('✅ Found provider in list');

    // UPDATE
    console.log('📝 Updating provider...');
    const updatedProvider = await updateApiProvider(createdProviderId, {
      name: 'CRUD Test Provider (Updated)',
      is_active: true,
      priority: 3,
    });
    console.log('✅ Updated:', updatedProvider.name);

    // Verify update
    if (updatedProvider.name !== 'CRUD Test Provider (Updated)' || 
        !updatedProvider.is_active || 
        updatedProvider.priority !== 3) {
      throw new Error('Update verification failed');
    }
    console.log('✅ Update verified');

    // DELETE
    console.log('🗑️ Deleting provider...');
    await deleteApiProvider(createdProviderId);
    console.log('✅ Deleted successfully');

    // Verify deletion
    const providersAfterDelete = await getAllApiProviders();
    const deletedProvider = providersAfterDelete.find(p => p.id === createdProviderId);
    if (deletedProvider) {
      throw new Error('Provider still exists after deletion');
    }
    console.log('✅ Deletion verified');

    console.log('🎉 All CRUD operations completed successfully!');
  } catch (error) {
    console.error('❌ CRUD test failed:', error);
    
    // Cleanup on error
    if (createdProviderId) {
      try {
        await deleteApiProvider(createdProviderId);
        console.log('🧹 Cleaned up test provider after error');
      } catch (cleanupError) {
        console.error('❌ Cleanup failed:', cleanupError);
      }
    }
  }
}

/**
 * Run all tests
 */
export async function runAllTests(): Promise<void> {
  console.log('🚀 Starting API Provider Management Tests...');
  console.log('================================================');

  await testProviderValidation();
  console.log('');
  
  await testFetchProviders();
  console.log('');
  
  await testCreateProvider();
  console.log('');
  
  await testUpdateProvider();
  console.log('');
  
  await testCRUDOperations();
  console.log('');
  
  console.log('🏁 All tests completed!');
  console.log('================================================');
}

/**
 * Test database connection and basic functionality
 */
export async function testDatabaseConnection(): Promise<boolean> {
  console.log('🔌 Testing database connection...');
  
  try {
    const providers = await getAllApiProviders();
    console.log(`✅ Database connection successful. Found ${providers.length} providers.`);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    console.log('💡 Make sure you have:');
    console.log('   1. Created the database tables using the migration script');
    console.log('   2. Configured your Supabase connection properly');
    console.log('   3. Set up the correct RLS policies');
    return false;
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testApiProviders = {
    runAllTests,
    testDatabaseConnection,
    testCreateProvider,
    testFetchProviders,
    testUpdateProvider,
    testProviderValidation,
    testCRUDOperations,
  };
}
