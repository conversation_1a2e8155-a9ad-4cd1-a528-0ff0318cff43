import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { TrendingUp, ArrowUpRight, ArrowDownRight, AlertTriangle, RefreshCw } from "lucide-react";
import { fetchTrendingCoins } from "@/services/api/coinMarketData";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";

interface TrendingToken {
  id: number;
  name: string;
  symbol: string;
  category: string;
  change: number;
  logo?: string;
}

interface Category {
  id: string;
  name: string;
}

interface TrendingSectionProps {
  tokens?: TrendingToken[];
}

export default function TrendingSection({ tokens: initialTokens }: TrendingSectionProps) {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [tokens, setTokens] = useState<TrendingToken[]>(initialTokens || []);
  const [loading, setLoading] = useState(!initialTokens);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<Category[]>([
    { id: "all", name: "All" },
    { id: "layer-1", name: "Layer 1" },
    { id: "defi", name: "DeFi" },
    { id: "gaming", name: "Gaming" },
    { id: "oracle", name: "Oracle" }
  ]);
  
  const loadTrendingCoins = async () => {
    try {
      setLoading(true);
      setError(null);
      const trendingData = await fetchTrendingCoins();
      
      console.log("Fetched trending data:", trendingData);
      
      if (!trendingData || !Array.isArray(trendingData) || trendingData.length === 0) {
        setError("No trending tokens available");
        setTokens([]);
        setLoading(false);
        return;
      }
      
      // Transform CoinGecko trending data to our format
      const transformedTokens = trendingData
        .filter(coin => coin && coin.item) // Make sure the coin and item exist
        .map((coin: any, index: number) => {
          // Use actual categories from CoinGecko when available
          const categoryMap: Record<string, string> = {
            "smart-contract-platform": "Layer 1",
            "decentralized-finance-defi": "DeFi",
            "gaming": "Gaming",
            "oracle": "Oracle"
          };
          
          const coinCategories = coin.item.data?.categories || [];
          let category = "Other";
          
          // Try to map the first category that matches our categories
          for (const cat of coinCategories) {
            if (categoryMap[cat]) {
              category = categoryMap[cat];
              break;
            }
          }
          
          // If no matching category found, assign a random one for display
          if (category === "Other") {
            const categories = ["Layer 1", "DeFi", "Gaming", "Oracle"];
            category = categories[Math.floor(Math.random() * categories.length)];
          }
          
          // Use actual price change when available
          const priceChange = coin.item.data?.price_change_percentage_24h?.usd || 
                            (Math.random() * 12 - 4).toFixed(1);
          
          return {
            id: index + 1,
            name: coin.item.name,
            symbol: coin.item.symbol,
            category: category,
            change: parseFloat(priceChange),
            logo: coin.item.thumb || coin.item.large
          };
        });
      
      console.log("Transformed trending tokens:", transformedTokens);
      setTokens(transformedTokens);
    } catch (error) {
      console.error("Failed to fetch trending tokens:", error);
      setError("Failed to load trending tokens");
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    if (!initialTokens || initialTokens.length === 0) {
      loadTrendingCoins();
    }
  }, [initialTokens]);
  
  const handleRefresh = () => {
    loadTrendingCoins();
    toast({
      title: "Refreshing data",
      description: "Fetching the latest trending tokens..."
    });
  };
  
  // Update filteredTokens to handle case when no tokens match the category
  const filteredTokens = selectedCategory === "all" 
    ? tokens 
    : tokens.filter(token => 
        token.category.toLowerCase().includes(selectedCategory.replace("-", " ").toLowerCase())
      );
  
  if (loading) {
    return (
      <div className="rounded-lg border border-border bg-card p-5 gradient-border animate-scale-in" style={{ animationDelay: "200ms" }}>
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <TrendingUp size={16} className="text-primary" />
            <h3 className="font-medium">Trending</h3>
          </div>
        </div>
        <div className="h-24 flex items-center justify-center">
          <div className="flex items-center gap-2 text-muted-foreground">
            <RefreshCw size={16} className="animate-spin" />
            Loading trending tokens...
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="rounded-lg border border-border bg-card p-5 gradient-border animate-scale-in" style={{ animationDelay: "200ms" }}>
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <TrendingUp size={16} className="text-primary" />
          <h3 className="font-medium">Trending</h3>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={cn(
                "text-xs px-3 py-1 rounded-full transition-colors",
                selectedCategory === category.id
                  ? "bg-primary text-primary-foreground"
                  : "bg-secondary text-muted-foreground hover:text-foreground"
              )}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>
      
      {error ? (
        <div className="h-24 flex flex-col items-center justify-center text-center p-4">
          <AlertTriangle className="h-5 w-5 text-amber-500 mb-2" />
          <p className="text-muted-foreground mb-2">{error}</p>
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm"
            className="flex items-center gap-1"
          >
            <RefreshCw size={14} />
            Try Again
          </Button>
        </div>
      ) : filteredTokens.length === 0 ? (
        <div className="h-24 flex flex-col items-center justify-center text-center">
          <p className="text-muted-foreground mb-2">No tokens found in this category</p>
          {selectedCategory !== "all" ? (
            <Button 
              onClick={() => setSelectedCategory("all")} 
              variant="outline"
              size="sm"
            >
              View all trending tokens
            </Button>
          ) : (
            <Button 
              onClick={handleRefresh} 
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <RefreshCw size={14} />
              Refresh Data
            </Button>
          )}
        </div>
      ) : (
        <div className="overflow-x-auto no-scrollbar">
          <div className="flex gap-3 py-2 min-w-max">
            {filteredTokens.map(token => (
              <div 
                key={token.id}
                className="flex flex-col justify-between p-4 min-w-[150px] md:min-w-[180px] rounded-lg bg-secondary border border-border hover:border-primary/50 transition-colors"
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center gap-2">
                    {token.logo && (
                      <img 
                        src={token.logo} 
                        alt={token.name} 
                        className="w-6 h-6 rounded-full"
                        onError={(e) => {
                          // Hide broken images
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                    )}
                    <div>
                      <h4 className="font-medium">{token.name}</h4>
                      <div className="text-xs text-muted-foreground mt-0.5">{token.symbol}</div>
                    </div>
                  </div>
                  <div className="text-xs px-2 py-0.5 rounded-full bg-secondary border border-border">
                    {token.category}
                  </div>
                </div>
                
                <div className={cn(
                  "flex items-center text-sm",
                  token.change >= 0 ? "text-green-500" : "text-red-500"
                )}>
                  {token.change >= 0 
                    ? <ArrowUpRight size={14} /> 
                    : <ArrowDownRight size={14} />
                  }
                  <span className="ml-0.5 font-medium">{Math.abs(token.change)}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
