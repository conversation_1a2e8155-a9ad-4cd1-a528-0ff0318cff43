
/**
 * Centralized error handling for API services
 */
import type { ApiError } from '../types';

export class ApiServiceError extends <PERSON>rror {
  public readonly code: string;
  public readonly details?: any;
  public readonly timestamp: string;
  public readonly statusCode?: number;

  constructor(message: string, code: string = 'UNKNOWN_ERROR', details?: any, statusCode?: number) {
    super(message);
    this.name = 'ApiServiceError';
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
    this.statusCode = statusCode;
  }

  public toApiError(): ApiError {
    return {
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp
    };
  }
}

/**
 * Standard error handler for API responses
 */
export function handleApiError(error: any): ApiServiceError {
  if (error instanceof ApiServiceError) {
    return error;
  }

  // Handle Axios errors
  if (error.response) {
    const status = error.response.status;
    const message = error.response.data?.message || error.message;
    
    switch (status) {
      case 401:
        return new ApiServiceError('Unauthorized access', 'UNAUTHORIZED', error.response.data, status);
      case 403:
        return new ApiServiceError('Forbidden access', 'FORBIDDEN', error.response.data, status);
      case 404:
        return new ApiServiceError('Resource not found', 'NOT_FOUND', error.response.data, status);
      case 429:
        return new ApiServiceError('Rate limit exceeded', 'RATE_LIMITED', error.response.data, status);
      case 500:
        return new ApiServiceError('Internal server error', 'SERVER_ERROR', error.response.data, status);
      default:
        return new ApiServiceError(message, 'HTTP_ERROR', error.response.data, status);
    }
  }

  // Handle network errors
  if (error.request) {
    return new ApiServiceError('Network error - no response received', 'NETWORK_ERROR', error);
  }

  // Handle other errors
  return new ApiServiceError(
    error.message || 'Unknown error occurred',
    'UNKNOWN_ERROR',
    error
  );
}

/**
 * Retry mechanism for failed API calls
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  backoffMs: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry on client errors (4xx)
      if (error instanceof ApiServiceError && 
          error.statusCode && 
          error.statusCode >= 400 && 
          error.statusCode < 500) {
        throw error;
      }

      if (attempt === maxRetries) {
        break;
      }

      // Exponential backoff
      const delay = backoffMs * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw new ApiServiceError(
    `Operation failed after ${maxRetries} attempts: ${lastError.message}`,
    'MAX_RETRIES_EXCEEDED',
    lastError
  );
}
