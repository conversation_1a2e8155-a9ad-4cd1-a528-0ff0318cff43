
import { coinGeckoAxios, handleApiError, cacheResponse } from "../coinGeckoClient";

// Fetch historical market data for a specific coin
export const fetchCoinHistoricalData = async (coinId: string, days: number = 30) => {
  try {
    const response = await coinGeckoAxios.get(`/coins/${coinId}/market_chart`, {
      params: {
        vs_currency: 'usd',
        days: days,
        interval: days > 90 ? 'daily' : undefined
      }
    });
    
    cacheResponse(`history_${coinId}_${days}`, response.data);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      key: `history_${coinId}_${days}`,
      data: { prices: [], market_caps: [], total_volumes: [] }
    });
  }
};

// Add the missing fetchPriceHistory function
export const fetchPriceHistory = async (coinId: string, days: number = 30) => {
  try {
    const historyData = await fetchCoinHistoricalData(coinId, days);
    
    // Transform the data into a format suitable for charts
    if (!historyData || !historyData.prices || historyData.prices.length === 0) {
      return { data: [] };
    }
    
    const formattedData = historyData.prices.map((item: [number, number]) => {
      return {
        date: new Date(item[0]).toISOString().split('T')[0],
        price: item[1],
        timestamp: item[0]
      };
    });
    
    return { data: formattedData };
  } catch (error) {
    console.error("Error fetching price history:", error);
    return { data: [] };
  }
};

// Transform CoinGecko historical data to candlestick format
export const transformToCandlestick = (historyData: any, interval: number = 24) => {
  if (!historyData || !historyData.prices || historyData.prices.length === 0) {
    return [];
  }
  
  const prices = historyData.prices;
  const groupedData: any[] = [];
  
  // Group price data into intervals (hours)
  for (let i = 0; i < prices.length; i += interval) {
    const intervalPrices = prices.slice(i, i + interval);
    if (intervalPrices.length > 0) {
      const timestamp = intervalPrices[0][0];
      const open = intervalPrices[0][1];
      const close = intervalPrices[intervalPrices.length - 1][1];
      const high = Math.max(...intervalPrices.map((p: any) => p[1]));
      const low = Math.min(...intervalPrices.map((p: any) => p[1]));
      
      groupedData.push({
        time: new Date(timestamp).toISOString().split('T')[0],
        open,
        high,
        low,
        close,
        volume: 0, // Default volume if not available
        predicted: false
      });
    }
  }
  
  return groupedData;
};
