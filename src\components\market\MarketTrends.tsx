
import { useQuery } from "@tanstack/react-query";
import { ChevronR<PERSON>, Flame, Loader2, AlertTriangle, Calendar, RefreshCw } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { fetchTrendingCoins } from "@/services/api/coinMarketData";
import { fetchRecentlyAdded } from "@/services/api/marketInsightsApi";
import { toast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

export default function MarketTrends() {
  // Track if we've attempted to load data already
  const [loadAttempted, setLoadAttempted] = useState(false);

  const { data: trendingCoins, isLoading: isTrendingLoading, error: trendingError, refetch: refetchTrending } = useQuery({
    queryKey: ['trendingCoins'],
    queryFn: fetchTrendingCoins,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });
  
  const { data: recentCoins, isLoading: isRecentLoading, error: recentError, refetch: refetchRecent } = useQuery({
    queryKey: ['recentlyAddedCoins'],
    queryFn: () => fetchRecentlyAdded(20), // Increased limit to find newer coins
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  // Filter out established coins to find only truly recent listings 
  const getFilteredRecent = () => {
    if (!recentCoins || !Array.isArray(recentCoins)) return [];
    
    // Filter to coins that:
    // 1. Have actual creation dates in last 6 months
    // 2. Have a lower market cap (likely newer)
    // 3. Don't include major established coins
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    // Known established coins to exclude
    const establishedCoins = [
      "bitcoin", "ethereum", "tether", "ripple", "xrp", "binancecoin", "bnb", 
      "solana", "cardano", "dogecoin", "polygon", "polkadot", "usd-coin", "usdc", "tron"
    ];
    
    return recentCoins
      .filter(coin => {
        // Skip established coins
        if (establishedCoins.includes(coin.id?.toLowerCase()) || 
            establishedCoins.includes(coin.symbol?.toLowerCase())) {
          return false;
        }
        
        // If we have a date, make sure it's recent
        if (coin.atl_date) {
          const coinDate = new Date(coin.atl_date);
          return coinDate > sixMonthsAgo;
        }
        
        // If no date but low market cap, probably new
        return coin.market_cap && coin.market_cap < 10000000; // Less than $10M market cap
      })
      .sort((a, b) => {
        // Sort by date, newest first
        if (a.atl_date && b.atl_date) {
          return new Date(b.atl_date).getTime() - new Date(a.atl_date).getTime();
        }
        return 0;
      });
  };
  
  // Get the filtered recent coins
  const filteredRecent = getFilteredRecent();
  
  // Handle cases where our filtering found no coins - MOVED BEFORE useEffect
  const effectiveRecentError = recentError || (loadAttempted && filteredRecent.length === 0 ? 
    new Error("No recently added coins found") : null);

  // Set loadAttempted to true when queries complete
  useEffect(() => {
    if (!isTrendingLoading && !isRecentLoading) {
      setLoadAttempted(true);
    }
  }, [isTrendingLoading, isRecentLoading]);

  // Error handling with toasts - moved from onSettled to useEffect
  useEffect(() => {
    if (trendingError && !isTrendingLoading) {
      toast({
        title: "Error loading trending data",
        description: "Could not load trending coins. Please try again later.",
        variant: "destructive",
      });
    }
    
    if (effectiveRecentError && !isRecentLoading) {
      toast({
        title: "Error loading recent listings",
        description: "Could not load recently added coins. Please try again later.",
        variant: "destructive",
      });
    }
  }, [trendingError, effectiveRecentError, isTrendingLoading, isRecentLoading]);

  const handleRefreshTrending = () => {
    refetchTrending();
    toast({
      title: "Refreshing trending data",
      description: "Attempting to load trending coins again.",
    });
  };
  
  const handleRefreshRecent = () => {
    refetchRecent();
    toast({
      title: "Refreshing recent listings",
      description: "Attempting to load recently added coins again.",
    });
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric'
    }).format(date);
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Flame className="h-5 w-5 text-orange-500" />
            Trending Coins
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isTrendingLoading ? (
            <div className="flex items-center justify-center h-60">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : trendingError ? (
            <div className="flex flex-col items-center justify-center h-60 text-center p-4">
              <AlertTriangle className="h-10 w-10 text-amber-500 mb-3" />
              <p className="text-muted-foreground mb-4">Unable to load trending coins</p>
              <Button onClick={handleRefreshTrending} variant="outline" className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
            </div>
          ) : !trendingCoins || !Array.isArray(trendingCoins) || trendingCoins.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-60 text-center">
              <p className="text-muted-foreground mb-4">No trending coins available</p>
              <Button onClick={handleRefreshTrending} variant="outline" className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Refresh Data
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {trendingCoins.slice(0, 6).map((item: any, idx: number) => {
                // Handle cases where API returns unexpected structure
                if (!item || !item.item) {
                  console.warn("Invalid trending coin data", item);
                  return null;
                }
                
                const coin = item.item;
                return (
                  <div key={coin.id} className="flex items-center justify-between border-b border-border pb-4 last:border-0">
                    <div className="flex items-center">
                      <div className="w-8 h-8 mr-4 font-semibold text-center text-muted-foreground">
                        #{idx + 1}
                      </div>
                      <div className="w-10 h-10 rounded-full overflow-hidden mr-4">
                        <img 
                          src={coin.thumb || coin.large} 
                          alt={coin.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            // Use a fallback for broken images
                            (e.target as HTMLImageElement).src = '/placeholder.svg';
                          }}
                        />
                      </div>
                      <div>
                        <div className="font-medium">{coin.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {coin.symbol}
                          {coin.market_cap_rank && 
                            <span className="ml-2 text-xs px-1.5 py-0.5 bg-secondary rounded-full">
                              Rank #{coin.market_cap_rank}
                            </span>
                          }
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
                      <ChevronRight className="h-5 w-5" />
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-green-500" />
            Recently Added
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isRecentLoading ? (
            <div className="flex items-center justify-center h-60">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : effectiveRecentError ? (
            <div className="flex flex-col items-center justify-center h-60 text-center p-4">
              <AlertTriangle className="h-10 w-10 text-amber-500 mb-3" />
              <p className="text-muted-foreground mb-4">Unable to find recently added coins</p>
              <Button onClick={handleRefreshRecent} variant="outline" className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
            </div>
          ) : filteredRecent.length > 0 ? (
            <div className="space-y-4">
              {filteredRecent.slice(0, 6).map((coin: any, idx: number) => (
                <div key={coin.id} className="flex items-center justify-between border-b border-border pb-4 last:border-0">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full overflow-hidden mr-4">
                      <img 
                        src={coin.image} 
                        alt={coin.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Use a fallback for broken images
                          (e.target as HTMLImageElement).src = '/placeholder.svg';
                        }}
                      />
                    </div>
                    <div>
                      <div className="font-medium">{coin.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {coin.symbol.toUpperCase()}
                        <span className="ml-2 text-xs px-1.5 py-0.5 bg-green-500/10 text-green-500 rounded-full">
                          New
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="text-sm">${coin.current_price?.toFixed(6) || "N/A"}</div>
                    <div className="text-xs text-muted-foreground">
                      Added: {formatDate(coin.atl_date)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-60 text-center">
              <p className="text-muted-foreground mb-4">No recent listings available</p>
              <Button onClick={handleRefreshRecent} variant="outline" className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Refresh Data
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
