<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400" fill="none">
  <defs>
    <linearGradient id="defiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5B21B6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="protocolGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06B6D4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891B2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="#0F172A"/>
  
  <!-- Central DeFi Hub -->
  <circle cx="400" cy="200" r="80" fill="url(#defiGradient)" stroke="#A855F7" stroke-width="3"/>
  <text x="400" y="195" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">DeFi</text>
  <text x="400" y="210" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">Ecosystem</text>
  
  <!-- DEX Protocol -->
  <g transform="translate(150, 80)">
    <circle cx="50" cy="50" r="40" fill="url(#protocolGradient)" stroke="#0EA5E9" stroke-width="2"/>
    <text x="50" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">DEX</text>
    <text x="50" y="58" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">Trading</text>
    <path d="M 90 100 Q 250 150 320 170" stroke="#06B6D4" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Lending Protocol -->
  <g transform="translate(600, 80)">
    <circle cx="50" cy="50" r="40" fill="url(#protocolGradient)" stroke="#0EA5E9" stroke-width="2"/>
    <text x="50" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Lending</text>
    <text x="50" y="58" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">Protocols</text>
    <path d="M 10 100 Q 150 150 320 170" stroke="#06B6D4" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Yield Farming -->
  <g transform="translate(150, 270)">
    <circle cx="50" cy="50" r="40" fill="url(#protocolGradient)" stroke="#0EA5E9" stroke-width="2"/>
    <text x="50" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Yield</text>
    <text x="50" y="58" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">Farming</text>
    <path d="M 90 300 Q 250 250 320 230" stroke="#06B6D4" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Stablecoins -->
  <g transform="translate(600, 270)">
    <circle cx="50" cy="50" r="40" fill="url(#protocolGradient)" stroke="#0EA5E9" stroke-width="2"/>
    <text x="50" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Stable</text>
    <text x="50" y="58" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">Coins</text>
    <path d="M 10 300 Q 150 250 320 230" stroke="#06B6D4" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Insurance -->
  <g transform="translate(400, 50)">
    <circle cx="50" cy="50" r="35" fill="url(#protocolGradient)" stroke="#0EA5E9" stroke-width="2"/>
    <text x="50" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">Insurance</text>
    <text x="50" y="58" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">Protocols</text>
    <path d="M 50 85 L 50 120" stroke="#06B6D4" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Governance -->
  <g transform="translate(400, 300)">
    <circle cx="50" cy="50" r="35" fill="url(#protocolGradient)" stroke="#0EA5E9" stroke-width="2"/>
    <text x="50" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">DAO</text>
    <text x="50" y="58" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">Governance</text>
    <path d="M 50 15 L 50 -20" stroke="#06B6D4" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#06B6D4"/>
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="400" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">Decentralized Finance Ecosystem</text>
  
  <!-- Benefits -->
  <g transform="translate(50, 350)">
    <text x="0" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓ No Intermediaries</text>
    <text x="150" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓ 24/7 Access</text>
    <text x="280" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓ Global Reach</text>
    <text x="420" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓ Programmable Money</text>
    <text x="600" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓ Transparency</text>
  </g>
</svg>
