
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { TrendingUp } from 'lucide-react';
import { safeNumber, formatLargeNumber } from './utils/formatters';
import PriceChange from './PriceChange';

interface TopCoinsTableProps {
  coins: any[];
}

export default function TopCoinsTable({ coins }: TopCoinsTableProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Top Cryptocurrencies by Market Cap
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">#</TableHead>
              <TableHead>Name</TableHead>
              <TableHead className="text-right">Price</TableHead>
              <TableHead className="text-right">24h %</TableHead>
              <TableHead className="text-right">7d %</TableHead>
              <TableHead className="text-right">Market Cap</TableHead>
              <TableHead className="text-right">Volume (24h)</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {coins.map((coin, index) => (
              <TableRow key={coin.id}>
                <TableCell>{index + 1}</TableCell>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full overflow-hidden">
                      <img src={coin.image} alt={coin.name} className="w-full h-full object-cover" />
                    </div>
                    <span>{coin.name}</span>
                    <span className="text-muted-foreground uppercase">{coin.symbol}</span>
                  </div>
                </TableCell>
                <TableCell className="text-right font-mono">
                  ${safeNumber(coin.current_price) > 0 ? safeNumber(coin.current_price).toLocaleString(undefined, { maximumFractionDigits: 8 }) : 'N/A'}
                </TableCell>
                <TableCell className="text-right">
                  <PriceChange value={coin.price_change_percentage_24h} />
                </TableCell>
                <TableCell className="text-right">
                  <PriceChange value={coin.price_change_percentage_7d_in_currency} />
                </TableCell>
                <TableCell className="text-right font-mono">
                  {safeNumber(coin.market_cap) > 0 ? formatLargeNumber(safeNumber(coin.market_cap)) : 'N/A'}
                </TableCell>
                <TableCell className="text-right font-mono">
                  {safeNumber(coin.total_volume) > 0 ? formatLargeNumber(safeNumber(coin.total_volume)) : 'N/A'}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
