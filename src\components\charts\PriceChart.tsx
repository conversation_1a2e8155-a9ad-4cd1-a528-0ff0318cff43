import React from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart
} from 'recharts';

interface PriceDataPoint {
  date: string;
  price: number;
  timestamp: number;
}

interface PriceChartProps {
  data: PriceDataPoint[];
  coinName: string;
  coinSymbol: string;
  isLoading?: boolean;
  chartType?: 'line' | 'area';
  color?: string;
}

const PriceChart: React.FC<PriceChartProps> = ({ 
  data, 
  coinName, 
  coinSymbol, 
  isLoading = false,
  chartType = 'area',
  color = '#2563eb'
}) => {
  if (isLoading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500 mb-2">No price data available for {coinName}</p>
          <p className="text-sm text-gray-400">Please check your API connection</p>
        </div>
      </div>
    );
  }

  // Format price for display
  const formatPrice = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`;
    } else if (value >= 1) {
      return `$${value.toFixed(2)}`;
    } else {
      return `$${value.toFixed(6)}`;
    }
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Calculate price change
  const firstPrice = data[0]?.price || 0;
  const lastPrice = data[data.length - 1]?.price || 0;
  const priceChange = lastPrice - firstPrice;
  const priceChangePercent = firstPrice > 0 ? (priceChange / firstPrice) * 100 : 0;

  const ChartComponent = chartType === 'area' ? AreaChart : LineChart;

  return (
    <div className="h-[300px]">
      {/* Price Summary */}
      <div className="mb-4 flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">{coinName} ({coinSymbol.toUpperCase()})</h3>
          <div className="flex items-center gap-2">
            <span className="text-2xl font-bold">{formatPrice(lastPrice)}</span>
            <span className={`text-sm px-2 py-1 rounded ${
              priceChangePercent >= 0 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {priceChangePercent >= 0 ? '+' : ''}{priceChangePercent.toFixed(2)}%
            </span>
          </div>
        </div>
        <div className="text-right text-sm text-gray-500">
          <div>{data.length} data points</div>
          <div>Last updated: {formatDate(data[data.length - 1]?.date || '')}</div>
        </div>
      </div>

      {/* Chart */}
      <ResponsiveContainer width="100%" height="100%">
        <ChartComponent data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
          <XAxis 
            dataKey="date" 
            tickFormatter={formatDate}
            tick={{ fontSize: 12 }}
          />
          <YAxis 
            tickFormatter={formatPrice}
            tick={{ fontSize: 12 }}
            domain={['dataMin * 0.95', 'dataMax * 1.05']}
          />
          <Tooltip 
            formatter={(value: number) => [formatPrice(value), `${coinName} Price`]}
            labelFormatter={(label) => `Date: ${formatDate(label)}`}
            contentStyle={{
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              border: 'none',
              borderRadius: '8px',
              color: 'white'
            }}
          />
          
          {chartType === 'area' ? (
            <Area 
              type="monotone" 
              dataKey="price" 
              stroke={color}
              fill={color}
              fillOpacity={0.3}
              strokeWidth={2}
            />
          ) : (
            <Line 
              type="monotone" 
              dataKey="price" 
              stroke={color}
              strokeWidth={2}
              dot={false}
            />
          )}
        </ChartComponent>
      </ResponsiveContainer>
    </div>
  );
};

export default PriceChart;
