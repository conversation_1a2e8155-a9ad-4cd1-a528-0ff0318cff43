
import { useState } from "react";
import { ChevronUp, ChevronDown, TrendingUp, TrendingDown } from "lucide-react";

interface GridData {
  asset: string;
  symbol: string;
  price: number;
  change24h: number;
  volume: number;
  marketCap: number;
  rating: number;
}

const mockData: GridData[] = [
  { asset: "Bitcoin", symbol: "BTC", price: 67420, change24h: 1.87, volume: 28500000000, marketCap: 1320000000000, rating: 8.5 },
  { asset: "Ethereum", symbol: "ETH", price: 3845, change24h: -3.15, volume: 15200000000, marketCap: 462000000000, rating: 8.2 },
  { asset: "Binance Coin", symbol: "BNB", price: 635, change24h: 2.91, volume: 1850000000, marketCap: 94500000000, rating: 7.8 },
  { asset: "Solana", symbol: "SOL", price: 178, change24h: 5.01, volume: 3200000000, marketCap: 78900000000, rating: 7.9 },
  { asset: "Cardano", symbol: "ADA", price: 0.89, change24h: -3.27, volume: 580000000, marketCap: 31200000000, rating: 6.8 }
];

export default function ProfessionalDataGrid() {
  const [sortConfig, setSortConfig] = useState<{ key: keyof GridData; direction: 'asc' | 'desc' } | null>(null);

  const handleSort = (key: keyof GridData) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const sortedData = [...mockData].sort((a, b) => {
    if (!sortConfig) return 0;
    
    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];
    
    if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
    return 0;
  });

  const formatCurrency = (value: number) => {
    if (value >= 1e9) return `$${(value / 1e9).toFixed(1)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(1)}M`;
    return `$${value.toLocaleString()}`;
  };

  return (
    <div className="bg-gray-900 border border-gray-800 rounded overflow-hidden">
      <div className="bg-gray-800 px-4 py-3 border-b border-gray-700">
        <h3 className="text-white font-semibold text-sm uppercase tracking-wide">Market Overview</h3>
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full text-xs">
          <thead>
            <tr className="bg-gray-800 border-b border-gray-700">
              {[
                { key: 'asset', label: 'ASSET' },
                { key: 'price', label: 'PRICE' },
                { key: 'change24h', label: '24H %' },
                { key: 'volume', label: 'VOLUME' },
                { key: 'marketCap', label: 'MARKET CAP' },
                { key: 'rating', label: 'RATING' }
              ].map(({ key, label }) => (
                <th 
                  key={key}
                  className="text-left px-4 py-3 text-gray-300 font-semibold uppercase tracking-wider cursor-pointer hover:text-white"
                  onClick={() => handleSort(key as keyof GridData)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{label}</span>
                    {sortConfig?.key === key && (
                      sortConfig.direction === 'asc' ? 
                        <ChevronUp className="h-3 w-3" /> : 
                        <ChevronDown className="h-3 w-3" />
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {sortedData.map((item, index) => (
              <tr 
                key={item.symbol} 
                className="border-b border-gray-800 hover:bg-gray-800/50 transition-colors"
              >
                <td className="px-4 py-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center text-black text-xs font-bold">
                      {item.symbol.charAt(0)}
                    </div>
                    <div>
                      <div className="text-white font-medium">{item.asset}</div>
                      <div className="text-gray-400 text-xs">{item.symbol}</div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3 text-white font-mono">
                  ${item.price.toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </td>
                <td className="px-4 py-3">
                  <div className={`flex items-center space-x-1 ${item.change24h >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {item.change24h >= 0 ? (
                      <TrendingUp className="h-3 w-3" />
                    ) : (
                      <TrendingDown className="h-3 w-3" />
                    )}
                    <span className="font-mono">
                      {item.change24h >= 0 ? '+' : ''}{item.change24h.toFixed(2)}%
                    </span>
                  </div>
                </td>
                <td className="px-4 py-3 text-gray-300 font-mono">
                  {formatCurrency(item.volume)}
                </td>
                <td className="px-4 py-3 text-gray-300 font-mono">
                  {formatCurrency(item.marketCap)}
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center space-x-2">
                    <div className="text-orange-500 font-bold">{item.rating}</div>
                    <div className="w-16 bg-gray-700 rounded-full h-1">
                      <div 
                        className="bg-orange-500 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${(item.rating / 10) * 100}%` }}
                      />
                    </div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
