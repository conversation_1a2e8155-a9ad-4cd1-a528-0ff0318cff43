
// Generate prediction data based on historical trends
export const generatePredictions = (historicalData: any[], days: number = 5) => {
  if (!historicalData || historicalData.length < 7) {
    return [];
  }
  
  // Simple prediction model using recent trend and volatility
  const recentData = historicalData.slice(-7);
  const lastClose = recentData[recentData.length - 1].close;
  
  // Calculate average daily change percentage
  let avgChangePercentage = 0;
  for (let i = 1; i < recentData.length; i++) {
    avgChangePercentage += ((recentData[i].close - recentData[i-1].close) / recentData[i-1].close);
  }
  avgChangePercentage = avgChangePercentage / (recentData.length - 1);
  
  // Calculate volatility (standard deviation of daily changes)
  const changes = [];
  for (let i = 1; i < recentData.length; i++) {
    changes.push((recentData[i].close - recentData[i-1].close) / recentData[i-1].close);
  }
  
  const avgChange = changes.reduce((sum, val) => sum + val, 0) / changes.length;
  const variance = changes.reduce((sum, val) => sum + Math.pow(val - avgChange, 2), 0) / changes.length;
  const volatility = Math.sqrt(variance);
  
  // Generate predictions with increasing uncertainty
  const predictions = [];
  let currentClose = lastClose;
  const lastDate = new Date(recentData[recentData.length - 1].time);
  
  for (let i = 1; i <= days; i++) {
    const date = new Date(lastDate);
    date.setDate(date.getDate() + i);
    
    // Increase volatility effect as we predict further
    const volatilityFactor = volatility * (1 + (i * 0.2));
    const randomFactor = (Math.random() - 0.5) * volatilityFactor;
    
    // Calculate predicted change with some randomness
    const change = avgChangePercentage + randomFactor;
    currentClose = currentClose * (1 + change);
    
    // Add confidence interval based on volatility
    const confidenceInterval = currentClose * volatilityFactor * 0.5;
    
    predictions.push({
      time: date.toISOString().split('T')[0],
      open: currentClose * (1 - volatilityFactor * 0.1),
      close: currentClose,
      high: currentClose + confidenceInterval,
      low: currentClose - confidenceInterval,
      predicted: true
    });
  }
  
  return predictions;
};

// Calculate model metrics based on historical performance
export const calculateModelMetrics = (historicalData: any[], predictedData: any[]) => {
  // Simple placeholder metrics - in real app would calculate by backtesting
  return {
    rmse: 2.15 + (Math.random() * 0.6),
    mae: 1.78 + (Math.random() * 0.4),
    rSquared: 0.72 + (Math.random() * 0.15 - 0.05),
    mape: 5.3 + (Math.random() * 1.2)
  };
};
