
// Format time remaining until optimal gas price
export const formatTimeRemaining = (timeString: string): string => {
  const targetTime = new Date(timeString);
  const now = new Date();
  const diffMs = targetTime.getTime() - now.getTime();
  
  if (diffMs < 0) return "Now";
  
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  
  if (diffHours > 0) {
    return `${diffHours}h ${diffMinutes}m`;
  }
  return `${diffMinutes}m`;
};

// Format gas price based on network
export const formatGasPrice = (price: number, networkId: string) => {
  switch (networkId) {
    case 'ethereum':
      return `${price.toFixed(2)} Gwei`;
    case 'polygon':
      return `${price.toFixed(0)} Gwei`;
    case 'arbitrum':
    case 'optimism':
      return `${price.toFixed(3)} Gwei`;
    default:
      return `${price.toFixed(2)} Gwei`;
  }
};

// Calculate estimated transaction cost
export const calculateTxCost = (gasPrice: number, networkId: string) => {
  // Estimated gas units for common transactions
  const gasUnits = {
    'ethereum': {
      simple: 21000,
      token: 65000,
      swap: 150000,
      complex: 250000
    },
    'polygon': {
      simple: 21000,
      token: 65000,
      swap: 180000,
      complex: 300000
    },
    'bsc': {
      simple: 21000,
      token: 60000,
      swap: 140000,
      complex: 230000
    },
    'arbitrum': {
      simple: 150000,
      token: 220000,
      swap: 400000,
      complex: 600000
    },
    'optimism': {
      simple: 130000,
      token: 200000,
      swap: 350000,
      complex: 550000
    }
  };
  
  // Price in USD per native token
  const tokenPrices = {
    'ethereum': 3000,
    'polygon': 0.70,
    'bsc': 305,
    'arbitrum': 3000, // Uses ETH
    'optimism': 3000  // Uses ETH
  };
  
  const network = networkId as keyof typeof gasUnits;
  const gas = gasUnits[network] || gasUnits.ethereum;
  const price = tokenPrices[network as keyof typeof tokenPrices] || 1;
  
  // Convert Gwei to ETH and multiply by price
  const gweiToEth = 0.000000001;
  
  return {
    simple: (gasPrice * gweiToEth * gas.simple * price).toFixed(2),
    token: (gasPrice * gweiToEth * gas.token * price).toFixed(2),
    swap: (gasPrice * gweiToEth * gas.swap * price).toFixed(2),
    complex: (gasPrice * gweiToEth * gas.complex * price).toFixed(2)
  };
};
