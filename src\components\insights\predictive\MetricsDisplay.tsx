
import { PredictionModel } from "@/types/aiInsights";

interface MetricsDisplayProps {
  prediction: PredictionModel;
  potentialReturn: number;
}

export default function MetricsDisplay({ prediction, potentialReturn }: MetricsDisplayProps) {
  const safeToFixed = (value: number | null | undefined, decimals: number = 1) => {
    if (value === null || value === undefined || isNaN(Number(value))) {
      return 'N/A';
    }
    const numValue = Number(value);
    if (!isFinite(numValue)) {
      return 'N/A';
    }
    return numValue.toFixed(decimals);
  };

  const safePercentage = (value: number | null | undefined) => {
    if (value === null || value === undefined || isNaN(Number(value))) {
      return 'N/A';
    }
    const numValue = Number(value);
    if (!isFinite(numValue)) {
      return 'N/A';
    }
    return Math.round(numValue * 100);
  };

  return (
    <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
      <div className="p-3 bg-secondary/30 rounded-md">
        <div className="text-sm text-muted-foreground">Model Confidence</div>
        <div className="text-xl font-bold mt-1">
          {safePercentage(prediction?.metrics?.confidence)}%
        </div>
      </div>
      
      <div className="p-3 bg-secondary/30 rounded-md">
        <div className="text-sm text-muted-foreground">Potential Return</div>
        <div className={`text-xl font-bold mt-1 ${potentialReturn >= 0 ? 'text-crypto-positive' : 'text-crypto-negative'}`}>
          {potentialReturn >= 0 ? '+' : ''}{safeToFixed(potentialReturn, 2)}%
        </div>
      </div>
      
      <div className="p-3 bg-secondary/30 rounded-md">
        <div className="text-sm text-muted-foreground">30D Volatility</div>
        <div className="text-xl font-bold mt-1">
          {safeToFixed(prediction?.metrics?.volatility)}%
        </div>
      </div>
      
      <div className="p-3 bg-secondary/30 rounded-md">
        <div className="text-sm text-muted-foreground">Forecast Error</div>
        <div className="text-xl font-bold mt-1">
          {safeToFixed(prediction?.metrics?.mape)}%
        </div>
      </div>
    </div>
  );
}
