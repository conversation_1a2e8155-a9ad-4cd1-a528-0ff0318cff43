
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Brain, Shield, TrendingUp, Info } from "lucide-react";
import CoinRecommendationCard from "./CoinRecommendationCard";

interface PersonalizedRecommendationsProps {
  data: any;
  isLoading: boolean;
  riskProfile: string;
}

export default function PersonalizedRecommendations({ 
  data, 
  isLoading, 
  riskProfile 
}: PersonalizedRecommendationsProps) {
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-32 bg-gray-200 rounded-lg"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array(6).fill(0).map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-muted-foreground">Unable to load personalized recommendations.</p>
        </CardContent>
      </Card>
    );
  }

  const getRiskProfileColor = (profile: string) => {
    switch (profile) {
      case 'conservative': return 'bg-green-500';
      case 'moderate': return 'bg-yellow-500';
      case 'aggressive': return 'bg-red-500';
      default: return 'bg-blue-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* AI Insights Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-purple-500" />
              AI Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{data.aiInsights}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-500" />
              Risk Assessment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Badge className={getRiskProfileColor(riskProfile)}>
                {riskProfile.toUpperCase()} RISK
              </Badge>
              <p className="text-sm">{data.riskAssessment}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              Strategy Rationale
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{data.rationale}</p>
          </CardContent>
        </Card>
      </div>

      {/* Info Alert */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Recommendations are personalized based on your {riskProfile} risk profile and current market conditions. 
          Always conduct your own research before making investment decisions.
        </AlertDescription>
      </Alert>

      {/* Recommended Coins */}
      <div>
        <h3 className="text-xl font-semibold mb-4">Recommended for You</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {data.coins?.map((coin) => (
            <CoinRecommendationCard key={coin.id} coin={coin} />
          ))}
        </div>
      </div>
    </div>
  );
}
