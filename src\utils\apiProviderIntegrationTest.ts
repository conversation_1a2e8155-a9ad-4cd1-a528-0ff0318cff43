/**
 * API Provider Integration Test
 * Tests the complete integration between API Provider Management and API Key Management
 */

import { getAllApiProviders } from '@/services/api/providers/apiProviderService';
import { validateAllDatabaseProviders } from '@/services/api/dynamicApiKeyValidation';
import { testCoinMarketCapConnection } from '@/services/api/coinMarketCapClient';

export class ApiProviderIntegrationTest {
  /**
   * Test 1: Verify database providers are loaded
   */
  static async testDatabaseProviders(): Promise<{
    success: boolean;
    providers: any[];
    error?: string;
  }> {
    console.log('🗄️ Testing database providers...');
    
    try {
      const providers = await getAllApiProviders();
      console.log(`📊 Found ${providers.length} providers in database:`);
      
      providers.forEach(provider => {
        console.log(`  - ${provider.name} (${provider.type}) - ${provider.is_active ? 'Active' : 'Inactive'}`);
      });
      
      return {
        success: true,
        providers
      };
      
    } catch (error: any) {
      console.error('❌ Database providers test failed:', error);
      return {
        success: false,
        providers: [],
        error: error.message
      };
    }
  }

  /**
   * Test 2: Verify dynamic validation works
   */
  static async testDynamicValidation(): Promise<{
    success: boolean;
    result?: any;
    error?: string;
  }> {
    console.log('🔑 Testing dynamic validation...');
    
    try {
      const result = await validateAllDatabaseProviders();
      console.log('📊 Dynamic validation results:', result);
      
      console.log('Services validated:');
      result.services.forEach(service => {
        const status = service.isValid ? '✅' : service.isRequired ? '❌' : '⚠️';
        console.log(`  ${status} ${service.service}: ${service.isValid ? 'Valid' : service.error}`);
      });
      
      return {
        success: true,
        result
      };
      
    } catch (error: any) {
      console.error('❌ Dynamic validation test failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Test 3: Verify CoinMarketCap specifically
   */
  static async testCoinMarketCapIntegration(): Promise<{
    success: boolean;
    inDatabase: boolean;
    validationWorks: boolean;
    error?: string;
  }> {
    console.log('💰 Testing CoinMarketCap integration...');
    
    try {
      // Check if CoinMarketCap is in database
      const providers = await getAllApiProviders();
      const cmcProvider = providers.find(p => 
        p.name.toLowerCase().includes('coinmarketcap') || 
        p.name.toLowerCase().includes('cmc')
      );
      
      const inDatabase = !!cmcProvider;
      console.log(`📊 CoinMarketCap in database: ${inDatabase ? 'Yes' : 'No'}`);
      
      if (cmcProvider) {
        console.log('📋 CoinMarketCap provider details:', {
          name: cmcProvider.name,
          type: cmcProvider.type,
          isActive: cmcProvider.is_active,
          hasApiKey: !!(cmcProvider.config as any)?.apiKey
        });
      }
      
      // Test validation
      let validationWorks = false;
      try {
        const validationResult = await testCoinMarketCapConnection();
        validationWorks = validationResult.success;
        console.log(`🔑 CoinMarketCap validation: ${validationWorks ? 'Success' : 'Failed'}`);
        
        if (!validationWorks) {
          console.log('❌ Validation error:', validationResult.error);
        }
      } catch (error: any) {
        console.log('❌ Validation test error:', error.message);
      }
      
      return {
        success: inDatabase && validationWorks,
        inDatabase,
        validationWorks
      };
      
    } catch (error: any) {
      console.error('❌ CoinMarketCap integration test failed:', error);
      return {
        success: false,
        inDatabase: false,
        validationWorks: false,
        error: error.message
      };
    }
  }

  /**
   * Test 4: Check API Key Management display
   */
  static async testApiKeyManagementDisplay(): Promise<{
    success: boolean;
    showsNewProviders: boolean;
    error?: string;
  }> {
    console.log('🖥️ Testing API Key Management display...');
    
    try {
      const validationResult = await validateAllDatabaseProviders();
      const serviceNames = validationResult.services.map(s => s.service);
      
      console.log('📊 Services that will be displayed in API Key Management:');
      serviceNames.forEach(name => {
        console.log(`  - ${name}`);
      });
      
      const showsNewProviders = serviceNames.some(name => 
        name.toLowerCase().includes('coinmarketcap') || 
        name.toLowerCase().includes('cmc')
      );
      
      console.log(`💰 CoinMarketCap will be displayed: ${showsNewProviders ? 'Yes' : 'No'}`);
      
      return {
        success: true,
        showsNewProviders
      };
      
    } catch (error: any) {
      console.error('❌ API Key Management display test failed:', error);
      return {
        success: false,
        showsNewProviders: false,
        error: error.message
      };
    }
  }

  /**
   * Run complete integration test
   */
  static async runCompleteIntegrationTest(): Promise<{
    success: boolean;
    results: Record<string, any>;
    recommendations: string[];
  }> {
    console.log('🚀 Running Complete API Provider Integration Test...');
    console.log('=' .repeat(60));
    
    const results: Record<string, any> = {};
    const recommendations: string[] = [];

    // Test 1: Database providers
    results.databaseProviders = await this.testDatabaseProviders();
    
    if (!results.databaseProviders.success) {
      recommendations.push('Fix database connection or run migration script');
      return { success: false, results, recommendations };
    }

    // Test 2: Dynamic validation
    results.dynamicValidation = await this.testDynamicValidation();
    
    if (!results.dynamicValidation.success) {
      recommendations.push('Fix dynamic validation service');
    }

    // Test 3: CoinMarketCap integration
    results.coinMarketCapIntegration = await this.testCoinMarketCapIntegration();
    
    if (!results.coinMarketCapIntegration.inDatabase) {
      recommendations.push('Add CoinMarketCap provider to database via API Provider Management');
    }
    
    if (!results.coinMarketCapIntegration.validationWorks) {
      recommendations.push('Check CoinMarketCap API key configuration');
    }

    // Test 4: API Key Management display
    results.apiKeyManagementDisplay = await this.testApiKeyManagementDisplay();
    
    if (!results.apiKeyManagementDisplay.showsNewProviders) {
      recommendations.push('Switch to Database mode in API Key Management to see new providers');
    }

    const allTestsPassed = Object.values(results).every(result => result.success);

    if (allTestsPassed) {
      recommendations.push('✅ All integration tests passed! New providers should be visible in API Key Management.');
    }

    console.log('=' .repeat(60));
    console.log('📊 Integration Test Results Summary:');
    console.log('Database Providers:', results.databaseProviders.success ? '✅' : '❌');
    console.log('Dynamic Validation:', results.dynamicValidation.success ? '✅' : '❌');
    console.log('CoinMarketCap Integration:', results.coinMarketCapIntegration.success ? '✅' : '❌');
    console.log('API Key Management Display:', results.apiKeyManagementDisplay.success ? '✅' : '❌');
    console.log('Overall:', allTestsPassed ? '✅ PASS' : '❌ FAIL');
    console.log('=' .repeat(60));

    if (recommendations.length > 0) {
      console.log('📋 Recommendations:');
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }

    return {
      success: allTestsPassed,
      results,
      recommendations
    };
  }
}

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).apiProviderIntegrationTest = ApiProviderIntegrationTest;
  (window as any).testApiProviderIntegration = () => ApiProviderIntegrationTest.runCompleteIntegrationTest();
}
