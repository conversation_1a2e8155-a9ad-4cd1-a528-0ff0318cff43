/**
 * CORS-Aware API Diagnostic Tool
 * Helps diagnose API connectivity issues with proper CORS understanding
 */

import { getAllApiProviders } from '@/services/api/providers/apiProviderService';

export class CorsAwareDiagnostic {
  /**
   * List of APIs known to have CORS restrictions
   */
  static readonly CORS_RESTRICTED_APIS = [
    'coinmarketcap.com',
    'pro-api.coinmarketcap.com',
    'cryptocompare.com',
    'min-api.cryptocompare.com',
    'glassnode.com',
    'api.glassnode.com'
  ];

  /**
   * List of APIs that typically allow CORS
   */
  static readonly CORS_FRIENDLY_APIS = [
    'coingecko.com',
    'api.coingecko.com',
    'geckoterminal.com',
    'api.geckoterminal.com',
    'deepseek.com',
    'api.deepseek.com'
  ];

  /**
   * Check if a URL is likely to have CORS restrictions
   */
  static isCorsRestricted(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      return this.CORS_RESTRICTED_APIS.some(restrictedDomain => 
        hostname.includes(restrictedDomain)
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if a URL is likely to be CORS-friendly
   */
  static isCorsFriendly(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      return this.CORS_FRIENDLY_APIS.some(friendlyDomain => 
        hostname.includes(friendlyDomain)
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Get CORS status for a provider
   */
  static getCorsStatus(baseUrl: string): {
    status: 'restricted' | 'friendly' | 'unknown';
    canTestDirectly: boolean;
    reason: string;
  } {
    if (this.isCorsRestricted(baseUrl)) {
      return {
        status: 'restricted',
        canTestDirectly: false,
        reason: 'API provider blocks direct browser requests (CORS policy)'
      };
    }
    
    if (this.isCorsFriendly(baseUrl)) {
      return {
        status: 'friendly',
        canTestDirectly: true,
        reason: 'API provider allows direct browser requests'
      };
    }
    
    return {
      status: 'unknown',
      canTestDirectly: false,
      reason: 'CORS policy unknown - assuming restricted for safety'
    };
  }

  /**
   * Validate API key format for known providers
   */
  static validateApiKeyFormat(providerName: string, apiKey: string): {
    isValid: boolean;
    expectedFormat: string;
    error?: string;
  } {
    const name = providerName.toLowerCase();
    
    if (name.includes('coinmarketcap')) {
      // CoinMarketCap uses UUID format
      const isValid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(apiKey);
      return {
        isValid,
        expectedFormat: 'UUID format (e.g., 12345678-1234-1234-1234-123456789abc)',
        error: isValid ? undefined : 'API key should be in UUID format'
      };
    }
    
    if (name.includes('coingecko')) {
      // CoinGecko uses CG- prefix
      const isValid = apiKey.startsWith('CG-') && apiKey.length > 10;
      return {
        isValid,
        expectedFormat: 'CG- prefix format (e.g., CG-abcdefghijklmnop)',
        error: isValid ? undefined : 'API key should start with "CG-" and be longer than 10 characters'
      };
    }
    
    if (name.includes('deepseek')) {
      // DeepSeek uses sk- prefix
      const isValid = apiKey.startsWith('sk-') && apiKey.length > 10;
      return {
        isValid,
        expectedFormat: 'sk- prefix format (e.g., sk-abcdefghijklmnop)',
        error: isValid ? undefined : 'API key should start with "sk-" and be longer than 10 characters'
      };
    }
    
    // Generic validation
    const isValid = apiKey.length >= 8;
    return {
      isValid,
      expectedFormat: 'At least 8 characters',
      error: isValid ? undefined : 'API key should be at least 8 characters long'
    };
  }

  /**
   * Analyze all providers for CORS and configuration issues
   */
  static async analyzeAllProviders(): Promise<{
    success: boolean;
    providers: Array<{
      name: string;
      baseUrl: string;
      corsStatus: ReturnType<typeof CorsAwareDiagnostic.getCorsStatus>;
      keyValidation: ReturnType<typeof CorsAwareDiagnostic.validateApiKeyFormat>;
      hasApiKey: boolean;
      recommendations: string[];
    }>;
    summary: {
      total: number;
      corsRestricted: number;
      corsFriendly: number;
      corsUnknown: number;
      validKeys: number;
      invalidKeys: number;
    };
  }> {
    console.log('🔍 Analyzing all providers for CORS and configuration issues...');
    
    try {
      const providers = await getAllApiProviders();
      const analysis = [];
      
      for (const provider of providers) {
        const config = provider.config as any;
        const baseUrl = config.baseUrl || config.base_url || '';
        const apiKey = config.apiKey || config.api_key || '';
        
        const corsStatus = this.getCorsStatus(baseUrl);
        const keyValidation = this.validateApiKeyFormat(provider.name, apiKey);
        
        const recommendations = [];
        
        if (!apiKey) {
          recommendations.push('Configure API key in provider settings');
        } else if (!keyValidation.isValid) {
          recommendations.push(`Fix API key format: ${keyValidation.error}`);
        }
        
        if (corsStatus.status === 'restricted') {
          recommendations.push('Direct browser testing not possible due to CORS - validation based on configuration only');
        }
        
        if (!baseUrl) {
          recommendations.push('Configure base URL in provider settings');
        }
        
        analysis.push({
          name: provider.name,
          baseUrl,
          corsStatus,
          keyValidation,
          hasApiKey: !!apiKey,
          recommendations
        });
      }
      
      const summary = {
        total: analysis.length,
        corsRestricted: analysis.filter(p => p.corsStatus.status === 'restricted').length,
        corsFriendly: analysis.filter(p => p.corsStatus.status === 'friendly').length,
        corsUnknown: analysis.filter(p => p.corsStatus.status === 'unknown').length,
        validKeys: analysis.filter(p => p.keyValidation.isValid).length,
        invalidKeys: analysis.filter(p => !p.keyValidation.isValid).length
      };
      
      console.log('📊 Provider Analysis Summary:', summary);
      
      analysis.forEach(provider => {
        const corsIcon = provider.corsStatus.status === 'restricted' ? '🚫' : 
                        provider.corsStatus.status === 'friendly' ? '✅' : '❓';
        const keyIcon = provider.keyValidation.isValid ? '🔑' : '❌';
        
        console.log(`${corsIcon}${keyIcon} ${provider.name}:`);
        console.log(`  CORS: ${provider.corsStatus.reason}`);
        console.log(`  Key: ${provider.keyValidation.isValid ? 'Valid' : provider.keyValidation.error}`);
        
        if (provider.recommendations.length > 0) {
          console.log(`  Recommendations:`);
          provider.recommendations.forEach(rec => console.log(`    - ${rec}`));
        }
      });
      
      return {
        success: true,
        providers: analysis,
        summary
      };
      
    } catch (error: any) {
      console.error('❌ Provider analysis failed:', error);
      return {
        success: false,
        providers: [],
        summary: {
          total: 0,
          corsRestricted: 0,
          corsFriendly: 0,
          corsUnknown: 0,
          validKeys: 0,
          invalidKeys: 0
        }
      };
    }
  }

  /**
   * Get recommendations for fixing network errors
   */
  static getNetworkErrorRecommendations(providerName: string, baseUrl: string): string[] {
    const recommendations = [];
    
    if (this.isCorsRestricted(baseUrl)) {
      recommendations.push(
        `${providerName} blocks direct browser requests due to CORS policy`,
        'This is normal and expected for financial APIs',
        'Validation is based on API key format and configuration only',
        'The API will work correctly in production server environments'
      );
    } else {
      recommendations.push(
        'Check your internet connection',
        'Verify the API endpoint URL is correct',
        'Check if the API service is currently available',
        'Ensure your API key has the correct permissions'
      );
    }
    
    return recommendations;
  }
}

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).corsAwareDiagnostic = CorsAwareDiagnostic;
  (window as any).analyzeCorsIssues = () => CorsAwareDiagnostic.analyzeAllProviders();
}
