
import React from "react";
import { Badge } from "@/components/ui/badge";

interface SecurityBadgeProps {
  score: number;
  showText?: boolean;
}

const SecurityBadge = ({ score, showText = false }: SecurityBadgeProps) => {
  let bgColor = "bg-green-100 text-green-800";
  let label = "High";
  
  if (score <= 4) {
    bgColor = "bg-red-100 text-red-800";
    label = "Low";
  } else if (score <= 7) {
    bgColor = "bg-yellow-100 text-yellow-800";
    label = "Medium";
  }

  return (
    <Badge variant="outline" className={`${bgColor} font-medium`}>
      {score}/10 {showText && `- ${label}`}
    </Badge>
  );
};

export default SecurityBadge;
