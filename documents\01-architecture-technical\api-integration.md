# 🔌 API Integration

## Overview

CryptoVision Pro integrates with multiple external APIs to provide comprehensive cryptocurrency market intelligence. The integration layer is designed for reliability, performance, and graceful degradation.

## 🌐 External API Sources

### **1. CoinGecko API** (Primary Market Data)
**Purpose**: Core cryptocurrency market data, prices, and statistics
**Reliability**: Primary data source with comprehensive coverage

```typescript
// Configuration
const coinGeckoAxios = axios.create({
  baseURL: 'https://api.coingecko.com/api/v3',
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    ...(process.env.VITE_COINGECKO_API_KEY && process.env.VITE_COINGECKO_API_KEY !== 'demo_key' ? {
      'x-cg-demo-api-key': process.env.VITE_COINGECKO_API_KEY
    } : {})
  },
  timeout: 15000
});

// Key Endpoints
const endpoints = {
  topCoins: '/coins/markets',           // Top cryptocurrencies
  globalData: '/global',               // Global market statistics
  trending: '/search/trending',        // Trending coins
  coinDetails: '/coins/{id}',          // Detailed coin information
  priceHistory: '/coins/{id}/market_chart', // Historical price data
  exchanges: '/exchanges',             // Exchange information
};
```

**Data Retrieved**:
- Real-time cryptocurrency prices
- Market capitalization and volume
- Price change percentages
- Global market statistics
- Trending cryptocurrencies
- Exchange rankings and trust scores

### **2. DeepSeek AI API** (AI-Powered Insights)
**Purpose**: AI-generated market analysis, sentiment analysis, and insights
**Reliability**: Secondary service with fallback to cached/mock data

```typescript
// Configuration
const deepSeekAxios = axios.create({
  baseURL: 'https://api.deepseek.com',
  headers: {
    'Authorization': `Bearer ${process.env.VITE_DEEPSEEK_API_KEY}`,
    'Content-Type': 'application/json'
  },
  timeout: 30000
});

// Usage Examples
const aiInsights = await generateAIResponse({
  model: "deepseek-chat",
  messages: [
    {
      role: "user",
      content: "Analyze current Bitcoin market conditions"
    }
  ]
});
```

**Capabilities**:
- Market sentiment analysis
- AI-generated news summaries
- Pattern recognition insights
- Risk assessment analysis
- Investment strategy recommendations
- Token correlation analysis
- Event impact assessment
- Volatility prediction modeling
- Team credibility analysis

### **3. GeckoTerminal API** (On-Chain Data)
**Purpose**: DEX data, liquidity pools, and on-chain metrics
**Reliability**: Specialized on-chain data with fallback mechanisms

```typescript
// Configuration
const geckoTerminalClient = axios.create({
  baseURL: 'https://api.geckoterminal.com/api/v2',
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Key Endpoints
const endpoints = {
  networkStats: '/networks/{network}/stats',
  topPools: '/networks/{network}/pools',
  poolInfo: '/networks/{network}/pools/{address}',
  tokenInfo: '/networks/{network}/tokens/{address}'
};
```

**Data Retrieved**:
- DEX trading volumes
- Liquidity pool information
- Token price data from DEXs
- Network activity statistics
- On-chain transaction metrics

### **4. Supabase** (Authentication & Database)
**Purpose**: User authentication, profile management, and data storage
**Reliability**: Primary authentication service with session management

```typescript
// Configuration
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

// Authentication Flow
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
});

// Database Operations
const { data: profile } = await supabase
  .from('profiles')
  .select('*')
  .eq('id', user.id)
  .single();
```

**Services Provided**:
- User authentication (email/password, social)
- User profile management
- Portfolio data storage
- Educational progress tracking
- User preferences and settings

## 🚀 Enhanced Analytics Services

### **1. Token Correlation Analysis Service**
**Purpose**: Advanced correlation analysis between cryptocurrencies
**Implementation**: `correlationAnalysisService.ts`

```typescript
// Correlation Matrix Calculation
export async function fetchCorrelationMatrix(days: number = 30): Promise<CorrelationResponse> {
  try {
    // Fetch price data for top cryptocurrencies
    const topCoins = await coinGeckoAxios.get('/coins/markets', {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: 50,
        page: 1,
        sparkline: false
      }
    });

    // Calculate correlation coefficients
    const correlationMatrix = calculateCorrelationMatrix(priceData);
    const clusters = performClusterAnalysis(correlationMatrix);

    return {
      correlationMatrix,
      clusters,
      riskScores: calculateRiskScores(correlationMatrix),
      success: true
    };
  } catch (error) {
    return handleCorrelationError(error);
  }
}
```

**Features**:
- Real-time correlation matrix calculation
- AI-powered clustering algorithms
- Risk/reward scoring system
- Interactive network visualization

### **2. Event Impact Analysis Service**
**Purpose**: Track and analyze market events and their price impact
**Implementation**: `eventImpactAnalysisService.ts`

```typescript
// Event Impact Analysis
export async function fetchEventImpactData(timeframe: number = 30): Promise<EventImpactResponse> {
  try {
    // Fetch market events from multiple sources
    const events = await aggregateMarketEvents(timeframe);

    // Analyze price impact using AI
    const impactAnalysis = await analyzeEventImpact(events);

    return {
      events: events.map(event => ({
        ...event,
        impactScore: calculateImpactScore(event),
        sentiment: analyzeSentiment(event.description),
        priceCorrelation: calculatePriceCorrelation(event)
      })),
      timeline: generateEventTimeline(events),
      predictions: generateImpactPredictions(events),
      success: true
    };
  } catch (error) {
    return handleEventAnalysisError(error);
  }
}
```

**Features**:
- Calendar-based event tracking
- NLP-powered sentiment analysis
- Impact prediction modeling
- Historical correlation analysis

### **3. Volatility Analysis Service**
**Purpose**: ML-based volatility prediction and risk assessment
**Implementation**: `volatilityAnalysisService.ts`

```typescript
// Volatility Prediction
export async function fetchVolatilityAnalysis(): Promise<VolatilityResponse> {
  try {
    // Fetch historical volatility data
    const volatilityData = await calculateHistoricalVolatility();

    // Apply ML models for prediction
    const predictions = await generateVolatilityPredictions(volatilityData);

    return {
      currentVolatility: volatilityData.current,
      predictions: predictions,
      riskCategories: categorizeByRisk(volatilityData),
      investorProfiles: generateInvestorRecommendations(predictions),
      success: true
    };
  } catch (error) {
    return handleVolatilityError(error);
  }
}
```

**Features**:
- ML-based volatility prediction
- Risk/return quadrant analysis
- Investor type categorization
- Real-time volatility rankings

### **4. Team Analysis Service**
**Purpose**: AI-powered team credibility and developer analysis
**Implementation**: `teamAnalysisService.ts`

```typescript
// Team Credibility Analysis
export async function fetchTeamAnalysis(): Promise<TeamAnalysisResponse> {
  try {
    // Analyze development activity
    const devActivity = await analyzeDeveloperActivity();

    // Assess team transparency
    const transparency = await assessTeamTransparency();

    // Generate credibility scores
    const credibilityScores = await generateCredibilityScores();

    return {
      teams: devActivity.map(team => ({
        ...team,
        credibilityScore: credibilityScores[team.id],
        transparencyLevel: transparency[team.id],
        socialProof: analyzeSocialProof(team),
        riskAssessment: assessTeamRisk(team)
      })),
      rankings: generateTeamRankings(credibilityScores),
      insights: generateTeamInsights(),
      success: true
    };
  } catch (error) {
    return handleTeamAnalysisError(error);
  }
}
```

**Features**:
- GitHub activity analysis
- Team transparency assessment
- Social proof verification
- Credibility scoring system

### **5. Etherscan API** (Ethereum Data)
**Purpose**: Ethereum blockchain data and smart contract information
**Reliability**: Optional service for enhanced Ethereum analytics

```typescript
// Configuration
const etherscanApi = axios.create({
  baseURL: 'https://api.etherscan.io/api',
  params: {
    apikey: process.env.VITE_ETHERSCAN_API_KEY
  }
});

// Usage Examples
const transactions = await etherscanApi.get('', {
  params: {
    module: 'account',
    action: 'txlist',
    address: '0x...',
    startblock: 0,
    endblock: ********
  }
});
```

## 🔄 Integration Architecture

### **API Client Structure**
```typescript
// Base API Client Pattern
class APIClient {
  private axios: AxiosInstance;
  private cache: Map<string, CacheEntry>;
  private rateLimiter: RateLimiter;

  constructor(config: APIConfig) {
    this.axios = axios.create(config);
    this.setupInterceptors();
    this.setupErrorHandling();
  }

  async get<T>(endpoint: string, params?: any): Promise<T> {
    // Check cache first
    const cacheKey = this.generateCacheKey(endpoint, params);
    const cached = this.getFromCache(cacheKey);
    if (cached && !this.isStale(cached)) {
      return cached.data;
    }

    // Rate limiting
    await this.rateLimiter.acquire();

    try {
      const response = await this.axios.get(endpoint, { params });
      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      return this.handleError(error, cacheKey);
    }
  }
}
```

### **Error Handling Strategy**
```typescript
// Multi-layer error handling
export const handleApiError = (error: any, fallback: any = null) => {
  console.error("API error:", error);

  // Network errors
  if (error.code === 'NETWORK_ERROR') {
    return getCachedData() || fallback;
  }

  // Rate limiting
  if (error.response?.status === 429) {
    return getCachedData() || fallback;
  }

  // Server errors
  if (error.response?.status >= 500) {
    return getCachedData() || fallback;
  }

  // Client errors
  if (error.response?.status >= 400) {
    throw new APIError(error.response.data.message);
  }

  return fallback;
};
```

### **Caching Strategy**
```typescript
// Multi-level caching system
const cacheConfig = {
  // Memory cache (React Query)
  memory: {
    staleTime: 5 * 60 * 1000,      // 5 minutes
    cacheTime: 30 * 60 * 1000      // 30 minutes
  },

  // Local storage cache
  localStorage: {
    marketData: 2 * 60 * 1000,     // 2 minutes
    globalStats: 5 * 60 * 1000,    // 5 minutes
    historicalData: 30 * 60 * 1000, // 30 minutes
    staticData: 24 * 60 * 60 * 1000 // 24 hours
  },

  // Service worker cache
  serviceWorker: {
    duration: 7 * 24 * 60 * 60 * 1000 // 7 days
  }
};
```

## 🛡️ Resilience Patterns

### **1. Circuit Breaker Pattern**
```typescript
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
}
```

### **2. Retry with Exponential Backoff**
```typescript
const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  maxRetries = 3
): Promise<T> => {
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) throw error;

      const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  throw new Error('Max retries exceeded');
};
```

### **3. Graceful Degradation**
```typescript
const fetchMarketDataWithFallback = async () => {
  try {
    // Try primary API
    return await coinGeckoApi.getMarketData();
  } catch (error) {
    console.warn('Primary API failed, trying fallback');

    try {
      // Try alternative API
      return await alternativeApi.getMarketData();
    } catch (fallbackError) {
      console.warn('Fallback API failed, using cached data');

      // Use cached data
      const cached = getCachedMarketData();
      if (cached) return cached;

      // Return minimal data
      return getMinimalMarketData();
    }
  }
};
```

## 📊 Rate Limiting & Quotas

### **API Limits**
```typescript
const rateLimits = {
  coinGecko: {
    free: 50,        // requests per minute
    pro: 500         // requests per minute
  },
  deepSeek: {
    standard: 100,   // requests per minute
    premium: 1000    // requests per minute
  },
  supabase: {
    free: 1000,      // requests per minute
    pro: 10000       // requests per minute
  }
};
```

### **Rate Limiter Implementation**
```typescript
class RateLimiter {
  private requests: number[] = [];
  private limit: number;
  private window: number;

  constructor(limit: number, windowMs: number) {
    this.limit = limit;
    this.window = windowMs;
  }

  async acquire(): Promise<void> {
    const now = Date.now();

    // Remove old requests outside the window
    this.requests = this.requests.filter(
      time => now - time < this.window
    );

    if (this.requests.length >= this.limit) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.window - (now - oldestRequest);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return this.acquire();
    }

    this.requests.push(now);
  }
}
```

## 🔐 Security Considerations

### **API Key Management**
```typescript
// Environment-based configuration
const apiKeys = {
  coinGecko: process.env.VITE_COINGECKO_API_KEY,
  deepSeek: process.env.VITE_DEEPSEEK_API_KEY,
  supabase: {
    url: process.env.VITE_SUPABASE_URL,
    anonKey: process.env.VITE_SUPABASE_ANON_KEY
  }
};

// Validation
const validateApiKeys = () => {
  const required = ['VITE_COINGECKO_API_KEY'];
  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    console.warn(`Missing API keys: ${missing.join(', ')}`);
  }
};
```

### **Request Sanitization**
```typescript
const sanitizeParams = (params: any) => {
  const sanitized = { ...params };

  // Remove sensitive data
  delete sanitized.password;
  delete sanitized.apiKey;

  // Validate and sanitize inputs
  Object.keys(sanitized).forEach(key => {
    if (typeof sanitized[key] === 'string') {
      sanitized[key] = sanitized[key].trim();
    }
  });

  return sanitized;
};
```

## 📋 API Integration Summary

### **Integration Health Monitoring**
```typescript
const apiHealthCheck = {
  coinGecko: {
    status: 'operational',
    responseTime: '< 500ms',
    uptime: '99.9%',
    lastCheck: new Date()
  },
  deepSeek: {
    status: 'operational',
    responseTime: '< 2s',
    uptime: '99.5%',
    lastCheck: new Date()
  },
  supabase: {
    status: 'operational',
    responseTime: '< 200ms',
    uptime: '99.99%',
    lastCheck: new Date()
  }
};
```

### **Data Flow Optimization**
- **Parallel Requests** - Multiple API calls executed concurrently
- **Request Batching** - Combine related API calls to reduce overhead
- **Smart Caching** - Intelligent cache invalidation based on data freshness
- **Background Refresh** - Update cached data in background for seamless UX

### **Error Recovery Strategies**
- **Automatic Retry** - Exponential backoff for transient failures
- **Circuit Breaker** - Prevent cascade failures across services
- **Fallback Data** - Cached or mock data when APIs are unavailable
- **Graceful Degradation** - Reduced functionality rather than complete failure

This integration architecture ensures reliable, performant, and secure access to external data sources while providing graceful degradation when services are unavailable.
