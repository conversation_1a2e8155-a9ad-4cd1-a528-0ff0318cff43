
/**
 * Database verification and sample data generation utility
 */

import { supabase } from '@/integrations/supabase/client';

/**
 * Verify that all required tables exist
 */
export async function verifyDatabaseTables(): Promise<boolean> {
  const requiredTables = ['api_providers', 'api_costs', 'api_usage_logs', 'ai_prompt_templates'] as const;
  
  for (const table of requiredTables) {
    try {
      const { error } = await supabase.from(table).select('id').limit(1);
      if (error) {
        console.error(`❌ Table ${table} verification failed:`, error);
        return false;
      }
    } catch (error) {
      console.error(`❌ Table ${table} not accessible:`, error);
      return false;
    }
  }
  
  console.log('✅ All required tables exist and are accessible');
  return true;
}

/**
 * Generate realistic sample data for testing
 */
export async function generateRealisticSampleData(): Promise<boolean> {
  try {
    console.log('🔄 Starting sample data generation...');
    
    // Check if tables exist first
    const tablesExist = await verifyDatabaseTables();
    if (!tablesExist) {
      console.error('❌ Required tables do not exist. Please run migrations first.');
      return false;
    }

    // Generate sample providers
    const sampleProviders = [
      {
        name: 'CoinGecko Pro',
        type: 'market' as const,
        priority: 1,
        rate_limit_per_minute: 50,
        monthly_quota: 10000,
        cost_per_request: 0.001,
        is_active: true,
        config: { baseUrl: 'https://api.coingecko.com/api/v3', timeout: 15000 }
      },
      {
        name: 'DeepSeek AI',
        type: 'ai' as const,
        priority: 1,
        rate_limit_per_minute: 100,
        monthly_quota: 1000000,
        cost_per_token: 0.00000014,
        is_active: true,
        config: { baseUrl: 'https://api.deepseek.com', timeout: 60000 }
      },
      {
        name: 'Etherscan',
        type: 'onchain' as const,
        priority: 2,
        rate_limit_per_minute: 5,
        monthly_quota: 100000,
        cost_per_request: 0,
        is_active: true,
        config: { baseUrl: 'https://api.etherscan.io', timeout: 15000 }
      }
    ];

    // Insert providers
    const { data: insertedProviders, error: providersError } = await supabase
      .from('api_providers')
      .insert(sampleProviders)
      .select();

    if (providersError) {
      console.error('❌ Error inserting providers:', providersError);
      return false;
    }

    console.log(`✅ Inserted ${insertedProviders?.length || 0} sample providers`);

    // Generate sample costs for today
    const today = new Date().toISOString().split('T')[0];
    const sampleCosts = insertedProviders?.map(provider => ({
      provider_id: provider.id,
      date: today,
      requests_count: Math.floor(Math.random() * 100) + 10,
      tokens_used: provider.type === 'ai' ? Math.floor(Math.random() * 10000) + 1000 : 0,
      estimated_cost: Math.random() * 5 + 0.1
    })) || [];

    if (sampleCosts.length > 0) {
      const { error: costsError } = await supabase
        .from('api_costs')
        .insert(sampleCosts);

      if (costsError) {
        console.error('❌ Error inserting costs:', costsError);
        return false;
      }

      console.log(`✅ Inserted ${sampleCosts.length} sample cost records`);
    }

    // Generate sample usage logs
    const sampleLogs = Array.from({ length: 20 }, () => ({
      provider: ['coingecko', 'deepseek', 'etherscan'][Math.floor(Math.random() * 3)],
      endpoint: ['/coins/markets', '/chat/completions', '/api'][Math.floor(Math.random() * 3)],
      user_id: null, // Anonymous usage for now
      response_time_ms: Math.floor(Math.random() * 2000) + 100,
      status_code: Math.random() > 0.1 ? 200 : 500,
      request_count: 1
    }));

    const { error: logsError } = await supabase
      .from('api_usage_logs')
      .insert(sampleLogs);

    if (logsError) {
      console.error('❌ Error inserting logs:', logsError);
      return false;
    }

    console.log(`✅ Inserted ${sampleLogs.length} sample usage logs`);

    console.log('🎉 Sample data generation completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Sample data generation failed:', error);
    return false;
  }
}

/**
 * Clear all sample data (useful for testing)
 */
export async function clearSampleData(): Promise<boolean> {
  try {
    console.log('🧹 Clearing sample data...');

    // Clear in reverse dependency order
    await supabase.from('api_usage_logs').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('api_costs').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('api_providers').delete().neq('id', '00000000-0000-0000-0000-000000000000');

    console.log('✅ Sample data cleared');
    return true;
  } catch (error) {
    console.error('❌ Error clearing sample data:', error);
    return false;
  }
}
