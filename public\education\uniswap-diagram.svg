<svg xmlns="http://www.w3.org/2000/svg" width="800" height="500" viewBox="0 0 800 500" fill="none">
  <defs>
    <linearGradient id="poolGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF007A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C70039;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="tokenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="500" fill="#0F172A"/>
  
  <!-- Title -->
  <text x="400" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Uniswap AMM Protocol</text>
  <text x="400" y="65" text-anchor="middle" fill="#94A3B8" font-family="Arial, sans-serif" font-size="14">Automated Market Maker with Constant Product Formula</text>
  
  <!-- Central Liquidity Pool -->
  <g transform="translate(350, 200)">
    <ellipse cx="50" cy="50" rx="80" ry="60" fill="url(#poolGradient)" stroke="#FF1493" stroke-width="3"/>
    <text x="50" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Liquidity Pool</text>
    <text x="50" y="55" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">ETH/USDC</text>
    <text x="50" y="70" text-anchor="middle" fill="#FFB6C1" font-family="Arial, sans-serif" font-size="10">x * y = k</text>
  </g>
  
  <!-- Token A (ETH) -->
  <g transform="translate(150, 180)">
    <circle cx="30" cy="30" r="25" fill="url(#tokenGradient)" stroke="#60A5FA" stroke-width="2"/>
    <text x="30" y="35" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">ETH</text>
    <text x="30" y="70" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">1000 ETH</text>
  </g>
  
  <!-- Token B (USDC) -->
  <g transform="translate(550, 180)">
    <circle cx="30" cy="30" r="25" fill="#10B981" stroke="#34D399" stroke-width="2"/>
    <text x="30" y="35" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">USDC</text>
    <text x="30" y="70" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="11">2M USDC</text>
  </g>
  
  <!-- Arrows to pool -->
  <path d="M 205 210 L 330 230" stroke="#06B6D4" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 550 210 L 450 230" stroke="#06B6D4" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Liquidity Provider -->
  <g transform="translate(350, 100)">
    <rect width="100" height="60" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="50" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Liquidity</text>
    <text x="50" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Provider</text>
    <text x="50" y="55" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="10">Earns 0.3% fees</text>
  </g>
  
  <!-- Arrow from LP to pool -->
  <path d="M 400 160 L 400 190" stroke="#10B981" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Trader -->
  <g transform="translate(100, 350)">
    <rect width="80" height="50" rx="8" fill="#374151" stroke="#4B5563" stroke-width="2"/>
    <text x="40" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Trader</text>
    <text x="40" y="40" text-anchor="middle" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">Swaps tokens</text>
  </g>
  
  <!-- Trading arrows -->
  <path d="M 180 360 Q 250 320 330 280" stroke="#F59E0B" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <text x="220" y="340" fill="#F59E0B" font-family="Arial, sans-serif" font-size="10">Swap ETH → USDC</text>
  
  <!-- Price calculation -->
  <g transform="translate(500, 350)">
    <rect width="200" height="80" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Price Calculation</text>
    <text x="100" y="40" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Price = USDC Reserve / ETH Reserve</text>
    <text x="100" y="55" text-anchor="middle" fill="#F59E0B" font-family="Arial, sans-serif" font-size="10">Current: 2,000,000 / 1,000 = $2,000</text>
    <text x="100" y="70" text-anchor="middle" fill="#94A3B8" font-family="Arial, sans-serif" font-size="9">Price changes with each trade</text>
  </g>
  
  <!-- Formula explanation -->
  <g transform="translate(50, 100)">
    <rect width="180" height="100" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="90" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Constant Product</text>
    <text x="90" y="40" text-anchor="middle" fill="#FF1493" font-family="Arial, sans-serif" font-size="14" font-weight="bold">x * y = k</text>
    <text x="90" y="60" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">x = ETH reserves</text>
    <text x="90" y="75" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">y = USDC reserves</text>
    <text x="90" y="90" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">k = constant</text>
  </g>
  
  <!-- Benefits -->
  <g transform="translate(50, 450)">
    <text x="0" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓ No Order Books</text>
    <text x="150" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓ Always Liquid</text>
    <text x="280" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓ Permissionless</text>
    <text x="420" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓ Earn Trading Fees</text>
    <text x="580" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓ Decentralized</text>
  </g>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#06B6D4"/>
    </marker>
  </defs>
</svg>
