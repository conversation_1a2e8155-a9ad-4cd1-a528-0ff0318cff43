import { useState, useEffect, useCallback } from 'react';
import {
  MarketSentiment,
  MarketMetrics,
  SectorPerformance,
  MarketNews,
  LiquidityMetrics,
  VolatilityData,
  fetchMarketSentiment,
  fetchEnhancedMarketMetrics,
  fetchSectorPerformance,
  fetchMarketNews,
  fetchLiquidityMetrics,
  fetchVolatilityData
} from '@/services/api/enhancedMarketInsights';

interface UseEnhancedMarketInsightsReturn {
  // Data
  sentiment: MarketSentiment | null;
  metrics: MarketMetrics | null;
  sectors: SectorPerformance[];
  news: MarketNews[];
  liquidity: LiquidityMetrics | null;
  volatility: VolatilityData | null;
  
  // Loading states
  sentimentLoading: boolean;
  metricsLoading: boolean;
  sectorsLoading: boolean;
  newsLoading: boolean;
  liquidityLoading: boolean;
  volatilityLoading: boolean;
  
  // Error states
  sentimentError: string | null;
  metricsError: string | null;
  sectorsError: string | null;
  newsError: string | null;
  liquidityError: string | null;
  volatilityError: string | null;
  
  // Actions
  refreshSentiment: () => Promise<void>;
  refreshMetrics: () => Promise<void>;
  refreshSectors: () => Promise<void>;
  refreshNews: () => Promise<void>;
  refreshLiquidity: () => Promise<void>;
  refreshVolatility: () => Promise<void>;
  refreshAll: () => Promise<void>;
  
  // Computed values
  overallMarketHealth: 'excellent' | 'good' | 'neutral' | 'poor' | 'critical';
  marketTrend: 'bullish' | 'bearish' | 'sideways';
  riskLevel: 'low' | 'medium' | 'high' | 'extreme';
}

export const useEnhancedMarketInsights = (): UseEnhancedMarketInsightsReturn => {
  // State for data
  const [sentiment, setSentiment] = useState<MarketSentiment | null>(null);
  const [metrics, setMetrics] = useState<MarketMetrics | null>(null);
  const [sectors, setSectors] = useState<SectorPerformance[]>([]);
  const [news, setNews] = useState<MarketNews[]>([]);
  const [liquidity, setLiquidity] = useState<LiquidityMetrics | null>(null);
  const [volatility, setVolatility] = useState<VolatilityData | null>(null);
  
  // Loading states
  const [sentimentLoading, setSentimentLoading] = useState(false);
  const [metricsLoading, setMetricsLoading] = useState(false);
  const [sectorsLoading, setSectorsLoading] = useState(false);
  const [newsLoading, setNewsLoading] = useState(false);
  const [liquidityLoading, setLiquidityLoading] = useState(false);
  const [volatilityLoading, setVolatilityLoading] = useState(false);
  
  // Error states
  const [sentimentError, setSentimentError] = useState<string | null>(null);
  const [metricsError, setMetricsError] = useState<string | null>(null);
  const [sectorsError, setSectorsError] = useState<string | null>(null);
  const [newsError, setNewsError] = useState<string | null>(null);
  const [liquidityError, setLiquidityError] = useState<string | null>(null);
  const [volatilityError, setVolatilityError] = useState<string | null>(null);

  // Fetch functions
  const refreshSentiment = useCallback(async () => {
    setSentimentLoading(true);
    setSentimentError(null);
    try {
      const data = await fetchMarketSentiment();
      setSentiment(data);
    } catch (error) {
      setSentimentError(error instanceof Error ? error.message : 'Failed to fetch sentiment data');
    } finally {
      setSentimentLoading(false);
    }
  }, []);

  const refreshMetrics = useCallback(async () => {
    setMetricsLoading(true);
    setMetricsError(null);
    try {
      const data = await fetchEnhancedMarketMetrics();
      setMetrics(data);
    } catch (error) {
      setMetricsError(error instanceof Error ? error.message : 'Failed to fetch metrics data');
    } finally {
      setMetricsLoading(false);
    }
  }, []);

  const refreshSectors = useCallback(async () => {
    setSectorsLoading(true);
    setSectorsError(null);
    try {
      const data = await fetchSectorPerformance();
      setSectors(data);
    } catch (error) {
      setSectorsError(error instanceof Error ? error.message : 'Failed to fetch sectors data');
    } finally {
      setSectorsLoading(false);
    }
  }, []);

  const refreshNews = useCallback(async () => {
    setNewsLoading(true);
    setNewsError(null);
    try {
      const data = await fetchMarketNews();
      setNews(data);
    } catch (error) {
      setNewsError(error instanceof Error ? error.message : 'Failed to fetch news data');
    } finally {
      setNewsLoading(false);
    }
  }, []);

  const refreshLiquidity = useCallback(async () => {
    setLiquidityLoading(true);
    setLiquidityError(null);
    try {
      const data = await fetchLiquidityMetrics();
      setLiquidity(data);
    } catch (error) {
      setLiquidityError(error instanceof Error ? error.message : 'Failed to fetch liquidity data');
    } finally {
      setLiquidityLoading(false);
    }
  }, []);

  const refreshVolatility = useCallback(async () => {
    setVolatilityLoading(true);
    setVolatilityError(null);
    try {
      const data = await fetchVolatilityData();
      setVolatility(data);
    } catch (error) {
      setVolatilityError(error instanceof Error ? error.message : 'Failed to fetch volatility data');
    } finally {
      setVolatilityLoading(false);
    }
  }, []);

  const refreshAll = useCallback(async () => {
    await Promise.all([
      refreshSentiment(),
      refreshMetrics(),
      refreshSectors(),
      refreshNews(),
      refreshLiquidity(),
      refreshVolatility()
    ]);
  }, [refreshSentiment, refreshMetrics, refreshSectors, refreshNews, refreshLiquidity, refreshVolatility]);

  // Computed values
  const overallMarketHealth = useCallback((): UseEnhancedMarketInsightsReturn['overallMarketHealth'] => {
    if (!sentiment || !metrics || !volatility) return 'neutral';
    
    const sentimentScore = sentiment.fearGreedIndex;
    const marketCapChange = metrics.marketCapChange24h;
    const volatilityScore = volatility.marketVolatility;
    
    // Calculate composite health score
    const healthScore = (
      (sentimentScore * 0.4) +
      ((marketCapChange + 10) * 5 * 0.3) + // Normalize market cap change
      ((20 - volatilityScore) * 5 * 0.3) // Lower volatility = better health
    );
    
    if (healthScore >= 80) return 'excellent';
    if (healthScore >= 65) return 'good';
    if (healthScore >= 35) return 'neutral';
    if (healthScore >= 20) return 'poor';
    return 'critical';
  }, [sentiment, metrics, volatility]);

  const marketTrend = useCallback((): UseEnhancedMarketInsightsReturn['marketTrend'] => {
    if (!metrics || !sentiment) return 'sideways';
    
    const marketCapChange = metrics.marketCapChange24h;
    const sentimentScore = sentiment.fearGreedIndex;
    
    if (marketCapChange > 2 && sentimentScore > 60) return 'bullish';
    if (marketCapChange < -2 && sentimentScore < 40) return 'bearish';
    return 'sideways';
  }, [metrics, sentiment]);

  const riskLevel = useCallback((): UseEnhancedMarketInsightsReturn['riskLevel'] => {
    if (!volatility || !sentiment) return 'medium';
    
    const volatilityScore = volatility.marketVolatility;
    const sentimentScore = sentiment.fearGreedIndex;
    
    // Extreme sentiment + high volatility = extreme risk
    if ((sentimentScore > 80 || sentimentScore < 20) && volatilityScore > 8) return 'extreme';
    if (volatilityScore > 6) return 'high';
    if (volatilityScore > 3) return 'medium';
    return 'low';
  }, [volatility, sentiment]);

  // Initial data fetch
  useEffect(() => {
    refreshAll();
  }, []);

  // Auto-refresh every 5 minutes for critical data
  useEffect(() => {
    const interval = setInterval(() => {
      refreshSentiment();
      refreshMetrics();
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [refreshSentiment, refreshMetrics]);

  // Auto-refresh every 15 minutes for less critical data
  useEffect(() => {
    const interval = setInterval(() => {
      refreshSectors();
      refreshLiquidity();
      refreshVolatility();
    }, 15 * 60 * 1000);

    return () => clearInterval(interval);
  }, [refreshSectors, refreshLiquidity, refreshVolatility]);

  // Auto-refresh news every 30 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      refreshNews();
    }, 30 * 60 * 1000);

    return () => clearInterval(interval);
  }, [refreshNews]);

  return {
    // Data
    sentiment,
    metrics,
    sectors,
    news,
    liquidity,
    volatility,
    
    // Loading states
    sentimentLoading,
    metricsLoading,
    sectorsLoading,
    newsLoading,
    liquidityLoading,
    volatilityLoading,
    
    // Error states
    sentimentError,
    metricsError,
    sectorsError,
    newsError,
    liquidityError,
    volatilityError,
    
    // Actions
    refreshSentiment,
    refreshMetrics,
    refreshSectors,
    refreshNews,
    refreshLiquidity,
    refreshVolatility,
    refreshAll,
    
    // Computed values
    overallMarketHealth: overallMarketHealth(),
    marketTrend: marketTrend(),
    riskLevel: riskLevel()
  };
};
