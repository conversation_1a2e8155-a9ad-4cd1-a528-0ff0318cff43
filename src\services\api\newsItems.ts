
import { fetchTopCoins, fetchTrendingCoins } from "./coinMarketData";
import { cacheResponse, handleApiError } from "./coinGeckoClient";
import { generateStories } from "./newsUtils";

// Fetch news items based on real market data
export const fetchNewsItems = async () => {
  try {
    // Get real market data to inform our news generation
    const topCoins = await fetchTopCoins(20);
    const trendingCoins = await fetchTrendingCoins();
    
    // Generate positive, neutral, and negative stories
    const positiveStories = generateStories(5, 'positive', topCoins, trendingCoins);
    const neutralStories = generateStories(8, 'neutral', topCoins, trendingCoins);
    const negativeStories = generateStories(4, 'negative', topCoins, trendingCoins);
    
    // Combine and shuffle the stories
    const allStories = [...positiveStories, ...neutralStories, ...negativeStories]
      .sort(() => Math.random() - 0.5);
    
    cacheResponse("news", allStories);
    return allStories;
  } catch (error) {
    return handleApiError(error, {
      key: "news",
      data: []
    });
  }
};
