
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface Coin {
  id: string;
  name: string;
  symbol: string;
}

interface VisualizationSelectorProps {
  coins: Coin[];
  selectedCoin: Coin;
  onCoinSelect: (coin: Coin) => void;
  isLoading: boolean;
}

export default function VisualizationSelector({
  coins,
  selectedCoin,
  onCoinSelect,
  isLoading
}: VisualizationSelectorProps) {
  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <Loader2 size={16} className="animate-spin" />
        <span>Loading coins...</span>
      </div>
    );
  }

  return (
    <div className="flex flex-wrap gap-3 mb-4">
      {coins?.map((coin) => (
        <Button
          key={coin.id}
          variant={selectedCoin.id === coin.id ? 'default' : 'outline'}
          onClick={() => onCoinSelect(coin)}
          className="flex items-center gap-2"
        >
          {coin.name} ({coin.symbol})
        </Button>
      ))}
    </div>
  );
}
