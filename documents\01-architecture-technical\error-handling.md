# 🛡️ Error Handling & Resilience

## Overview

CryptoVision Pro implements a comprehensive error handling strategy that ensures graceful degradation, user-friendly error messages, and system resilience across all layers of the application.

## 🎯 Error Handling Strategy

### **Multi-Layer Error Handling**
```
┌─────────────────────────────────────────────────────────────┐
│                    Error Handling Layers                    │
├─────────────────────────────────────────────────────────────┤
│ 1. Global Error Boundary    │  Catches unhandled errors    │
│ 2. Component Error Boundary │  Component-level recovery    │
│ 3. API Error Handling       │  Service-level error mgmt    │
│ 4. Hook Error States        │  Business logic errors       │
│ 5. Form Validation          │  User input validation       │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Implementation Patterns

### **1. Global Error Boundary**
```typescript
// Global error boundary for unhandled errors
class GlobalErrorBoundary extends Component<
  { children: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Global error caught:', error, errorInfo);
    
    // Report to error tracking service
    this.reportError(error, errorInfo);
  }

  reportError = (error: Error, errorInfo: ErrorInfo) => {
    // Send to error tracking service (Sentry, LogRocket, etc.)
    if (process.env.NODE_ENV === 'production') {
      // Sentry.captureException(error, { extra: errorInfo });
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-8 w-8 text-red-500 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                Something went wrong
              </h1>
            </div>
            <p className="text-gray-600 mb-4">
              We're sorry, but something unexpected happened. Please try refreshing the page.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => window.location.reload()}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                Refresh Page
              </button>
              <button
                onClick={() => this.setState({ hasError: false })}
                className="flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### **2. API Error Handling**
```typescript
// Centralized API error handling
export class APIError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// API error handler with fallback strategies
export const handleApiError = async (error: any, fallbackFn?: () => any) => {
  console.error('API Error:', error);

  // Network errors
  if (!error.response) {
    const cachedData = await getCachedData();
    if (cachedData) {
      console.warn('Using cached data due to network error');
      return cachedData;
    }
    throw new APIError('Network error. Please check your connection.');
  }

  // HTTP status errors
  const { status, data } = error.response;
  
  switch (status) {
    case 400:
      throw new APIError('Invalid request. Please check your input.', status);
    
    case 401:
      // Handle authentication errors
      localStorage.removeItem('auth_token');
      window.location.href = '/auth';
      throw new APIError('Authentication required.', status);
    
    case 403:
      throw new APIError('Access denied. Insufficient permissions.', status);
    
    case 404:
      throw new APIError('Resource not found.', status);
    
    case 429:
      // Rate limiting - use cached data or retry
      const retryAfter = error.response.headers['retry-after'];
      console.warn(`Rate limited. Retry after ${retryAfter} seconds`);
      
      const cached = await getCachedData();
      if (cached) return cached;
      
      throw new APIError(`Too many requests. Please try again in ${retryAfter} seconds.`, status);
    
    case 500:
    case 502:
    case 503:
    case 504:
      // Server errors - use fallback or cached data
      if (fallbackFn) {
        console.warn('Server error, using fallback');
        return await fallbackFn();
      }
      
      const fallbackData = await getCachedData();
      if (fallbackData) {
        console.warn('Server error, using cached data');
        return fallbackData;
      }
      
      throw new APIError('Server error. Please try again later.', status);
    
    default:
      throw new APIError(data?.message || 'An unexpected error occurred.', status);
  }
};
```

### **3. React Query Error Handling**
```typescript
// React Query error handling configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      onError: (error: any) => {
        console.error('Query error:', error);
        
        // Show user-friendly error message
        if (error instanceof APIError) {
          toast.error(error.message);
        } else {
          toast.error('Failed to load data. Please try again.');
        }
      },
    },
    mutations: {
      onError: (error: any) => {
        console.error('Mutation error:', error);
        
        if (error instanceof APIError) {
          toast.error(error.message);
        } else {
          toast.error('Operation failed. Please try again.');
        }
      },
    },
  },
});
```

### **4. Component-Level Error Boundaries**
```typescript
// Reusable error boundary for specific components
export const ComponentErrorBoundary: React.FC<{
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}> = ({ children, fallback, onError }) => {
  return (
    <ErrorBoundary
      fallback={fallback || <DefaultErrorFallback />}
      onError={(error, errorInfo) => {
        console.error('Component error:', error, errorInfo);
        onError?.(error, errorInfo);
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

const DefaultErrorFallback = () => (
  <div className="p-4 border border-red-200 rounded-lg bg-red-50">
    <div className="flex items-center mb-2">
      <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
      <h3 className="text-red-800 font-medium">Unable to load content</h3>
    </div>
    <p className="text-red-600 text-sm mb-3">
      This section couldn't be loaded. Please try refreshing the page.
    </p>
    <button
      onClick={() => window.location.reload()}
      className="text-sm bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
    >
      Refresh
    </button>
  </div>
);
```

### **5. Hook Error States**
```typescript
// Custom hook with comprehensive error handling
export const useMarketDataWithErrorHandling = (coinIds: string[]) => {
  const [errorState, setErrorState] = useState<{
    hasError: boolean;
    error?: Error;
    isUsingFallback: boolean;
  }>({
    hasError: false,
    isUsingFallback: false
  });

  const query = useQuery({
    queryKey: ['marketData', coinIds],
    queryFn: async () => {
      try {
        setErrorState({ hasError: false, isUsingFallback: false });
        return await fetchMarketData(coinIds);
      } catch (error) {
        console.error('Market data fetch failed:', error);
        
        // Try to get cached data
        const cachedData = getCachedMarketData(coinIds);
        if (cachedData) {
          setErrorState({ 
            hasError: true, 
            error: error as Error, 
            isUsingFallback: true 
          });
          return cachedData;
        }
        
        // If no cached data, throw error
        setErrorState({ 
          hasError: true, 
          error: error as Error, 
          isUsingFallback: false 
        });
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000,
    onError: (error) => {
      setErrorState({ 
        hasError: true, 
        error: error as Error, 
        isUsingFallback: false 
      });
    }
  });

  return {
    ...query,
    errorState,
    retry: () => {
      setErrorState({ hasError: false, isUsingFallback: false });
      query.refetch();
    }
  };
};
```

## 🔄 Fallback Strategies

### **1. Cached Data Fallback**
```typescript
// Intelligent cache fallback system
class FallbackCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl: number = 24 * 60 * 60 * 1000) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
    
    // Also store in localStorage for persistence
    try {
      localStorage.setItem(`fallback_${key}`, JSON.stringify({
        data,
        timestamp: Date.now(),
        ttl
      }));
    } catch (error) {
      console.warn('Failed to store fallback data in localStorage:', error);
    }
  }

  get(key: string): any | null {
    // Check memory cache first
    const memoryEntry = this.cache.get(key);
    if (memoryEntry && this.isValid(memoryEntry)) {
      return memoryEntry.data;
    }

    // Check localStorage
    try {
      const stored = localStorage.getItem(`fallback_${key}`);
      if (stored) {
        const entry = JSON.parse(stored);
        if (this.isValid(entry)) {
          // Restore to memory cache
          this.cache.set(key, entry);
          return entry.data;
        }
      }
    } catch (error) {
      console.warn('Failed to retrieve fallback data from localStorage:', error);
    }

    return null;
  }

  private isValid(entry: { timestamp: number; ttl: number }): boolean {
    return Date.now() - entry.timestamp < entry.ttl;
  }
}

export const fallbackCache = new FallbackCache();
```

### **2. Progressive Degradation**
```typescript
// Component with progressive degradation
export const MarketDataDisplay: React.FC<{ coinIds: string[] }> = ({ coinIds }) => {
  const { data, isLoading, error, errorState } = useMarketDataWithErrorHandling(coinIds);

  // Full functionality
  if (data && !errorState.hasError) {
    return <FullMarketDataView data={data} />;
  }

  // Degraded functionality with cached data
  if (data && errorState.isUsingFallback) {
    return (
      <div>
        <div className="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
          <Info className="inline h-4 w-4 mr-1" />
          Showing cached data. Live data temporarily unavailable.
        </div>
        <CachedMarketDataView data={data} />
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return <MarketDataSkeleton />;
  }

  // Error state with minimal functionality
  if (error) {
    return (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <div className="flex items-center mb-2">
          <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
          <h3 className="text-red-800 font-medium">Market data unavailable</h3>
        </div>
        <p className="text-red-600 text-sm mb-3">
          Unable to load current market data. Please check your connection and try again.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="text-sm bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return null;
};
```

## 📊 Error Monitoring & Analytics

### **Error Tracking**
```typescript
// Error analytics and reporting
class ErrorTracker {
  private errors: Array<{
    error: Error;
    timestamp: number;
    context: any;
    userId?: string;
  }> = [];

  track(error: Error, context: any = {}) {
    const errorEntry = {
      error,
      timestamp: Date.now(),
      context,
      userId: getCurrentUserId()
    };

    this.errors.push(errorEntry);
    
    // Report to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportToService(errorEntry);
    }

    // Log locally for development
    console.error('Error tracked:', errorEntry);
  }

  private reportToService(errorEntry: any) {
    // Send to error tracking service
    // Sentry.captureException(errorEntry.error, {
    //   extra: errorEntry.context,
    //   user: { id: errorEntry.userId }
    // });
  }

  getErrorStats() {
    const now = Date.now();
    const last24h = this.errors.filter(e => now - e.timestamp < 24 * 60 * 60 * 1000);
    
    return {
      total: this.errors.length,
      last24h: last24h.length,
      mostCommon: this.getMostCommonErrors(last24h),
      errorRate: last24h.length / 24 // errors per hour
    };
  }

  private getMostCommonErrors(errors: any[]) {
    const errorCounts = errors.reduce((acc, { error }) => {
      const key = error.message;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(errorCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);
  }
}

export const errorTracker = new ErrorTracker();
```

This comprehensive error handling strategy ensures that CryptoVision Pro remains functional and user-friendly even when encountering various types of errors and service disruptions.
