
import React from "react";
import {
  AreaChart,
  Area,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
} from "recharts";

interface GasForecastChartProps {
  combinedChartData: Array<{
    time: string;
    price?: number;
    predicted?: number;
    timestamp: number | string;
  }>;
  recommendedPrice: number;
}

const GasForecastChart = ({
  combinedChartData,
  recommendedPrice,
}: GasForecastChartProps) => {
  return (
    <div className="h-[350px]">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={combinedChartData}
          margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
        >
          <defs>
            <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#8884d8" stopOpacity={0.1} />
            </linearGradient>
            <linearGradient id="colorPredicted" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#82ca9d" stopOpacity={0.1} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
          <XAxis dataKey="time" tick={{ fontSize: 12 }} dy={10} />
          <YAxis
            tickFormatter={(value) => `${value.toFixed(1)}`}
            tick={{ fontSize: 12 }}
            domain={["auto", "auto"]}
          />
          <Tooltip
            formatter={(value: any) => [
              `${parseFloat(value).toFixed(2)} Gwei`,
              value.hasOwnProperty("predicted") ? "Predicted" : "Actual",
            ]}
            labelFormatter={(label) => `Time: ${label}`}
          />
          <Legend verticalAlign="top" height={36} />

          <Area
            type="monotone"
            dataKey="price"
            stroke="#8884d8"
            fillOpacity={1}
            fill="url(#colorPrice)"
            name="Historical"
            connectNulls
          />
          <Area
            type="monotone"
            dataKey="predicted"
            stroke="#82ca9d"
            fillOpacity={1}
            fill="url(#colorPredicted)"
            name="Forecast"
            strokeDasharray="5 5"
            connectNulls
          />

          <ReferenceLine
            y={recommendedPrice}
            stroke="orange"
            strokeDasharray="3 3"
            label={{
              value: "Recommended",
              position: "insideTopRight",
              fill: "orange",
              fontSize: 12,
            }}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default GasForecastChart;
