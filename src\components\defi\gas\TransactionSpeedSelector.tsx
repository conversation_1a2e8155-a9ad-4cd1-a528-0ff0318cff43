
import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock } from "lucide-react";

interface TransactionSpeedSelectorProps {
  speedOption: "fast" | "standard" | "slow";
  setSpeedOption: (speed: "fast" | "standard" | "slow") => void;
  estimatedTimes: {
    fast: number;
    standard: number;
    slow: number;
  };
  recommendations: {
    fast: { price: number; time: string };
    standard: { price: number; time: string };
    slow: { price: number; time: string };
  };
  networkId: string;
  formatGasPrice: (price: number, networkId: string) => string;
}

const TransactionSpeedSelector = ({
  speedOption,
  setSpeedOption,
  estimatedTimes,
  recommendations,
  networkId,
  formatGasPrice,
}: TransactionSpeedSelectorProps) => {
  return (
    <div>
      <h3 className="font-medium mb-3">Transaction Speed</h3>
      <div className="flex flex-col gap-2">
        <Button
          variant={speedOption === "fast" ? "default" : "outline"}
          className="justify-between"
          onClick={() => setSpeedOption("fast")}
        >
          <span className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Fast (~{estimatedTimes.fast} min)
          </span>
          <Badge variant="secondary">
            {formatGasPrice(recommendations.fast.price, networkId)}
          </Badge>
        </Button>

        <Button
          variant={speedOption === "standard" ? "default" : "outline"}
          className="justify-between"
          onClick={() => setSpeedOption("standard")}
        >
          <span className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Standard (~{estimatedTimes.standard} min)
          </span>
          <Badge variant="secondary">
            {formatGasPrice(recommendations.standard.price, networkId)}
          </Badge>
        </Button>

        <Button
          variant={speedOption === "slow" ? "default" : "outline"}
          className="justify-between"
          onClick={() => setSpeedOption("slow")}
        >
          <span className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Low Priority (~{estimatedTimes.slow} min)
          </span>
          <Badge variant="secondary">
            {formatGasPrice(recommendations.slow.price, networkId)}
          </Badge>
        </Button>
      </div>
    </div>
  );
};

export default TransactionSpeedSelector;
