
import { cn } from "@/lib/utils";
import { ArrowUpRight, ArrowDownRight } from "lucide-react";

interface PriceChangeIndicatorProps {
  changePercentage: number;
}

export function PriceChangeIndicator({ changePercentage }: PriceChangeIndicatorProps) {
  return (
    <div className={cn(
      "inline-flex items-center",
      changePercentage >= 0 ? "text-green-500" : "text-red-500"
    )}>
      {changePercentage >= 0 
        ? <ArrowUpRight size={14} /> 
        : <ArrowDownRight size={14} />
      }
      <span className="ml-0.5 font-medium">{Math.abs(changePercentage).toFixed(2)}%</span>
    </div>
  );
}
