
import React, { useState } from 'react';
import { useQuery } from "@tanstack/react-query";
import { Loader2 } from 'lucide-react';

// Enhanced components
import { useEnhancedMarketInsights } from '@/hooks/useEnhancedMarketInsights';
import MarketHealthSummary from '@/components/market/MarketHealthSummary';
import MarketInsightsTabs from '@/components/market/MarketInsightsTabs';

// Legacy components
import HeaderBar from "@/components/HeaderBar";
import GlobalMarketStats from "@/components/market/GlobalMarketStats";
import { fetchGlobalData, fetchTopCoins } from "@/services/api/coinMarketData";
import { toast } from "@/hooks/use-toast";

export default function MarketInsights() {
  const [activeTab, setActiveTab] = useState("overview");

  // Enhanced market insights data (with graceful fallback)
  let enhancedData;
  try {
    enhancedData = useEnhancedMarketInsights();
  } catch (error) {
    console.error('Enhanced market insights failed:', error);
    enhancedData = {
      sentiment: null,
      metrics: null,
      sectors: [],
      news: [],
      liquidity: null,
      volatility: null,
      sentimentLoading: false,
      metricsLoading: false,
      sectorsLoading: false,
      newsLoading: false,
      liquidityLoading: false,
      volatilityLoading: false,
      sentimentError: 'Enhanced insights unavailable',
      metricsError: null,
      sectorsError: null,
      newsError: null,
      liquidityError: null,
      volatilityError: null,
      overallMarketHealth: 'neutral' as const,
      marketTrend: 'sideways' as const,
      riskLevel: 'medium' as const,
      refreshAll: async () => {}
    };
  }

  const {
    sentiment,
    metrics,
    sectors,
    news,
    liquidity,
    volatility,
    sentimentLoading,
    metricsLoading,
    sectorsLoading,
    newsLoading,
    liquidityLoading,
    volatilityLoading,
    sentimentError,
    metricsError,
    sectorsError,
    newsError,
    liquidityError,
    volatilityError,
    overallMarketHealth,
    marketTrend,
    riskLevel,
    refreshAll
  } = enhancedData;

  // Legacy data queries for basic market data
  const { data: globalData, isLoading: isLoadingGlobal, error: globalError } = useQuery({
    queryKey: ['globalMarketData'],
    queryFn: fetchGlobalData,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: topCoins, isLoading: isLoadingCoins, error: coinsError } = useQuery({
    queryKey: ['topCoins'],
    queryFn: () => fetchTopCoins(50),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Loading and data availability checks
  const isEnhancedLoading = sentimentLoading || metricsLoading || sectorsLoading || newsLoading || liquidityLoading || volatilityLoading;
  const hasEnhancedData = sentiment || metrics || sectors.length > 0 || news.length > 0 || liquidity || volatility;
  const isBasicLoading = isLoadingGlobal || isLoadingCoins;

  // Error handling
  if (globalError || coinsError) {
    toast({
      title: "Error loading data",
      description: "Could not load market data. Please try again later.",
      variant: "destructive",
    });
  }

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar
        title="Market Intelligence Center"
        description="Comprehensive cryptocurrency market analysis with real-time insights"
      />

      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">

          {/* Enhanced Market Health Summary (if data available) */}
          {hasEnhancedData && (
            <MarketHealthSummary
              sentiment={sentiment}
              metrics={metrics}
              volatility={volatility}
              overallMarketHealth={overallMarketHealth}
              marketTrend={marketTrend}
              riskLevel={riskLevel}
              isEnhancedLoading={isEnhancedLoading}
              refreshAll={refreshAll}
            />
          )}

          {/* Loading indicator */}
          {(isBasicLoading || isEnhancedLoading) && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading market data...</span>
            </div>
          )}

          {/* Global Market Stats */}
          {globalData && !isLoadingGlobal && (
            <GlobalMarketStats globalData={globalData} />
          )}

          {/* Unified Tabs Interface */}
          <MarketInsightsTabs
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            hasEnhancedData={hasEnhancedData}
            metrics={metrics}
            volatility={volatility}
            overallMarketHealth={overallMarketHealth}
            marketTrend={marketTrend}
            riskLevel={riskLevel}
            metricsLoading={metricsLoading}
            volatilityLoading={volatilityLoading}
            topCoins={topCoins}
            isLoadingCoins={isLoadingCoins}
            sentiment={sentiment}
            sentimentLoading={sentimentLoading}
            liquidity={liquidity}
            liquidityLoading={liquidityLoading}
            sectors={sectors}
            sectorsLoading={sectorsLoading}
            news={news}
            newsLoading={newsLoading}
          />
        </div>
      </main>
    </div>
  );
}
