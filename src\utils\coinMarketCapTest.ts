/**
 * CoinMarketCap Specific Test Suite
 * Tests CoinMarketCap integration with CORS awareness
 */

import { getAllApiProviders } from '@/services/api/providers/apiProviderService';
import { testCoinMarketCapConnection } from '@/services/api/coinMarketCapClient';
import { validateAllDatabaseProviders } from '@/services/api/dynamicApiKeyValidation';

export class CoinMarketCapTest {
  /**
   * Test 1: Check if CoinMarketCap provider exists in database
   */
  static async testProviderInDatabase(): Promise<{
    success: boolean;
    provider?: any;
    error?: string;
  }> {
    console.log('🗄️ Testing CoinMarketCap provider in database...');
    
    try {
      const providers = await getAllApiProviders();
      const cmcProvider = providers.find(p => 
        p.name.toLowerCase().includes('coinmarketcap') || 
        p.name.toLowerCase().includes('cmc')
      );
      
      if (!cmcProvider) {
        return {
          success: false,
          error: 'CoinMarketCap provider not found in database'
        };
      }
      
      console.log('✅ CoinMarketCap provider found:', {
        name: cmcProvider.name,
        type: cmcProvider.type,
        isActive: cmcProvider.is_active,
        priority: cmcProvider.priority,
        config: cmcProvider.config
      });
      
      return {
        success: true,
        provider: cmcProvider
      };
      
    } catch (error: any) {
      console.error('❌ Database test failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Test 2: Validate API key configuration
   */
  static async testApiKeyConfiguration(): Promise<{
    success: boolean;
    hasEnvKey: boolean;
    hasDbKey: boolean;
    keyFormat: {
      isValid: boolean;
      error?: string;
    };
    error?: string;
  }> {
    console.log('🔑 Testing CoinMarketCap API key configuration...');
    
    try {
      // Check environment variable
      const envKey = import.meta.env.VITE_COINMARKETCAP_API_KEY;
      const hasEnvKey = !!envKey;
      
      console.log('📊 Environment API key:', hasEnvKey ? 'Configured' : 'Not configured');
      
      // Check database provider
      const providers = await getAllApiProviders();
      const cmcProvider = providers.find(p => 
        p.name.toLowerCase().includes('coinmarketcap') || 
        p.name.toLowerCase().includes('cmc')
      );
      
      const dbKey = cmcProvider ? (cmcProvider.config as any)?.apiKey : null;
      const hasDbKey = !!dbKey;
      
      console.log('📊 Database API key:', hasDbKey ? 'Configured' : 'Not configured');
      
      // Validate key format
      const keyToValidate = dbKey || envKey;
      let keyFormat = { isValid: false, error: 'No API key found' };
      
      if (keyToValidate) {
        const isValidFormat = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(keyToValidate);
        keyFormat = {
          isValid: isValidFormat,
          error: isValidFormat ? undefined : 'API key should be in UUID format (e.g., 12345678-1234-1234-1234-123456789abc)'
        };
      }
      
      console.log('🔍 API key format validation:', keyFormat);
      
      return {
        success: hasEnvKey || hasDbKey,
        hasEnvKey,
        hasDbKey,
        keyFormat
      };
      
    } catch (error: any) {
      console.error('❌ API key configuration test failed:', error);
      return {
        success: false,
        hasEnvKey: false,
        hasDbKey: false,
        keyFormat: { isValid: false, error: 'Test failed' },
        error: error.message
      };
    }
  }

  /**
   * Test 3: Test CORS-aware validation
   */
  static async testCorsAwareValidation(): Promise<{
    success: boolean;
    validationResult?: any;
    corsHandled: boolean;
    error?: string;
  }> {
    console.log('🌐 Testing CORS-aware validation...');
    
    try {
      const result = await testCoinMarketCapConnection();
      
      console.log('📊 CoinMarketCap connection test result:', result);
      
      // Check if CORS is properly handled
      const corsHandled = result.error?.includes('CORS') || 
                         result.error?.includes('Configuration valid') ||
                         result.success;
      
      return {
        success: result.success || corsHandled,
        validationResult: result,
        corsHandled
      };
      
    } catch (error: any) {
      console.error('❌ CORS-aware validation test failed:', error);
      return {
        success: false,
        corsHandled: false,
        error: error.message
      };
    }
  }

  /**
   * Test 4: Test integration with dynamic validation system
   */
  static async testDynamicValidationIntegration(): Promise<{
    success: boolean;
    cmcInResults: boolean;
    cmcStatus?: any;
    error?: string;
  }> {
    console.log('🔄 Testing dynamic validation integration...');
    
    try {
      const result = await validateAllDatabaseProviders();
      
      const cmcService = result.services.find(s => 
        s.service.toLowerCase().includes('coinmarketcap') || 
        s.service.toLowerCase().includes('cmc')
      );
      
      const cmcInResults = !!cmcService;
      
      console.log('📊 CoinMarketCap in validation results:', cmcInResults ? 'Yes' : 'No');
      
      if (cmcService) {
        console.log('📋 CoinMarketCap validation status:', {
          service: cmcService.service,
          isConfigured: cmcService.isConfigured,
          isValid: cmcService.isValid,
          error: cmcService.error
        });
      }
      
      return {
        success: cmcInResults,
        cmcInResults,
        cmcStatus: cmcService
      };
      
    } catch (error: any) {
      console.error('❌ Dynamic validation integration test failed:', error);
      return {
        success: false,
        cmcInResults: false,
        error: error.message
      };
    }
  }

  /**
   * Run complete CoinMarketCap test suite
   */
  static async runCompleteTest(): Promise<{
    success: boolean;
    results: Record<string, any>;
    recommendations: string[];
  }> {
    console.log('🚀 Running Complete CoinMarketCap Test Suite...');
    console.log('=' .repeat(60));
    
    const results: Record<string, any> = {};
    const recommendations: string[] = [];

    // Test 1: Provider in database
    results.providerInDatabase = await this.testProviderInDatabase();
    
    if (!results.providerInDatabase.success) {
      recommendations.push('Add CoinMarketCap provider via API Provider Management');
      recommendations.push('Use provider name "CoinMarketCap" and type "market"');
    }

    // Test 2: API key configuration
    results.apiKeyConfiguration = await this.testApiKeyConfiguration();
    
    if (!results.apiKeyConfiguration.success) {
      recommendations.push('Configure CoinMarketCap API key in environment variables or provider settings');
      recommendations.push('Get API key from: https://coinmarketcap.com/api/');
    } else if (!results.apiKeyConfiguration.keyFormat.isValid) {
      recommendations.push(`Fix API key format: ${results.apiKeyConfiguration.keyFormat.error}`);
    }

    // Test 3: CORS-aware validation
    results.corsAwareValidation = await this.testCorsAwareValidation();
    
    if (!results.corsAwareValidation.corsHandled) {
      recommendations.push('CORS handling may need improvement');
    }

    // Test 4: Dynamic validation integration
    results.dynamicValidationIntegration = await this.testDynamicValidationIntegration();
    
    if (!results.dynamicValidationIntegration.success) {
      recommendations.push('Check dynamic validation system integration');
    }

    const allTestsPassed = Object.values(results).every(result => result.success);

    if (allTestsPassed) {
      recommendations.push('✅ All CoinMarketCap tests passed! Provider should be working correctly.');
      recommendations.push('Note: Network errors are expected due to CORS - this is normal behavior.');
    }

    console.log('=' .repeat(60));
    console.log('📊 CoinMarketCap Test Results Summary:');
    console.log('Provider in Database:', results.providerInDatabase.success ? '✅' : '❌');
    console.log('API Key Configuration:', results.apiKeyConfiguration.success ? '✅' : '❌');
    console.log('CORS-Aware Validation:', results.corsAwareValidation.success ? '✅' : '❌');
    console.log('Dynamic Validation Integration:', results.dynamicValidationIntegration.success ? '✅' : '❌');
    console.log('Overall:', allTestsPassed ? '✅ PASS' : '❌ FAIL');
    console.log('=' .repeat(60));

    if (recommendations.length > 0) {
      console.log('📋 Recommendations:');
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }

    return {
      success: allTestsPassed,
      results,
      recommendations
    };
  }
}

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).coinMarketCapTest = CoinMarketCapTest;
  (window as any).testCoinMarketCap = () => CoinMarketCapTest.runCompleteTest();
}
