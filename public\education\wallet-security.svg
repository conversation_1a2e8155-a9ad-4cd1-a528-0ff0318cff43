<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400" fill="none">
  <defs>
    <linearGradient id="walletGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1F2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="securityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="#0F172A"/>
  
  <!-- Title -->
  <text x="400" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="22" font-weight="bold">Cryptocurrency Wallet Security</text>
  <text x="400" y="50" text-anchor="middle" fill="#94A3B8" font-family="Arial, sans-serif" font-size="14">Best practices for keeping your crypto safe</text>
  
  <!-- Hardware Wallet -->
  <g transform="translate(100, 100)">
    <rect width="120" height="80" rx="12" fill="url(#walletGradient)" stroke="#6B7280" stroke-width="2"/>
    <rect width="80" height="40" x="20" y="20" rx="4" fill="#1F2937" stroke="#4B5563" stroke-width="1"/>
    <circle cx="60" cy="65" r="8" fill="#10B981"/>
    <text x="60" y="110" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Hardware Wallet</text>
    <text x="60" y="125" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="10">Most Secure</text>
  </g>
  
  <!-- Software Wallet -->
  <g transform="translate(300, 100)">
    <rect width="120" height="80" rx="12" fill="#1E40AF" stroke="#3B82F6" stroke-width="2"/>
    <rect width="80" height="50" x="20" y="15" rx="8" fill="#1E3A8A"/>
    <circle cx="40" cy="35" r="4" fill="#60A5FA"/>
    <circle cx="60" cy="35" r="4" fill="#60A5FA"/>
    <circle cx="80" cy="35" r="4" fill="#60A5FA"/>
    <rect width="60" height="8" x="30" y="50" rx="2" fill="#3B82F6"/>
    <text x="60" y="110" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Software Wallet</text>
    <text x="60" y="125" text-anchor="middle" fill="#F59E0B" font-family="Arial, sans-serif" font-size="10">Moderate Security</text>
  </g>
  
  <!-- Web Wallet -->
  <g transform="translate(500, 100)">
    <rect width="120" height="80" rx="12" fill="#7C2D12" stroke="#EA580C" stroke-width="2"/>
    <rect width="80" height="50" x="20" y="15" rx="4" fill="#9A3412"/>
    <circle cx="35" cy="30" r="3" fill="#FB923C"/>
    <rect width="35" height="4" x="45" y="28" rx="2" fill="#FB923C"/>
    <rect width="50" height="4" x="30" y="40" rx="2" fill="#FB923C"/>
    <rect width="40" height="4" x="30" y="50" rx="2" fill="#FB923C"/>
    <text x="60" y="110" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Web Wallet</text>
    <text x="60" y="125" text-anchor="middle" fill="#EF4444" font-family="Arial, sans-serif" font-size="10">Lower Security</text>
  </g>
  
  <!-- Security Shield -->
  <g transform="translate(650, 90)">
    <path d="M 50 10 L 70 20 L 70 50 Q 70 70 50 80 Q 30 70 30 50 L 30 20 Z" fill="url(#securityGradient)" stroke="#10B981" stroke-width="2"/>
    <path d="M 45 35 L 50 40 L 60 30" stroke="white" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <text x="50" y="110" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Security First</text>
  </g>
  
  <!-- Security Tips -->
  <g transform="translate(50, 220)">
    <rect width="700" height="120" rx="12" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="350" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Essential Security Practices</text>
    
    <!-- Tip 1 -->
    <g transform="translate(20, 40)">
      <circle cx="10" cy="10" r="8" fill="#10B981"/>
      <text x="10" y="15" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">1</text>
      <text x="30" y="15" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Backup Your Seed Phrase</text>
      <text x="30" y="30" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">Write down your 12-24 word recovery phrase and store it safely offline</text>
    </g>
    
    <!-- Tip 2 -->
    <g transform="translate(350, 40)">
      <circle cx="10" cy="10" r="8" fill="#3B82F6"/>
      <text x="10" y="15" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">2</text>
      <text x="30" y="15" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Use Strong Passwords</text>
      <text x="30" y="30" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">Create unique, complex passwords and enable 2FA where possible</text>
    </g>
    
    <!-- Tip 3 -->
    <g transform="translate(20, 70)">
      <circle cx="10" cy="10" r="8" fill="#8B5CF6"/>
      <text x="10" y="15" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">3</text>
      <text x="30" y="15" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Verify Addresses</text>
      <text x="30" y="30" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">Always double-check recipient addresses before sending transactions</text>
    </g>
    
    <!-- Tip 4 -->
    <g transform="translate(350, 70)">
      <circle cx="10" cy="10" r="8" fill="#F59E0B"/>
      <text x="10" y="15" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">4</text>
      <text x="30" y="15" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Beware of Phishing</text>
      <text x="30" y="30" fill="#94A3B8" font-family="Arial, sans-serif" font-size="10">Only use official websites and never share your private keys</text>
    </g>
  </g>
  
  <!-- Warning Box -->
  <g transform="translate(200, 360)">
    <rect width="400" height="30" rx="6" fill="#7F1D1D" stroke="#EF4444" stroke-width="1"/>
    <text x="20" y="20" fill="#FEE2E2" font-family="Arial, sans-serif" font-size="12" font-weight="bold">⚠️ Never share your private keys or seed phrase with anyone!</text>
  </g>
</svg>
