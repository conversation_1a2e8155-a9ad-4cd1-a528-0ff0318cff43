
import React, { useMemo, useState } from "react";
import { <PERSON>, <PERSON>H<PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from "recharts";
import { useDefiData } from "@/hooks/useDefiData";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/hooks/use-toast";

export default function StrategyBuilder({ isLoading }: { isLoading: boolean }) {
  const { strategies } = useDefiData();
  const [selectedRiskLevel, setSelectedRiskLevel] = useState<"Low" | "Medium" | "High">("Medium");
  
  const filteredStrategies = useMemo(() => {
    return (strategies || []).filter(strategy => strategy.riskLevel === selectedRiskLevel);
  }, [strategies, selectedRiskLevel]);
  
  const [selectedStrategy, setSelectedStrategy] = useState<string | null>(null);
  
  const currentStrategy = useMemo(() => {
    if (!selectedStrategy && filteredStrategies.length > 0) {
      return filteredStrategies[0];
    }
    return filteredStrategies.find(s => s.id === selectedStrategy) || filteredStrategies[0];
  }, [filteredStrategies, selectedStrategy]);
  
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658', '#8dd1e1'];
  
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <Card className="col-span-1">
        <CardHeader>
          <CardTitle>Investment Profile</CardTitle>
          <CardDescription>Define your risk tolerance and investment goals</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-28 w-full" />
            </div>
          ) : (
            <>
              <div className="space-y-2">
                <Label htmlFor="risk-level">Risk Tolerance</Label>
                <RadioGroup
                  id="risk-level"
                  value={selectedRiskLevel}
                  onValueChange={(value) => setSelectedRiskLevel(value as "Low" | "Medium" | "High")}
                  className="flex flex-col space-y-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="Low" id="low" />
                    <Label htmlFor="low" className="font-normal">Conservative (Low Risk)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="Medium" id="medium" />
                    <Label htmlFor="medium" className="font-normal">Balanced (Medium Risk)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="High" id="high" />
                    <Label htmlFor="high" className="font-normal">Aggressive (High Risk)</Label>
                  </div>
                </RadioGroup>
              </div>
              
              <div className="space-y-2">
                <Label>Time Horizon</Label>
                <div className="px-2">
                  <Slider defaultValue={[6]} max={24} step={1} />
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>1 Month</span>
                  <span>6 Months</span>
                  <span>1 Year</span>
                  <span>2 Years</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Capital Allocation</Label>
                <div className="px-2">
                  <Slider defaultValue={[5000]} max={100000} step={1000} />
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>$1,000</span>
                  <span>$50,000</span>
                  <span>$100,000</span>
                </div>
              </div>
            </>
          )}
        </CardContent>
        <CardFooter>
          <Button 
            className="w-full"
            onClick={() => {
              toast({
                title: "Building optimal strategy",
                description: "Your strategy has been updated based on your preferences.",
              });
            }}
          >
            Generate Strategy
          </Button>
        </CardFooter>
      </Card>
      
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>Recommended Strategy</CardTitle>
          <CardDescription>Optimal asset allocation based on your risk profile</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {isLoading || !currentStrategy ? (
            <div className="space-y-4">
              <Skeleton className="h-6 w-1/3" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-[250px] w-full" />
            </div>
          ) : (
            <>
              <div>
                <h4 className="text-lg font-semibold">{currentStrategy.name}</h4>
                <p className="text-muted-foreground">{currentStrategy.description}</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={currentStrategy.allocation}
                        dataKey="percentage"
                        nameKey="name"
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        label={(entry) => `${entry.name}: ${entry.percentage}%`}
                      >
                        {currentStrategy.allocation.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip 
                        formatter={(value: number) => `${value}%`}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                
                <div className="flex flex-col justify-center">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-muted-foreground">Expected APY</p>
                      <p className="text-2xl font-bold text-green-600 dark:text-green-500">
                        {currentStrategy.expectedApy.toFixed(1)}%
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Time Frame</p>
                      <p className="text-2xl font-bold">{currentStrategy.timeframe}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Risk Level</p>
                      <p className="text-2xl font-bold">
                        <RiskBadge riskLevel={currentStrategy.riskLevel} />
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Protocols</p>
                      <p className="text-2xl font-bold">{currentStrategy.protocolCount}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="pt-4">
                <h4 className="font-medium mb-2">Strategy Alternatives</h4>
                <div className="flex flex-wrap gap-2">
                  {filteredStrategies.map(strategy => (
                    <Button
                      key={strategy.id}
                      variant={currentStrategy.id === strategy.id ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedStrategy(strategy.id)}
                    >
                      {strategy.name}
                    </Button>
                  ))}
                </div>
              </div>
            </>
          )}
        </CardContent>
        <CardFooter>
          {!isLoading && (
            <Button 
              className="w-full"
              variant="outline"
              onClick={() => {
                toast({
                  title: "Strategy details exported",
                  description: "Detailed strategy information has been copied to your clipboard.",
                });
              }}
            >
              Export Strategy Details
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}

function RiskBadge({ riskLevel }: { riskLevel: "Low" | "Medium" | "High" }) {
  const color = riskLevel === "Low" 
    ? "bg-green-100 text-green-800" 
    : riskLevel === "Medium"
      ? "bg-yellow-100 text-yellow-800"
      : "bg-red-100 text-red-800";
      
  return (
    <span className={`inline-block px-2 py-1 text-sm font-medium rounded-full ${color}`}>
      {riskLevel}
    </span>
  );
}
