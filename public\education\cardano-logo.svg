<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">
  <defs>
    <linearGradient id="adaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0033AD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E3A8A;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="32" cy="32" r="32" fill="url(#adaGradient)"/>
  
  <!-- Cardano symbol - stylized circles -->
  <g transform="translate(32, 32)">
    <!-- Central circle -->
    <circle cx="0" cy="0" r="6" fill="white"/>
    
    <!-- Surrounding circles -->
    <circle cx="0" cy="-12" r="3" fill="white" opacity="0.8"/>
    <circle cx="10" cy="-6" r="3" fill="white" opacity="0.8"/>
    <circle cx="10" cy="6" r="3" fill="white" opacity="0.8"/>
    <circle cx="0" cy="12" r="3" fill="white" opacity="0.8"/>
    <circle cx="-10" cy="6" r="3" fill="white" opacity="0.8"/>
    <circle cx="-10" cy="-6" r="3" fill="white" opacity="0.8"/>
    
    <!-- Outer ring circles -->
    <circle cx="0" cy="-18" r="2" fill="white" opacity="0.6"/>
    <circle cx="15" cy="-9" r="2" fill="white" opacity="0.6"/>
    <circle cx="15" cy="9" r="2" fill="white" opacity="0.6"/>
    <circle cx="0" cy="18" r="2" fill="white" opacity="0.6"/>
    <circle cx="-15" cy="9" r="2" fill="white" opacity="0.6"/>
    <circle cx="-15" cy="-9" r="2" fill="white" opacity="0.6"/>
  </g>
  
  <!-- ADA text -->
  <text x="32" y="52" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">ADA</text>
</svg>
