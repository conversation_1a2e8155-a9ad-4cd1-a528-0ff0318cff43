
import { cn } from "@/lib/utils";
import { ArrowDownRight, ArrowUpRight } from "lucide-react";
import { ReactNode } from "react";

interface StatCardProps {
  title: string;
  value: string;
  change: number;
  icon?: ReactNode;
  className?: string;
  animationDelay?: string;
}

export default function StatCard({ 
  title, 
  value, 
  change, 
  icon,
  className,
  animationDelay = "0ms"
}: StatCardProps) {
  const isPositive = change >= 0;
  const ChangeIcon = isPositive ? ArrowUpRight : ArrowDownRight;
  const changeColor = isPositive ? "text-green-500" : "text-red-500";
  
  return (
    <div 
      className={cn(
        "relative overflow-hidden rounded-lg border border-border bg-card p-5 animate-scale-in",
        className
      )}
      style={{ animationDelay }}
    >
      <div className="flex justify-between items-start">
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
          <div className="text-2xl font-semibold">{value}</div>
          <div className={cn("flex items-center text-sm font-medium", changeColor)}>
            <ChangeIcon size={16} />
            <span>{Math.abs(change)}%</span>
            <span className="text-muted-foreground ml-1">vs yesterday</span>
          </div>
        </div>
        
        {icon && (
          <div className="h-10 w-10 rounded-md bg-card flex items-center justify-center border border-border">
            {icon}
          </div>
        )}
      </div>
    </div>
  );
}
