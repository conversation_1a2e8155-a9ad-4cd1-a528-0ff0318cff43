export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      admin_users: {
        Row: {
          admin_level: string
          created_at: string
          id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          admin_level?: string
          created_at?: string
          id?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          admin_level?: string
          created_at?: string
          id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      ai_prompt_templates: {
        Row: {
          category: string
          created_at: string | null
          id: string
          is_active: boolean
          template_content: string
          template_name: string
          updated_at: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          id?: string
          is_active?: boolean
          template_content: string
          template_name: string
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          id?: string
          is_active?: boolean
          template_content?: string
          template_name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      api_configurations: {
        Row: {
          cache_duration_minutes: number
          fallback_cache_enabled: boolean
          id: string
          is_enabled: boolean
          provider: string
          rate_limit_per_minute: number
          updated_at: string
          updated_by: string | null
        }
        Insert: {
          cache_duration_minutes?: number
          fallback_cache_enabled?: boolean
          id?: string
          is_enabled?: boolean
          provider: string
          rate_limit_per_minute?: number
          updated_at?: string
          updated_by?: string | null
        }
        Update: {
          cache_duration_minutes?: number
          fallback_cache_enabled?: boolean
          id?: string
          is_enabled?: boolean
          provider?: string
          rate_limit_per_minute?: number
          updated_at?: string
          updated_by?: string | null
        }
        Relationships: []
      }
      api_costs: {
        Row: {
          actual_cost: number | null
          created_at: string | null
          date: string
          estimated_cost: number | null
          id: string
          provider_id: string | null
          requests_count: number | null
          tokens_used: number | null
        }
        Insert: {
          actual_cost?: number | null
          created_at?: string | null
          date: string
          estimated_cost?: number | null
          id?: string
          provider_id?: string | null
          requests_count?: number | null
          tokens_used?: number | null
        }
        Update: {
          actual_cost?: number | null
          created_at?: string | null
          date?: string
          estimated_cost?: number | null
          id?: string
          provider_id?: string | null
          requests_count?: number | null
          tokens_used?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "api_costs_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "api_providers"
            referencedColumns: ["id"]
          },
        ]
      }
      api_providers: {
        Row: {
          config: Json | null
          cost_per_request: number | null
          cost_per_token: number | null
          created_at: string | null
          id: string
          is_active: boolean | null
          monthly_quota: number | null
          name: string
          priority: number
          rate_limit_per_minute: number | null
          type: string
          updated_at: string | null
        }
        Insert: {
          config?: Json | null
          cost_per_request?: number | null
          cost_per_token?: number | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          monthly_quota?: number | null
          name: string
          priority: number
          rate_limit_per_minute?: number | null
          type: string
          updated_at?: string | null
        }
        Update: {
          config?: Json | null
          cost_per_request?: number | null
          cost_per_token?: number | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          monthly_quota?: number | null
          name?: string
          priority?: number
          rate_limit_per_minute?: number | null
          type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      api_usage_logs: {
        Row: {
          created_at: string
          endpoint: string
          error_message: string | null
          id: string
          provider: string
          request_count: number
          response_time_ms: number | null
          status_code: number | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          endpoint: string
          error_message?: string | null
          id?: string
          provider: string
          request_count?: number
          response_time_ms?: number | null
          status_code?: number | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          endpoint?: string
          error_message?: string | null
          id?: string
          provider?: string
          request_count?: number
          response_time_ms?: number | null
          status_code?: number | null
          user_id?: string | null
        }
        Relationships: []
      }
      app_error_logs: {
        Row: {
          created_at: string
          error_message: string
          error_type: string
          id: string
          page_url: string | null
          stack_trace: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          error_message: string
          error_type: string
          id?: string
          page_url?: string | null
          stack_trace?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          error_message?: string
          error_type?: string
          id?: string
          page_url?: string | null
          stack_trace?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      cache_entries: {
        Row: {
          cache_key: string
          created_at: string | null
          data: Json
          data_type: string
          expires_at: string
          hit_count: number | null
          id: string
          last_accessed: string | null
          provider: string
          ttl_seconds: number
        }
        Insert: {
          cache_key: string
          created_at?: string | null
          data: Json
          data_type: string
          expires_at: string
          hit_count?: number | null
          id?: string
          last_accessed?: string | null
          provider: string
          ttl_seconds: number
        }
        Update: {
          cache_key?: string
          created_at?: string | null
          data?: Json
          data_type?: string
          expires_at?: string
          hit_count?: number | null
          id?: string
          last_accessed?: string | null
          provider?: string
          ttl_seconds?: number
        }
        Relationships: []
      }
      cache_status: {
        Row: {
          cache_key: string
          data_size_bytes: number | null
          expires_at: string
          hit_count: number
          id: string
          last_updated: string
          miss_count: number
          provider: string
        }
        Insert: {
          cache_key: string
          data_size_bytes?: number | null
          expires_at: string
          hit_count?: number
          id?: string
          last_updated?: string
          miss_count?: number
          provider: string
        }
        Update: {
          cache_key?: string
          data_size_bytes?: number | null
          expires_at?: string
          hit_count?: number
          id?: string
          last_updated?: string
          miss_count?: number
          provider?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string | null
          full_name: string | null
          id: string
          notification_preferences: Json | null
          theme: string | null
          updated_at: string
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id: string
          notification_preferences?: Json | null
          theme?: string | null
          updated_at?: string
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id?: string
          notification_preferences?: Json | null
          theme?: string | null
          updated_at?: string
          username?: string | null
        }
        Relationships: []
      }
      user_activity_logs: {
        Row: {
          action: string
          created_at: string
          id: string
          page: string
          session_duration_minutes: number | null
          user_id: string
        }
        Insert: {
          action: string
          created_at?: string
          id?: string
          page: string
          session_duration_minutes?: number | null
          user_id: string
        }
        Update: {
          action?: string
          created_at?: string
          id?: string
          page?: string
          session_duration_minutes?: number | null
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      cleanup_expired_cache: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      is_admin: {
        Args: { user_uuid: string }
        Returns: boolean
      }
      log_api_usage: {
        Args: {
          p_provider: string
          p_endpoint: string
          p_user_id: string
          p_response_time_ms?: number
          p_status_code?: number
          p_error_message?: string
        }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
