/**
 * API Provider Diagnostic Utility
 * Comprehensive testing and debugging for API provider save functionality
 */

import { supabase } from '@/integrations/supabase/client';
import { 
  createApiProvider, 
  getAllApiProviders, 
  validateApiProviderConfig,
  CreateApiProviderRequest 
} from '@/services/api/providers/apiProviderService';

export class ApiProviderDiagnostic {
  /**
   * Test database connectivity and table existence
   */
  static async testDatabaseSetup(): Promise<{
    success: boolean;
    tablesExist: boolean;
    error?: string;
    details: any;
  }> {
    console.log('🔍 Testing database setup for API providers...');
    
    try {
      // Test 1: Check if api_providers table exists
      const { data, error } = await supabase
        .from('api_providers')
        .select('count')
        .limit(1);

      if (error) {
        if (error.code === '42P01' || error.message.includes('does not exist')) {
          console.log('❌ api_providers table does not exist');
          return {
            success: false,
            tablesExist: false,
            error: 'Database tables not found',
            details: {
              errorCode: error.code,
              errorMessage: error.message,
              needsMigration: true
            }
          };
        } else {
          console.log('❌ Database connection error:', error);
          return {
            success: false,
            tablesExist: false,
            error: 'Database connection failed',
            details: error
          };
        }
      }

      console.log('✅ Database tables exist and are accessible');
      return {
        success: true,
        tablesExist: true,
        details: {
          connectionWorking: true,
          tableExists: true
        }
      };

    } catch (error: any) {
      console.log('❌ Database test failed:', error);
      return {
        success: false,
        tablesExist: false,
        error: error.message,
        details: error
      };
    }
  }

  /**
   * Test API provider creation with sample data
   */
  static async testCreateProvider(): Promise<{
    success: boolean;
    error?: string;
    provider?: any;
    validationErrors?: string[];
  }> {
    console.log('🧪 Testing API provider creation...');

    const testProvider: CreateApiProviderRequest = {
      name: `Test Provider ${Date.now()}`,
      type: 'market',
      priority: 10,
      rate_limit_per_minute: 60,
      monthly_quota: 5000,
      cost_per_request: 0.002,
      is_active: true,
      config: {
        baseUrl: 'https://api.test.com',
        apiKey: 'test-key-123',
        timeout: 15000,
      },
    };

    try {
      // Test validation first
      const validationErrors = validateApiProviderConfig(testProvider);
      if (validationErrors.length > 0) {
        console.log('❌ Validation failed:', validationErrors);
        return {
          success: false,
          error: 'Validation failed',
          validationErrors
        };
      }

      // Test creation
      const createdProvider = await createApiProvider(testProvider);
      console.log('✅ Provider created successfully:', createdProvider);

      return {
        success: true,
        provider: createdProvider
      };

    } catch (error: any) {
      console.log('❌ Provider creation failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Test form data transformation
   */
  static async testFormDataTransformation(): Promise<{
    success: boolean;
    error?: string;
    transformedData?: any;
  }> {
    console.log('🔄 Testing form data transformation...');

    // Simulate form data as it comes from the form
    const formData = {
      name: 'Test Form Provider',
      type: 'market',
      priority: 5,
      rate_limit_per_minute: 30,
      monthly_quota: 10000,
      cost_per_request: 0.001,
      cost_per_token: undefined,
      is_active: true,
      apiKey: 'test-api-key',
      baseUrl: 'https://api.example.com',
      model: '',
      timeout: 15000,
      config: '{"customOption": true}'
    };

    try {
      // Parse existing config
      let existingConfig = {};
      try {
        existingConfig = JSON.parse(formData.config);
      } catch (error) {
        console.warn('Invalid JSON in config field, using empty object');
      }

      // Merge individual fields into config
      const mergedConfig = {
        ...existingConfig,
        ...(formData.apiKey && { apiKey: formData.apiKey }),
        ...(formData.baseUrl && { baseUrl: formData.baseUrl }),
        ...(formData.model && { model: formData.model }),
        ...(formData.timeout && { timeout: formData.timeout }),
      };

      // Create submission data with merged config
      const transformedData = {
        name: formData.name,
        type: formData.type,
        priority: formData.priority,
        rate_limit_per_minute: formData.rate_limit_per_minute,
        monthly_quota: formData.monthly_quota,
        cost_per_request: formData.cost_per_request,
        cost_per_token: formData.cost_per_token,
        is_active: formData.is_active,
        config: mergedConfig,
      };

      console.log('✅ Form data transformation successful:', transformedData);

      return {
        success: true,
        transformedData
      };

    } catch (error: any) {
      console.log('❌ Form data transformation failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Test the complete save flow
   */
  static async testCompleteSaveFlow(): Promise<{
    success: boolean;
    error?: string;
    steps: Record<string, boolean>;
    details: any;
  }> {
    console.log('🚀 Testing complete API provider save flow...');

    const steps = {
      databaseSetup: false,
      formTransformation: false,
      validation: false,
      creation: false
    };

    const details: any = {};

    try {
      // Step 1: Database setup
      const dbTest = await this.testDatabaseSetup();
      steps.databaseSetup = dbTest.success;
      details.database = dbTest;

      if (!dbTest.success) {
        return {
          success: false,
          error: 'Database setup failed',
          steps,
          details
        };
      }

      // Step 2: Form transformation
      const transformTest = await this.testFormDataTransformation();
      steps.formTransformation = transformTest.success;
      details.transformation = transformTest;

      if (!transformTest.success) {
        return {
          success: false,
          error: 'Form transformation failed',
          steps,
          details
        };
      }

      // Step 3: Validation
      const validationErrors = validateApiProviderConfig(transformTest.transformedData!);
      steps.validation = validationErrors.length === 0;
      details.validation = { errors: validationErrors };

      if (validationErrors.length > 0) {
        return {
          success: false,
          error: 'Validation failed',
          steps,
          details
        };
      }

      // Step 4: Creation
      const createTest = await this.testCreateProvider();
      steps.creation = createTest.success;
      details.creation = createTest;

      const allStepsSuccessful = Object.values(steps).every(step => step);

      return {
        success: allStepsSuccessful,
        error: allStepsSuccessful ? undefined : 'Some steps failed',
        steps,
        details
      };

    } catch (error: any) {
      console.log('❌ Complete save flow test failed:', error);
      return {
        success: false,
        error: error.message,
        steps,
        details
      };
    }
  }

  /**
   * Get migration script for missing tables
   */
  static getMigrationScript(): string {
    return `-- API Provider Management Tables Migration
-- Run this in your Supabase SQL Editor

-- Create API providers table
CREATE TABLE IF NOT EXISTS api_providers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL UNIQUE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('market', 'onchain', 'defi', 'ai')),
  priority INTEGER NOT NULL CHECK (priority >= 1 AND priority <= 100),
  rate_limit_per_minute INTEGER CHECK (rate_limit_per_minute >= 0),
  monthly_quota INTEGER CHECK (monthly_quota >= 0),
  cost_per_request DECIMAL(10,6) CHECK (cost_per_request >= 0),
  cost_per_token DECIMAL(10,8) CHECK (cost_per_token >= 0),
  is_active BOOLEAN DEFAULT true,
  config JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE api_providers ENABLE ROW LEVEL SECURITY;

-- Create policy for admin access
CREATE POLICY "Admin can manage api_providers" ON api_providers
    FOR ALL USING (true);

-- Insert default providers
INSERT INTO api_providers (name, type, priority, rate_limit_per_minute, monthly_quota, cost_per_request, is_active, config) VALUES
('CoinGecko', 'market', 1, 30, 10000, 0.001, true, '{"baseUrl": "https://api.coingecko.com/api/v3", "timeout": 15000}'),
('DeepSeek', 'ai', 1, 100, 1000000, null, true, '{"baseUrl": "https://api.deepseek.com", "timeout": 60000}')
ON CONFLICT (name) DO NOTHING;

-- Update AI providers with token costs
UPDATE api_providers SET cost_per_token = 0.00000014 WHERE name = 'DeepSeek';

SELECT 'API Providers table created successfully' AS status;`;
  }
}

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).apiProviderDiagnostic = ApiProviderDiagnostic;
  
  // Add convenient shortcuts
  (window as any).testApiProviderSave = () => ApiProviderDiagnostic.testCompleteSaveFlow();
  (window as any).testDatabaseSetup = () => ApiProviderDiagnostic.testDatabaseSetup();
  (window as any).getMigrationScript = () => {
    console.log(ApiProviderDiagnostic.getMigrationScript());
    return ApiProviderDiagnostic.getMigrationScript();
  };
}
