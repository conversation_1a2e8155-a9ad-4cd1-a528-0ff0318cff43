// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://fwbnxtxtmpfvsheawznq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ3Ym54dHh0bXBmdnNoZWF3em5xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0NDcxNzIsImV4cCI6MjA2MjAyMzE3Mn0.l70Pel6kZugWJEhO0h3H7OEPk5kDDge1qm4BnFpZFAI";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);