
import { supabase } from '@/integrations/supabase/client';
import { Profile } from './types';
import { useToast } from '@/hooks/use-toast';

export const getNotificationPreference = (preferences: any, key: string): boolean => {
  if (typeof preferences === 'object' && preferences !== null && !Array.isArray(preferences)) {
    return Boolean(preferences[key]);
  }
  return false;
};

export const fetchUserProfile = async (userId: string) => {
  const { toast } = useToast();
  
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      throw error;
    }

    if (data) {
      // Convert the data to the Profile type with proper notification_preferences handling
      const profileData: Profile = {
        id: data.id,
        username: data.username,
        full_name: data.full_name,
        avatar_url: data.avatar_url,
        theme: data.theme || 'light',
        notification_preferences: {
          email: getNotificationPreference(data.notification_preferences, 'email'),
          push: getNotificationPreference(data.notification_preferences, 'push')
        }
      };
      return profileData;
    }
    return null;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    toast({
      title: 'Error',
      description: 'Failed to load user profile.',
      variant: 'destructive',
    });
    return null;
  }
};

export const updateUserProfile = async (userId: string, updates: Partial<Profile>) => {
  const { error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId);
  
  if (error) throw error;
  
  return true;
};
