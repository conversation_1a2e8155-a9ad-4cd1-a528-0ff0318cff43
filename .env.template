# Environment Configuration Template
# Copy this file to .env and fill in your actual API keys

# Supabase Configuration (Required)
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# CoinGecko API (Primary market data - Required)
# Get your API key from: https://www.coingecko.com/en/api/pricing
# Free tier: 30 req/min, 10k/month
VITE_COINGECKO_API_KEY=CG-your_api_key_here

# Alternative Market Data APIs (Optional fallbacks)
VITE_COINMARKETCAP_API_KEY=your_cmc_api_key
VITE_CRYPTOCOMPARE_API_KEY=your_cryptocompare_api_key

# On-chain Data APIs (Optional)
VITE_GECKOTERMINAL_API_KEY=your_geckoterminal_key
VITE_DEXSCREENER_API_KEY=your_dexscreener_key

# DeFi Data APIs (Optional)
VITE_DEFILLAMA_API_KEY=your_defillama_key
VITE_DUNE_API_KEY=your_dune_api_key

# AI APIs (Optional but recommended)
# DeepSeek AI (Primary AI - Cost effective: $0.14/1M tokens)
# Get your API key from: https://platform.deepseek.com/api_keys
VITE_DEEPSEEK_API_KEY=sk-your_deepseek_api_key
VITE_DEEPSEEK_API_URL=https://api.deepseek.com

# OpenAI (Fallback AI)
VITE_OPENAI_API_KEY=sk-your_openai_api_key
VITE_OPENAI_API_URL=https://api.openai.com/v1

# Claude (Alternative AI)
VITE_CLAUDE_API_KEY=sk-your_claude_api_key
VITE_CLAUDE_API_URL=https://api.anthropic.com

# Application Configuration
VITE_APP_NAME=CryptoVision Pro
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=development

# Feature Flags
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_ADVANCED_ANALYTICS=true
VITE_ENABLE_REAL_TIME_UPDATES=true

# Rate Limiting Configuration
VITE_DEFAULT_RATE_LIMIT=30
VITE_CACHE_DURATION=300000

# Security Configuration
VITE_ENABLE_API_KEY_VALIDATION=true
VITE_LOG_API_ERRORS=true
