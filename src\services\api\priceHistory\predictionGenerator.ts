
/**
 * Prediction Generator
 * Generates price predictions using trend analysis and moving averages
 */

import { calculateTrend, calculateVolatility, calculateMovingAverage, generateConfidenceIntervals } from './trendAnalysis';
import type { PriceDataPoint, PriceForecast } from './types';

/**
 * Generate price predictions based on historical data
 * @param historicalData - Historical price data points
 * @param daysToForecast - Number of days to predict
 * @returns Array of price predictions with confidence intervals
 */
export function generatePricePredictions(
  historicalData: PriceDataPoint[],
  daysToForecast: number = 7
): PriceForecast['predictions'] {
  if (!historicalData || historicalData.length < 10) {
    throw new Error('Insufficient historical data for forecasting (minimum 10 data points required)');
  }

  const prices = historicalData.map(d => d.price);
  const recentPrices = prices.slice(-7); // Last 7 days for moving average
  const movingAverage = calculateMovingAverage(prices, 7);
  const trend = calculateTrend(prices.slice(-14)); // Last 14 days for trend
  const volatility = calculateVolatility(recentPrices);
  
  const predictions = [];
  const lastDate = new Date(historicalData[historicalData.length - 1].date);
  
  for (let i = 1; i <= daysToForecast; i++) {
    const forecastDate = new Date(lastDate);
    forecastDate.setDate(lastDate.getDate() + i);
    
    // Simple prediction model: moving average + trend * days
    const basePrice = movingAverage + (trend * i);
    
    // Generate confidence intervals
    const { upperBound, lowerBound, confidence } = generateConfidenceIntervals(
      basePrice,
      volatility,
      i,
      daysToForecast
    );
    
    predictions.push({
      date: forecastDate.toISOString().split('T')[0],
      predictedPrice: basePrice,
      confidence,
      upperBound,
      lowerBound
    });
  }

  return predictions;
}

/**
 * Calculate forecast accuracy metrics
 * @param predictions - Array of predictions
 * @param actualPrices - Array of actual prices
 * @returns Accuracy score (0-1)
 */
export function calculateForecastAccuracy(
  predictions: Array<{ predictedPrice: number }>,
  actualPrices: number[]
): number {
  if (predictions.length !== actualPrices.length || predictions.length === 0) {
    return 0;
  }

  const errors = predictions.map((pred, i) => 
    Math.abs(pred.predictedPrice - actualPrices[i]) / actualPrices[i]
  );

  const meanError = errors.reduce((sum, error) => sum + error, 0) / errors.length;
  
  return Math.max(0, 1 - meanError); // Convert to accuracy (1 - error rate)
}
