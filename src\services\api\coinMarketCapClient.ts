/**
 * CoinMarketCap API Client
 * Handles API requests to CoinMarketCap Pro API
 */

import axios from 'axios';

// Create axios instance for CoinMarketCap API
export const coinMarketCapAxios = axios.create({
  baseURL: 'https://pro-api.coinmarketcap.com/v1',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...(import.meta.env.VITE_COINMARKETCAP_API_KEY && {
      'X-CMC_PRO_API_KEY': import.meta.env.VITE_COINMARKETCAP_API_KEY
    })
  }
});

// Add request interceptor for logging
coinMarketCapAxios.interceptors.request.use(
  (config) => {
    console.log('🚀 CoinMarketCap API Request:', {
      url: config.url,
      method: config.method,
      params: config.params,
      hasApiKey: !!config.headers['X-CMC_PRO_API_KEY']
    });
    return config;
  },
  (error) => {
    console.error('❌ CoinMarketCap Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging
coinMarketCapAxios.interceptors.response.use(
  (response) => {
    console.log('✅ CoinMarketCap API Response:', {
      status: response.status,
      url: response.config.url,
      dataSize: JSON.stringify(response.data).length,
      rateLimit: {
        remaining: response.headers['x-ratelimit-credit-remaining'],
        reset: response.headers['x-ratelimit-credit-reset']
      }
    });
    return response;
  },
  (error) => {
    console.error('❌ CoinMarketCap Response Error:', {
      status: error.response?.status,
      url: error.config?.url,
      message: error.response?.data?.status?.error_message || error.message,
      rateLimit: {
        remaining: error.response?.headers['x-ratelimit-credit-remaining'],
        reset: error.response?.headers['x-ratelimit-credit-reset']
      }
    });
    return Promise.reject(error);
  }
);

/**
 * Test CoinMarketCap API connectivity
 * Note: This will fail in browser due to CORS restrictions
 */
export const testCoinMarketCapConnection = async (): Promise<{
  success: boolean;
  error?: string;
  rateLimit?: {
    remaining: number;
    reset: number;
  };
}> => {
  const apiKey = import.meta.env.VITE_COINMARKETCAP_API_KEY;

  // Check if API key is configured
  if (!apiKey) {
    return {
      success: false,
      error: 'API key not configured'
    };
  }

  // Validate API key format (CoinMarketCap keys are UUIDs)
  const isValidFormat = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(apiKey);

  if (!isValidFormat) {
    return {
      success: false,
      error: 'Invalid API key format (should be UUID format)'
    };
  }

  // Since we can't test the API directly due to CORS restrictions,
  // we'll return success if the key format is valid
  return {
    success: true,
    error: 'Configuration valid (CORS prevents direct testing)'
  };
};

/**
 * Get cryptocurrency listings from CoinMarketCap
 */
export const getCoinMarketCapListings = async (params: {
  start?: number;
  limit?: number;
  convert?: string;
} = {}) => {
  const {
    start = 1,
    limit = 100,
    convert = 'USD'
  } = params;

  try {
    const response = await coinMarketCapAxios.get('/cryptocurrency/listings/latest', {
      params: {
        start,
        limit,
        convert
      }
    });

    return {
      success: true,
      data: response.data.data,
      status: response.data.status,
      rateLimit: {
        remaining: parseInt(response.headers['x-ratelimit-credit-remaining'] || '0'),
        reset: parseInt(response.headers['x-ratelimit-credit-reset'] || '0')
      }
    };

  } catch (error: any) {
    console.error('❌ CoinMarketCap listings error:', error);

    return {
      success: false,
      error: error.response?.data?.status?.error_message || error.message,
      rateLimit: error.response?.headers ? {
        remaining: parseInt(error.response.headers['x-ratelimit-credit-remaining'] || '0'),
        reset: parseInt(error.response.headers['x-ratelimit-credit-reset'] || '0')
      } : undefined
    };
  }
};

/**
 * Get specific cryptocurrency quotes from CoinMarketCap
 */
export const getCoinMarketCapQuotes = async (params: {
  symbol?: string;
  id?: string;
  convert?: string;
}) => {
  const {
    symbol,
    id,
    convert = 'USD'
  } = params;

  if (!symbol && !id) {
    throw new Error('Either symbol or id must be provided');
  }

  try {
    const response = await coinMarketCapAxios.get('/cryptocurrency/quotes/latest', {
      params: {
        ...(symbol && { symbol }),
        ...(id && { id }),
        convert
      }
    });

    return {
      success: true,
      data: response.data.data,
      status: response.data.status,
      rateLimit: {
        remaining: parseInt(response.headers['x-ratelimit-credit-remaining'] || '0'),
        reset: parseInt(response.headers['x-ratelimit-credit-reset'] || '0')
      }
    };

  } catch (error: any) {
    console.error('❌ CoinMarketCap quotes error:', error);

    return {
      success: false,
      error: error.response?.data?.status?.error_message || error.message,
      rateLimit: error.response?.headers ? {
        remaining: parseInt(error.response.headers['x-ratelimit-credit-remaining'] || '0'),
        reset: parseInt(error.response.headers['x-ratelimit-credit-reset'] || '0')
      } : undefined
    };
  }
};

/**
 * Get CoinMarketCap API info (for testing and rate limit checking)
 */
export const getCoinMarketCapApiInfo = async () => {
  try {
    const response = await coinMarketCapAxios.get('/key/info');

    return {
      success: true,
      data: response.data.data,
      status: response.data.status,
      rateLimit: {
        remaining: parseInt(response.headers['x-ratelimit-credit-remaining'] || '0'),
        reset: parseInt(response.headers['x-ratelimit-credit-reset'] || '0')
      }
    };

  } catch (error: any) {
    console.error('❌ CoinMarketCap API info error:', error);

    return {
      success: false,
      error: error.response?.data?.status?.error_message || error.message,
      rateLimit: error.response?.headers ? {
        remaining: parseInt(error.response.headers['x-ratelimit-credit-remaining'] || '0'),
        reset: parseInt(error.response.headers['x-ratelimit-credit-reset'] || '0')
      } : undefined
    };
  }
};

// Export the axios instance for direct use if needed
export default coinMarketCapAxios;
