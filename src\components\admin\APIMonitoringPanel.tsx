
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { RefreshCw, AlertCircle, CheckCircle, Clock, Activity } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export function APIMonitoringPanel() {
  const [timeRange, setTimeRange] = useState('24h');
  const queryClient = useQueryClient();

  // API usage logs
  const { data: apiLogs, isLoading } = useQuery({
    queryKey: ['apiLogs', timeRange],
    queryFn: async () => {
      const hours = timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 1;
      const { data } = await supabase
        .from('api_usage_logs')
        .select('*')
        .gte('created_at', new Date(Date.now() - hours * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .limit(100);
      return data || [];
    },
    refetchInterval: 30000,
  });

  // API provider status
  const { data: providerStats } = useQuery({
    queryKey: ['providerStats', timeRange],
    queryFn: async () => {
      const hours = timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 1;
      const { data } = await supabase
        .from('api_usage_logs')
        .select('provider, status_code, response_time_ms, error_message')
        .gte('created_at', new Date(Date.now() - hours * 60 * 60 * 1000).toISOString());

      const stats = data?.reduce((acc: any, log) => {
        const provider = log.provider;
        if (!acc[provider]) {
          acc[provider] = {
            total: 0,
            success: 0,
            errors: 0,
            avgResponseTime: 0,
            responseTimeSum: 0,
            responseTimeCount: 0
          };
        }
        
        acc[provider].total++;
        if (log.status_code >= 200 && log.status_code < 300) {
          acc[provider].success++;
        } else {
          acc[provider].errors++;
        }
        
        if (log.response_time_ms) {
          acc[provider].responseTimeSum += log.response_time_ms;
          acc[provider].responseTimeCount++;
          acc[provider].avgResponseTime = acc[provider].responseTimeSum / acc[provider].responseTimeCount;
        }
        
        return acc;
      }, {});

      return Object.entries(stats || {}).map(([provider, data]: [string, any]) => ({
        provider,
        ...data,
        successRate: data.total > 0 ? (data.success / data.total) * 100 : 0
      }));
    },
    refetchInterval: 30000,
  });

  const refreshData = () => {
    queryClient.invalidateQueries({ queryKey: ['apiLogs'] });
    queryClient.invalidateQueries({ queryKey: ['providerStats'] });
  };

  const getStatusBadge = (statusCode: number | null) => {
    if (!statusCode) return <Badge variant="secondary">Unknown</Badge>;
    if (statusCode >= 200 && statusCode < 300) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Success</Badge>;
    }
    if (statusCode >= 400 && statusCode < 500) {
      return <Badge variant="destructive">Client Error</Badge>;
    }
    if (statusCode >= 500) {
      return <Badge variant="destructive">Server Error</Badge>;
    }
    return <Badge variant="secondary">Other</Badge>;
  };

  const getProviderHealthBadge = (successRate: number) => {
    if (successRate >= 95) {
      return <Badge variant="default" className="bg-green-100 text-green-800 flex items-center gap-1">
        <CheckCircle className="w-3 h-3" />
        Healthy
      </Badge>;
    }
    if (successRate >= 80) {
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 flex items-center gap-1">
        <Clock className="w-3 h-3" />
        Warning
      </Badge>;
    }
    return <Badge variant="destructive" className="flex items-center gap-1">
      <AlertCircle className="w-3 h-3" />
      Critical
    </Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Last Hour</SelectItem>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button onClick={refreshData} variant="outline" size="sm">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Provider Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            API Provider Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Provider</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Success Rate</TableHead>
                <TableHead>Total Requests</TableHead>
                <TableHead>Avg Response Time</TableHead>
                <TableHead>Errors</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {providerStats?.map((provider) => (
                <TableRow key={provider.provider}>
                  <TableCell className="font-medium">{provider.provider}</TableCell>
                  <TableCell>{getProviderHealthBadge(provider.successRate)}</TableCell>
                  <TableCell>{provider.successRate.toFixed(1)}%</TableCell>
                  <TableCell>{provider.total}</TableCell>
                  <TableCell>
                    {provider.avgResponseTime > 0 ? `${Math.round(provider.avgResponseTime)}ms` : 'N/A'}
                  </TableCell>
                  <TableCell>
                    {provider.errors > 0 ? (
                      <Badge variant="destructive">{provider.errors}</Badge>
                    ) : (
                      <Badge variant="secondary">0</Badge>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Recent API Requests */}
      <Card>
        <CardHeader>
          <CardTitle>Recent API Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>Provider</TableHead>
                <TableHead>Endpoint</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Response Time</TableHead>
                <TableHead>User</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {apiLogs?.map((log) => (
                <TableRow key={log.id}>
                  <TableCell className="text-xs">
                    {new Date(log.created_at).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{log.provider}</Badge>
                  </TableCell>
                  <TableCell className="text-xs max-w-xs truncate">
                    {log.endpoint}
                  </TableCell>
                  <TableCell>{getStatusBadge(log.status_code)}</TableCell>
                  <TableCell>
                    {log.response_time_ms ? `${log.response_time_ms}ms` : 'N/A'}
                  </TableCell>
                  <TableCell className="text-xs">
                    {log.user_id ? log.user_id.substring(0, 8) + '...' : 'System'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
