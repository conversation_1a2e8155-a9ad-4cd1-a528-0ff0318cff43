
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, ChevronRight } from "lucide-react";
import { NavigationSection } from "./NavigationSection";
import { navigationSections } from "./navigationData";
import { useAdminAuth } from "@/hooks/useAdminAuth";

export default function Navigation() {
  const location = useLocation();
  const { isAdmin } = useAdminAuth();
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    "Core Analytics": true,
    "Advanced Analytics": true,
    "Research Tools": false,
    "Advanced Features": false,
    "Learning & Support": false,
    "Administration": isAdmin
  });

  const toggleSection = (sectionTitle: string) => {
    setOpenSections(prev => ({
      ...prev,
      [sectionTitle]: !prev[sectionTitle]
    }));
  };

  const filteredSections = navigationSections.map(section => ({
    ...section,
    items: section.items.filter(item => 
      !item.adminOnly || (item.adminOnly && isAdmin)
    )
  })).filter(section => section.items.length > 0);

  return (
    <div className="flex h-screen w-64 flex-col border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-14 items-center border-b px-4">
        <Link to="/" className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <span className="text-sm font-bold">CA</span>
          </div>
          <span className="font-bold">Crypto Analytics</span>
        </Link>
      </div>
      
      <ScrollArea className="flex-1 px-3 py-4">
        <div className="space-y-2">
          {filteredSections.map((section) => (
            <div key={section.title}>
              <Collapsible
                open={openSections[section.title]}
                onOpenChange={() => toggleSection(section.title)}
              >
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-between p-2 h-8 text-xs font-medium text-muted-foreground hover:text-foreground"
                  >
                    <span className="flex items-center gap-2">
                      {section.title}
                      {section.title === "Administration" && isAdmin && (
                        <Badge variant="destructive" className="text-xs px-1 py-0">
                          ADMIN
                        </Badge>
                      )}
                    </span>
                    {openSections[section.title] ? (
                      <ChevronDown className="h-3 w-3" />
                    ) : (
                      <ChevronRight className="h-3 w-3" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-1 pt-1">
                  <NavigationSection
                    items={section.items}
                    currentPath={location.pathname}
                  />
                </CollapsibleContent>
              </Collapsible>
              <Separator className="my-2" />
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
