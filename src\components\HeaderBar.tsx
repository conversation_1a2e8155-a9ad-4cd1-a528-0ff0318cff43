
import { useState, useEffect, ReactNode } from "react";
import { Search, Bell, RefreshCw, User, Sun, Moon, LogOut, Settings } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { SearchCommand } from "@/components/SearchCommand";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/auth/useAuth";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useTheme } from "@/contexts/theme-provider";
import { SystemStatusIndicator } from "@/components/SystemStatusIndicator";

interface HeaderBarProps {
  title: string;
  description?: string;
  onRefresh?: () => void;
  isLoading?: boolean;
  actions?: ReactNode;
}

export default function HeaderBar({
  title,
  description,
  onRefresh,
  isLoading = false,
  actions
}: HeaderBarProps) {
  const [searchDialogOpen, setSearchDialogOpen] = useState(false);
  const { toast } = useToast();
  const { user, profile, signOut } = useAuth();
  const { theme, setTheme } = useTheme();

  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
      toast({
        title: "Refreshing data",
        description: "Fetching the latest information...",
      });
    }
  };

  const handleThemeToggle = (checked: boolean) => {
    console.log('Theme toggle clicked, checked:', checked);
    const newTheme = checked ? "dark" : "light";
    console.log('Setting theme to:', newTheme);
    setTheme(newTheme);
  };

  console.log('Current theme in HeaderBar:', theme);

  return (
    <header className="bg-card py-4 px-4 md:px-8 border-b border-border shadow-sm flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="h-8 w-8 rounded-md bg-primary flex items-center justify-center">
          <RefreshCw size={18} className="text-primary-foreground" />
        </div>
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-foreground">{title}</h1>
          {description && (
            <p className="text-sm text-muted-foreground mt-1">
              {description}
            </p>
          )}
        </div>
      </div>

      <div className="flex items-center gap-3">
        {actions}

        {/* System Status Indicator */}
        <SystemStatusIndicator />

        <div className="flex items-center gap-2">
          <Sun size={16} className="text-muted-foreground" />
          <Switch
            checked={theme === "dark"}
            onCheckedChange={handleThemeToggle}
          />
          <Moon size={16} className="text-muted-foreground" />
        </div>

        {onRefresh && (
          <Button
            variant="outline"
            size="icon"
            onClick={handleRefresh}
            className={cn(
              "h-9 w-9 rounded-full",
              isLoading && "opacity-70 pointer-events-none"
            )}
            disabled={isLoading}
          >
            <RefreshCw size={16} className={isLoading ? "animate-spin" : ""} />
          </Button>
        )}

        <Button variant="outline" size="icon" className="h-9 w-9 rounded-full relative">
          <Bell size={16} />
          <span className="absolute top-1 right-2 h-2 w-2 rounded-full bg-primary"></span>
        </Button>

        <div
          className="h-9 relative hidden sm:flex items-center"
          onClick={() => setSearchDialogOpen(true)}
        >
          <Button
            variant="outline"
            className="w-[220px] md:w-[280px] justify-start text-left font-normal pl-10 pr-4 py-2 h-full rounded-full text-sm bg-background border border-input hover:bg-background/80"
          >
            <span className="text-muted-foreground">Search coins and tokens...</span>
            <kbd className="pointer-events-none absolute right-4 top-[10px] hidden h-5 select-none items-center gap-1 rounded border border-input bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
              <span className="text-xs">⌘</span>K
            </kbd>
          </Button>
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        </div>

        {/* User Profile Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" className="h-9 w-9 rounded-full overflow-hidden">
              {profile?.avatar_url ? (
                <Avatar className="h-9 w-9">
                  <AvatarImage src={profile.avatar_url} alt={profile.full_name || ''} />
                  <AvatarFallback>
                    {profile.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
              ) : (
                <User size={16} />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <div className="flex items-center justify-start gap-2 p-2">
              <div className="flex flex-col space-y-1 leading-none">
                {profile?.full_name && (
                  <p className="font-medium">{profile.full_name}</p>
                )}
                {user?.email && (
                  <p className="w-[200px] truncate text-sm text-muted-foreground">
                    {user.email}
                  </p>
                )}
              </div>
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link to="/profile" className="flex w-full cursor-pointer items-center">
                <Settings className="mr-2 h-4 w-4" />
                <span>Profile Settings</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => signOut()} className="text-red-600 focus:text-red-600">
              <LogOut className="mr-2 h-4 w-4" />
              <span>Log out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <SearchCommand
        open={searchDialogOpen}
        onOpenChange={setSearchDialogOpen}
      />
    </header>
  );
}
