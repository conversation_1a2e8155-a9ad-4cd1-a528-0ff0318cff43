<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">
  <defs>
    <linearGradient id="btcGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F7931A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="32" cy="32" r="32" fill="url(#btcGradient)"/>
  
  <!-- Bitcoin B symbol -->
  <g transform="translate(32, 32)">
    <!-- Outer B shape -->
    <path d="M -8 -16 L -8 16 L 4 16 Q 12 16 12 8 Q 12 0 8 0 Q 12 0 12 -8 Q 12 -16 4 -16 Z" fill="white"/>
    <!-- Inner cuts -->
    <path d="M -4 -12 L -4 -4 L 4 -4 Q 8 -4 8 -8 Q 8 -12 4 -12 Z" fill="#F7931A"/>
    <path d="M -4 4 L -4 12 L 4 12 Q 8 12 8 8 Q 8 4 4 4 Z" fill="#F7931A"/>
    <!-- Vertical lines -->
    <line x1="-2" y1="-20" x2="-2" y2="-16" stroke="white" stroke-width="2"/>
    <line x1="2" y1="-20" x2="2" y2="-16" stroke="white" stroke-width="2"/>
    <line x1="-2" y1="16" x2="-2" y2="20" stroke="white" stroke-width="2"/>
    <line x1="2" y1="16" x2="2" y2="20" stroke="white" stroke-width="2"/>
  </g>
  
  <!-- BTC text -->
  <text x="32" y="52" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">BTC</text>
</svg>
