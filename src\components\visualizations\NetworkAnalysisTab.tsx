
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import NetworkGraph from "@/components/NetworkGraph";

interface NetworkAnalysisTabProps {
  networkData: {
    nodes: any[];
    links: any[];
  } | undefined;
  isLoadingNetwork: boolean;
}

export default function NetworkAnalysisTab({
  networkData,
  isLoadingNetwork
}: NetworkAnalysisTabProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Crypto Ecosystem Network Analysis</CardTitle>
        <CardDescription>
          Interactive visualization of relationships between DeFi protocols, tokens, and blockchain networks
        </CardDescription>
      </CardHeader>
      <CardContent>
        <NetworkGraph 
          title="DeFi Ecosystem Network"
          description="Visualizing connections between protocols, tokens, and chains"
          nodes={networkData?.nodes || []}
          links={networkData?.links || []}
          isLoading={isLoadingNetwork}
          height={600}
          layoutOptions={{
            chargeStrength: -120,
            linkDistance: 120
          }}
        />
      </CardContent>
    </Card>
  );
}
