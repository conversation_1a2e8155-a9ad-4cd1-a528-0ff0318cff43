
import { useState } from 'react';
import { 
  <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, 
  Legend, ResponsiveContainer, Area, ComposedChart, Bar, Label
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Calendar, TrendingDown, TrendingUp, LineChart as LineChartIcon } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface HistoricalMetricsChartProps {
  data: any;
  coinId: string;
  coinName: string;
  isLoading: boolean;
}

export function HistoricalMetricsChart({ data, coinId, coinName, isLoading }: HistoricalMetricsChartProps) {
  const [timeframe, setTimeframe] = useState<'7d' | '30d' | '90d' | '1y'>('30d');

  // Format date for X-axis
  const formatXAxis = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Format numbers for tooltips
  const formatValue = (value: number) => {
    if (value > 1000000000) {
      return `$${(value / 1000000000).toFixed(2)}B`;
    } else if (value > 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`;
    } else if (value > 1000) {
      return `$${(value / 1000).toFixed(2)}K`;
    }
    return `$${value.toFixed(2)}`;
  };

  // Format price for Y axis
  const formatYAxis = (value: number) => {
    if (value > 1000) {
      return `$${(value / 1000)}K`;
    }
    return `$${value}`;
  };

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border p-3 rounded-lg shadow-md">
          <p className="text-sm font-medium">{new Date(label).toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric',
            year: 'numeric' 
          })}</p>
          {payload.map((entry: any, index: number) => (
            <div key={`tooltip-${index}`} className="flex justify-between gap-4 text-sm">
              <span style={{ color: entry.color }}>{entry.name}:</span>
              <span className="font-medium">
                {entry.name === 'Volume' ? formatValue(entry.value) : 
                 entry.name === 'Price' ? `$${entry.value.toFixed(2)}` : entry.value}
              </span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  // Get price change and color
  const getPriceChange = () => {
    if (!data || data.length < 2) return { change: 0, isPositive: true };
    
    const firstPrice = data[0]?.price || 0;
    const lastPrice = data[data.length - 1]?.price || 0;
    const change = ((lastPrice - firstPrice) / firstPrice) * 100;
    
    return {
      change: change.toFixed(2),
      isPositive: change >= 0
    };
  };

  const { change, isPositive } = getPriceChange();

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Price History</CardTitle>
          <CardDescription>Loading historical data...</CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[350px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Price History</CardTitle>
          <CardDescription>No historical data available</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[350px]">
          <div className="text-center text-muted-foreground">
            <LineChartIcon className="mx-auto h-12 w-12 opacity-30 mb-2" />
            <p>Historical data not available for {coinName}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-0">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <LineChartIcon className="h-5 w-5" />
              Price History
            </CardTitle>
            <CardDescription>Historical price and volume data</CardDescription>
          </div>
          <div className={`flex items-center gap-1 ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
            {isPositive ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
            <span className="font-bold">{change}%</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <div className="flex space-x-1">
            <Button 
              size="sm" 
              variant={timeframe === '7d' ? "default" : "outline"} 
              onClick={() => setTimeframe('7d')}
            >
              7D
            </Button>
            <Button 
              size="sm" 
              variant={timeframe === '30d' ? "default" : "outline"} 
              onClick={() => setTimeframe('30d')}
            >
              30D
            </Button>
            <Button 
              size="sm" 
              variant={timeframe === '90d' ? "default" : "outline"} 
              onClick={() => setTimeframe('90d')}
            >
              90D
            </Button>
            <Button 
              size="sm" 
              variant={timeframe === '1y' ? "default" : "outline"} 
              onClick={() => setTimeframe('1y')}
            >
              1Y
            </Button>
          </div>
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>Last updated: {new Date().toLocaleDateString()}</span>
          </div>
        </div>

        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart
              data={data}
              margin={{
                top: 5,
                right: 5,
                left: 5,
                bottom: 20,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" opacity={0.15} />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={formatXAxis} 
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                yAxisId="price" 
                domain={['auto', 'auto']} 
                tickFormatter={formatYAxis}
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                yAxisId="volume" 
                orientation="right" 
                tickFormatter={(value) => `${(value / 1000000).toFixed(0)}M`}
                tick={{ fontSize: 10 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar 
                yAxisId="volume" 
                dataKey="volume" 
                name="Volume" 
                fill="#8884d8" 
                opacity={0.3}
                barSize={20}
              />
              <Line 
                yAxisId="price" 
                type="monotone" 
                dataKey="price" 
                name="Price" 
                stroke="#10b981" 
                dot={false} 
                strokeWidth={2} 
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
