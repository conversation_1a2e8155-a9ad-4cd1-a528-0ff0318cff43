
// Re-export all commonly used types from their respective modules
export type {
  PriceDataPoint,
  CandleData,
  ForecastDataResponse,
  ForecastMetrics,
  PriceHistoryResponse,
  PriceForecast,
  ForecastResponse
} from "../priceHistory/types";

// Common API response patterns
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  error?: string;
  timestamp?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

// Common market data types
export interface MarketDataPoint {
  timestamp: number;
  value: number;
  volume?: number;
}

export interface CoinBasicInfo {
  id: string;
  name: string;
  symbol: string;
  image?: string;
  current_price?: number;
  market_cap?: number;
  price_change_percentage_24h?: number;
}

// API provider types
export interface ApiProviderConfig {
  name: string;
  baseUrl: string;
  apiKey?: string;
  rateLimit?: number;
  timeout?: number;
}

// Error types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Re-export error handling utilities
export { ApiServiceError, handleApiError, withRetry } from '../utils/errorHandler';
