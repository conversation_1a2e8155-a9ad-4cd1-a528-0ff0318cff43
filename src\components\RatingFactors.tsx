
import { CategoryCard } from "@/components/rating/CategoryCard";
import type { FactorWeight } from "@/hooks/useRatingSystem";

interface RatingFactorsProps {
  factorWeights: FactorWeight[];
  updateWeight: (factorId: string, weight: number) => void;
}

export default function RatingFactors({ factorWeights, updateWeight }: RatingFactorsProps) {
  const groupedFactors = factorWeights.reduce((acc, factor) => {
    if (!acc[factor.category]) {
      acc[factor.category] = [];
    }
    acc[factor.category].push(factor);
    return acc;
  }, {} as Record<string, FactorWeight[]>);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 animate-scale-in">
      {Object.entries(groupedFactors).map(([category, factors]) => (
        <CategoryCard
          key={category}
          category={category}
          factors={factors}
          updateWeight={updateWeight}
        />
      ))}
    </div>
  );
}
