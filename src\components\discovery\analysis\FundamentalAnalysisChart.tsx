
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer, PieChart, Pie, Cell, Tooltip } from "recharts";
import { Shield, Users, Code, Target, TrendingUp } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { CoinData, FundamentalMetrics, AnalysisProps } from "./types";
import { useQuery } from "@tanstack/react-query";
import { fetchCoinData } from "@/services/api/coinMarketData";

interface FundamentalAnalysisChartProps extends AnalysisProps {
  fundamentalMetrics: FundamentalMetrics;
}

export function FundamentalAnalysisChart({ coinData, fundamentalMetrics, isLoading }: FundamentalAnalysisChartProps) {
  // Fetch detailed coin data for real fundamental analysis
  const { data: detailedCoinData, isLoading: detailedLoading } = useQuery({
    queryKey: ['detailed-coin', coinData.id],
    queryFn: () => fetchCoinData(coinData.id),
    enabled: !!coinData.id,
    staleTime: 15 * 60 * 1000,
  });

  if (isLoading || detailedLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse h-80 bg-gray-200 rounded-lg"></div>
        <div className="animate-pulse h-60 bg-gray-200 rounded-lg"></div>
      </div>
    );
  }

  // Calculate real fundamental scores from API data
  const calculateFundamentalScores = (coinData: any, detailedData: any) => {
    const scores = {
      technology: 50,
      adoption: 50,
      tokenomics: 50,
      community: 50,
      liquidity: 50,
      governance: 50
    };

    if (detailedData) {
      // Technology score based on developer activity
      if (detailedData.developer_data) {
        const devData = detailedData.developer_data;
        scores.technology = Math.min(100, 
          (devData.forks || 0) / 100 * 30 +
          (devData.stars || 0) / 1000 * 40 +
          (devData.commit_count_4_weeks || 0) / 10 * 30
        );
      }

      // Community score based on social metrics
      if (detailedData.community_data) {
        const communityData = detailedData.community_data;
        scores.community = Math.min(100,
          (communityData.twitter_followers || 0) / 10000 * 40 +
          (communityData.reddit_subscribers || 0) / 1000 * 30 +
          (communityData.telegram_channel_user_count || 0) / 1000 * 30
        );
      }

      // Tokenomics score based on supply metrics
      if (detailedData.market_data) {
        const marketData = detailedData.market_data;
        const circulatingSupply = marketData.circulating_supply || 0;
        const totalSupply = marketData.total_supply || circulatingSupply;
        const maxSupply = marketData.max_supply;
        
        let tokenomicsScore = 50;
        if (maxSupply) {
          const supplyRatio = circulatingSupply / maxSupply;
          tokenomicsScore = Math.min(100, 30 + supplyRatio * 70);
        }
        scores.tokenomics = tokenomicsScore;
      }
    }

    // Adoption score based on market metrics
    const marketCapRank = coinData.market_cap_rank || 999;
    scores.adoption = Math.max(20, 100 - (marketCapRank / 10));

    // Liquidity score based on volume to market cap ratio
    const liquidityRatio = (coinData.total_volume || 0) / (coinData.market_cap || 1);
    scores.liquidity = Math.min(100, liquidityRatio * 1000);

    // Governance score (simplified calculation)
    scores.governance = 50 + (scores.community + scores.technology) / 4;

    return scores;
  };

  const realScores = calculateFundamentalScores(coinData, detailedCoinData);

  const radarData = [
    { subject: 'Technology', A: realScores.technology, fullMark: 100 },
    { subject: 'Adoption', A: realScores.adoption, fullMark: 100 },
    { subject: 'Tokenomics', A: realScores.tokenomics, fullMark: 100 },
    { subject: 'Community', A: realScores.community, fullMark: 100 },
    { subject: 'Liquidity', A: realScores.liquidity, fullMark: 100 },
    { subject: 'Governance', A: realScores.governance, fullMark: 100 },
  ];

  // Calculate real market distribution from detailed data
  const calculateMarketDistribution = (detailedData: any) => {
    if (!detailedData?.market_data) {
      return [
        { name: 'Circulating Supply', value: 70, color: '#0088FE' },
        { name: 'Locked/Staked', value: 20, color: '#00C49F' },
        { name: 'Team/Treasury', value: 10, color: '#FFBB28' },
      ];
    }

    const marketData = detailedData.market_data;
    const circulatingSupply = marketData.circulating_supply || 0;
    const totalSupply = marketData.total_supply || circulatingSupply;
    const maxSupply = marketData.max_supply || totalSupply;

    const circulatingPercentage = (circulatingSupply / maxSupply) * 100;
    const lockedPercentage = ((totalSupply - circulatingSupply) / maxSupply) * 100;
    const teamPercentage = 100 - circulatingPercentage - lockedPercentage;

    return [
      { name: 'Circulating Supply', value: circulatingPercentage, color: '#0088FE' },
      { name: 'Locked/Staked', value: lockedPercentage, color: '#00C49F' },
      { name: 'Team/Treasury', value: Math.max(0, teamPercentage), color: '#FFBB28' },
    ];
  };

  const marketData = calculateMarketDistribution(detailedCoinData);

  return (
    <div className="space-y-6">
      {/* Fundamental Score Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Fundamental Score Radar
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RadarChart cx="50%" cy="50%" outerRadius="80%" data={radarData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="subject" />
                <PolarRadiusAxis angle={30} domain={[0, 100]} />
                <Radar name="Score" dataKey="A" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
              </RadarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Token Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={marketData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {marketData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Code className="h-4 w-4" />
              Development Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Overall Score</span>
                <span className="text-sm font-medium">{realScores.technology.toFixed(0)}/100</span>
              </div>
              <Progress value={realScores.technology} className="h-2" />
              {detailedCoinData?.developer_data && (
                <div className="text-xs text-muted-foreground mt-2">
                  GitHub Stars: {detailedCoinData.developer_data.stars?.toLocaleString() || 'N/A'}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Users className="h-4 w-4" />
              Community Strength
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Community Score</span>
                <span className="text-sm font-medium">{realScores.community.toFixed(0)}/100</span>
              </div>
              <Progress value={realScores.community} className="h-2" />
              {detailedCoinData?.community_data && (
                <div className="text-xs text-muted-foreground mt-2">
                  Twitter: {detailedCoinData.community_data.twitter_followers?.toLocaleString() || 'N/A'}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Market Adoption
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Adoption Score</span>
                <span className="text-sm font-medium">{realScores.adoption.toFixed(0)}/100</span>
              </div>
              <Progress value={realScores.adoption} className="h-2" />
              <div className="text-xs text-muted-foreground mt-2">
                Market Cap Rank: #{coinData.market_cap_rank || 'N/A'}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Key Metrics Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Key Fundamental Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-muted-foreground">Market Cap</div>
              <div className="font-medium">${coinData.market_cap?.toLocaleString() || 'N/A'}</div>
            </div>
            <div>
              <div className="text-muted-foreground">24h Volume</div>
              <div className="font-medium">${coinData.total_volume?.toLocaleString() || 'N/A'}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Circulating Supply</div>
              <div className="font-medium">
                {detailedCoinData?.market_data?.circulating_supply?.toLocaleString() || 'N/A'}
              </div>
            </div>
            <div>
              <div className="text-muted-foreground">Max Supply</div>
              <div className="font-medium">
                {detailedCoinData?.market_data?.max_supply?.toLocaleString() || 'Unlimited'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
