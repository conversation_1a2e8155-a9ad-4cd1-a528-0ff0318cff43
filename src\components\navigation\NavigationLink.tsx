
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface NavigationLinkProps {
  href: string;
  icon: React.ReactNode;
  title: string;
  isActive: boolean;
  isNew?: boolean;
  isUpdated?: boolean;
  collapsed: boolean;
}

export function NavigationLink({
  href,
  icon,
  title,
  isActive,
  isNew,
  isUpdated,
  collapsed,
}: NavigationLinkProps) {
  return (
    <Link
      to={href}
      className={cn(
        "flex items-center gap-2.5 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent transition-colors",
        isActive && "bg-accent",
      )}
    >
      {icon}
      <span className={cn("grow", collapsed && "sr-only")}>{title}</span>
      {!collapsed && (
        <>
          {isNew && (
            <Badge variant="outline" className="h-6 rounded-md px-1.5 bg-green-500 text-white text-xs">
              New
            </Badge>
          )}
          {isUpdated && (
            <Badge variant="outline" className="h-6 rounded-md px-1.5 bg-blue-500 text-white text-xs">
              Updated
            </Badge>
          )}
        </>
      )}
    </Link>
  );
}
