
import { FundamentalCoin } from "@/hooks/useFundamentalAnalysis";

interface DevelopmentTabProps {
  coin: FundamentalCoin;
}

export function DevelopmentTab({ coin }: DevelopmentTabProps) {
  const { developerData } = coin;
  
  if (!developerData) {
    return (
      <div className="text-center py-12 text-muted-foreground">
        No development data available
      </div>
    );
  }

  return (
    <div className="p-4 space-y-4">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div className="bg-secondary/40 p-4 rounded-lg">
          <div className="text-sm text-muted-foreground">GitHub Stars</div>
          <div className="text-lg font-medium">
            {developerData.stars?.toLocaleString() || 'N/A'}
          </div>
        </div>
        <div className="bg-secondary/40 p-4 rounded-lg">
          <div className="text-sm text-muted-foreground">GitHub Forks</div>
          <div className="text-lg font-medium">
            {developerData.forks?.toLocaleString() || 'N/A'}
          </div>
        </div>
        <div className="bg-secondary/40 p-4 rounded-lg">
          <div className="text-sm text-muted-foreground">Subscribers</div>
          <div className="text-lg font-medium">
            {developerData.subscribers?.toLocaleString() || 'N/A'}
          </div>
        </div>
      </div>
      
      <div className="bg-secondary/40 p-4 rounded-lg">
        <div className="flex justify-between mb-2">
          <div className="text-sm text-muted-foreground">Recent Activity</div>
          <div className="text-sm">Last 4 weeks</div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-muted-foreground">Commits</div>
            <div className="text-lg font-medium">
              {developerData.commit_count_4_weeks || 0}
            </div>
          </div>
          <div>
            <div className="text-sm text-muted-foreground">Contributors</div>
            <div className="text-lg font-medium">
              {developerData.pull_request_contributors || 0}
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-secondary/40 p-4 rounded-lg">
        <div className="text-sm text-muted-foreground mb-2">Pull Requests</div>
        <div className="grid grid-cols-3 gap-4">
          <div>
            <div className="text-xs text-muted-foreground">Merged</div>
            <div className="text-lg font-medium">
              {developerData.pull_requests_merged || 0}
            </div>
          </div>
          <div>
            <div className="text-xs text-muted-foreground">Proposed</div>
            <div className="text-lg font-medium">
              {developerData.pull_request_contributors || 0}
            </div>
          </div>
          <div>
            <div className="text-xs text-muted-foreground">Closed Issues</div>
            <div className="text-lg font-medium">
              {developerData.closed_issues || 0}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
