
/**
 * Trend Analysis Utilities
 * Mathematical functions for calculating price trends and volatility
 */

/**
 * Calculate trend using simple linear regression
 * @param prices - Array of price values
 * @returns Slope representing the trend direction and strength
 */
export function calculateTrend(prices: number[]): number {
  const n = prices.length;
  if (n < 2) return 0;
  
  const x = Array.from({ length: n }, (_, i) => i);
  const y = prices;
  
  const sumX = x.reduce((sum, val) => sum + val, 0);
  const sumY = y.reduce((sum, val) => sum + val, 0);
  const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
  const sumXX = x.reduce((sum, val) => sum + val * val, 0);
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  
  return slope || 0;
}

/**
 * Calculate price volatility using standard deviation
 * @param prices - Array of price values
 * @returns Volatility as a percentage (0-1 range, capped at 30%)
 */
export function calculateVolatility(prices: number[]): number {
  if (prices.length < 2) return 0.1; // Default 10% volatility
  
  const mean = prices.reduce((sum, price) => sum + price, 0) / prices.length;
  const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
  const standardDeviation = Math.sqrt(variance);
  
  return Math.min(0.3, standardDeviation / mean); // Cap at 30% volatility
}

/**
 * Calculate moving average for a given period
 * @param prices - Array of price values
 * @param period - Number of periods for moving average
 * @returns Moving average value
 */
export function calculateMovingAverage(prices: number[], period: number = 7): number {
  if (prices.length === 0) return 0;
  
  const recentPrices = prices.slice(-period);
  return recentPrices.reduce((sum, price) => sum + price, 0) / recentPrices.length;
}

/**
 * Generate confidence intervals based on volatility and time distance
 * @param basePrice - Base price for the prediction
 * @param volatility - Calculated volatility
 * @param dayIndex - Day index for time-based confidence decay
 * @param totalDays - Total forecast period
 * @returns Object with upper and lower bounds
 */
export function generateConfidenceIntervals(
  basePrice: number,
  volatility: number,
  dayIndex: number,
  totalDays: number
): { upperBound: number; lowerBound: number; confidence: number } {
  const confidence = Math.max(0.3, 1 - (dayIndex / totalDays) * 0.4);
  const multiplier = volatility * (1 - confidence);
  
  return {
    upperBound: basePrice * (1 + multiplier),
    lowerBound: basePrice * (1 - multiplier),
    confidence
  };
}
