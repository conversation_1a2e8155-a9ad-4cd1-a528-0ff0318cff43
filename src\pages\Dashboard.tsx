import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import HeaderBar from "@/components/HeaderBar";
import TokenHeatmap from "@/components/TokenHeatmap";
import MarketOverviewChart from "@/components/charts/MarketOverviewChart";
import PriceChart from "@/components/charts/PriceChart";
import { fetchPriceHistory } from "@/services/api/priceHistory";
import { Home } from "lucide-react";
import TokenCorrelationMapper from "@/components/dashboard/TokenCorrelationMapper";
import EventImpactAnalyzer from "@/components/dashboard/EventImpactAnalyzer";
import VolatilityDashboard from "@/components/dashboard/VolatilityDashboard";
import TeamScreener from "@/components/dashboard/TeamScreener";

const Dashboard = () => {
  const navigate = useNavigate();
  const [bitcoinData, setBitcoinData] = useState([]);
  const [ethereumData, setEthereumData] = useState([]);
  const [marketOverviewData, setMarketOverviewData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [btcData, ethData] = await Promise.all([
          fetchPriceHistory('bitcoin', 30),
          fetchPriceHistory('ethereum', 30)
        ]);

        setBitcoinData(btcData.data || []);
        setEthereumData(ethData.data || []);

        // Generate market overview data based on BTC and ETH
        const marketData = (btcData.data || []).map((item, index) => {
          const ethItem = (ethData.data || [])[index];
          return {
            date: item.date,
            btcPrice: item.price || 0,
            ethPrice: ethItem?.price || 0,
            totalMarketCap: (item.price || 0) * 19000000 + (ethItem?.price || 0) * 120000000,
            volume: Math.random() * 50000000000 + 30000000000
          };
        });

        setMarketOverviewData(marketData);
        setIsLoading(false);

        console.log('📊 Dashboard data loaded:', {
          bitcoinDataPoints: btcData.data?.length || 0,
          ethereumDataPoints: ethData.data?.length || 0,
          marketDataPoints: marketData.length
        });
      } catch (error) {
        console.error("Error fetching price data:", error);
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar
        title="Dashboard"
        description="Cryptocurrency market overview"
        actions={
          <Button variant="outline" size="sm" onClick={() => navigate("/")} className="flex items-center gap-1">
            <Home className="h-4 w-4" />
            Market Overview
          </Button>
        }
      />

      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-medium mb-4">Market Overview</h2>
                {isLoading ? (
                  <div className="h-[300px] flex items-center justify-center">
                    <p>Loading market data...</p>
                  </div>
                ) : (
                  <MarketOverviewChart
                    data={marketOverviewData}
                    isLoading={isLoading}
                  />
                )}
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-medium mb-4">Token Heatmap</h2>
                <TokenHeatmap />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-medium mb-4">Price Charts</h2>
              <Tabs defaultValue="bitcoin">
                <TabsList className="mb-4">
                  <TabsTrigger value="bitcoin">Bitcoin</TabsTrigger>
                  <TabsTrigger value="ethereum">Ethereum</TabsTrigger>
                </TabsList>

                <TabsContent value="bitcoin">
                  <PriceChart
                    data={bitcoinData}
                    coinName="Bitcoin"
                    coinSymbol="BTC"
                    isLoading={isLoading}
                    color="#f7931a"
                  />
                </TabsContent>

                <TabsContent value="ethereum">
                  <PriceChart
                    data={ethereumData}
                    coinName="Ethereum"
                    coinSymbol="ETH"
                    isLoading={isLoading}
                    color="#627eea"
                  />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          <div className="flex gap-4">
            <Button onClick={() => navigate("/forecasting")}>
              Price Forecasting
            </Button>
            <Button variant="outline" onClick={() => navigate("/portfolio")}>
              Portfolio Analytics
            </Button>
          </div>

          {/* Enhanced Analytics Dashboard Section */}
          <div className="space-y-8 mt-8">
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 p-6 rounded-lg border-2 border-blue-200 dark:border-blue-800">
              <h2 className="text-2xl font-bold text-blue-800 dark:text-blue-200 mb-2">🚀 Enhanced Analytics Dashboard</h2>
              <p className="text-blue-700 dark:text-blue-300">Advanced AI-powered analytics and market intelligence tools</p>
            </div>

            {/* Token Correlation Mapper */}
            <TokenCorrelationMapper />

            {/* Event Impact Analyzer */}
            <EventImpactAnalyzer />

            {/* Volatility & Risk Analysis */}
            <VolatilityDashboard />

            {/* Team & Developer Screener */}
            <TeamScreener />
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
