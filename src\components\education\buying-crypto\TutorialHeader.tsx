
import { Progress } from "@/components/ui/progress";

interface TutorialHeaderProps {
  progress: number;
}

export default function TutorialHeader({ progress }: TutorialHeaderProps) {
  return (
    <>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Buying Your First Crypto: Exchanges, DEXs, and Fees</h1>
        <p className="text-muted-foreground">Learn the ins and outs of purchasing cryptocurrency, understanding different platforms, and managing transaction costs.</p>
      </div>

      {/* Progress indicator */}
      <div className="mb-8">
        <Progress value={progress} className="h-2" />
        <div className="flex justify-between mt-2 text-sm text-muted-foreground">
          <span>Getting started</span>
          <span>Complete</span>
        </div>
      </div>
    </>
  );
}
