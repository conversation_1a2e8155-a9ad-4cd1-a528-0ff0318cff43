import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, TrendingDown, Minus, <PERSON><PERSON><PERSON>riangle, CheckCircle } from 'lucide-react';
import { MarketSentiment } from '@/services/api/enhancedMarketInsights';

interface FearGreedIndexProps {
  sentiment: MarketSentiment;
  loading?: boolean;
}

export const FearGreedIndex: React.FC<FearGreedIndexProps> = ({ sentiment, loading }) => {
  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-6 h-6 bg-gray-300 rounded animate-pulse" />
            Fear & Greed Index
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded animate-pulse" />
            <div className="space-y-2">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="flex justify-between">
                  <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="w-12 h-4 bg-gray-200 rounded animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getSentimentColor = (score: number) => {
    if (score <= 20) return 'text-red-600 bg-red-50 border-red-200';
    if (score <= 40) return 'text-orange-600 bg-orange-50 border-orange-200';
    if (score <= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    if (score <= 80) return 'text-green-600 bg-green-50 border-green-200';
    return 'text-emerald-600 bg-emerald-50 border-emerald-200';
  };

  const getSentimentIcon = (score: number) => {
    if (score <= 20) return <AlertTriangle className="w-5 h-5 text-red-600" />;
    if (score <= 40) return <TrendingDown className="w-5 h-5 text-orange-600" />;
    if (score <= 60) return <Minus className="w-5 h-5 text-yellow-600" />;
    if (score <= 80) return <TrendingUp className="w-5 h-5 text-green-600" />;
    return <CheckCircle className="w-5 h-5 text-emerald-600" />;
  };

  const getProgressColor = (score: number) => {
    if (score <= 20) return 'bg-red-500';
    if (score <= 40) return 'bg-orange-500';
    if (score <= 60) return 'bg-yellow-500';
    if (score <= 80) return 'bg-green-500';
    return 'bg-emerald-500';
  };

  const indicators = [
    { name: 'Volatility', value: sentiment.indicators.volatility },
    { name: 'Market Momentum', value: sentiment.indicators.marketMomentum },
    { name: 'Social Volume', value: sentiment.indicators.socialVolume },
    { name: 'Surveys', value: sentiment.indicators.surveys },
    { name: 'Dominance', value: sentiment.indicators.dominance },
    { name: 'Trends', value: sentiment.indicators.trends }
  ];

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getSentimentIcon(sentiment.fearGreedIndex)}
            Fear & Greed Index
          </div>
          <Badge className={getSentimentColor(sentiment.fearGreedIndex)}>
            {sentiment.sentiment}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Main Index Display */}
        <div className="text-center space-y-4">
          <div className="relative">
            <div className="text-6xl font-bold text-gray-900 dark:text-white">
              {sentiment.fearGreedIndex}
            </div>
            <div className="text-sm text-gray-500 mt-1">out of 100</div>
          </div>
          
          {/* Visual Progress Bar */}
          <div className="relative">
            <Progress 
              value={sentiment.fearGreedIndex} 
              className="h-3 bg-gray-200"
            />
            <div 
              className={`absolute top-0 left-0 h-3 rounded-full transition-all duration-500 ${getProgressColor(sentiment.fearGreedIndex)}`}
              style={{ width: `${sentiment.fearGreedIndex}%` }}
            />
            
            {/* Scale markers */}
            <div className="flex justify-between text-xs text-gray-400 mt-2">
              <span>0</span>
              <span>25</span>
              <span>50</span>
              <span>75</span>
              <span>100</span>
            </div>
            
            {/* Scale labels */}
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Extreme Fear</span>
              <span>Fear</span>
              <span>Neutral</span>
              <span>Greed</span>
              <span>Extreme Greed</span>
            </div>
          </div>
        </div>

        {/* Detailed Indicators */}
        <div className="space-y-3">
          <h4 className="font-semibold text-sm text-gray-700 dark:text-gray-300">
            Contributing Factors
          </h4>
          <div className="grid grid-cols-2 gap-3">
            {indicators.map((indicator) => (
              <div key={indicator.name} className="space-y-1">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    {indicator.name}
                  </span>
                  <span className="text-xs font-medium">
                    {Math.round(indicator.value)}
                  </span>
                </div>
                <Progress 
                  value={indicator.value} 
                  className="h-1.5"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Interpretation */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h5 className="font-medium text-sm mb-2">Market Interpretation</h5>
          <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
            {sentiment.fearGreedIndex <= 20 && 
              "Extreme fear indicates potential buying opportunities as the market may be oversold. However, proceed with caution and consider dollar-cost averaging."
            }
            {sentiment.fearGreedIndex > 20 && sentiment.fearGreedIndex <= 40 && 
              "Fear in the market suggests investors are worried. This could present good entry points for long-term investors with proper risk management."
            }
            {sentiment.fearGreedIndex > 40 && sentiment.fearGreedIndex <= 60 && 
              "Neutral sentiment indicates a balanced market. Monitor key levels and wait for clearer directional signals before making major moves."
            }
            {sentiment.fearGreedIndex > 60 && sentiment.fearGreedIndex <= 80 && 
              "Greed is building in the market. Consider taking some profits and be prepared for potential corrections. Maintain disciplined position sizing."
            }
            {sentiment.fearGreedIndex > 80 && 
              "Extreme greed suggests the market may be overbought. Exercise extreme caution, consider reducing exposure, and prepare for potential volatility."
            }
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
