import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Newspaper, 
  TrendingUp, 
  TrendingDown, 
  AlertCircle,
  Clock,
  ExternalLink,
  Filter,
  Zap,
  Shield,
  Coins,
  Building,
  Gamepad2,
  Lock
} from 'lucide-react';
import { MarketNews } from '@/services/api/enhancedMarketInsights';

interface MarketNewsPanelProps {
  news: MarketNews[];
  loading?: boolean;
}

export const MarketNewsPanel: React.FC<MarketNewsPanelProps> = ({ news, loading }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSentiment, setSelectedSentiment] = useState<string>('all');

  // Ensure news is always an array
  const safeNews = Array.isArray(news) ? news : [];

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Newspaper className="w-5 h-5" />
            Market News & Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="space-y-2">
                <div className="w-3/4 h-4 bg-gray-200 rounded animate-pulse" />
                <div className="w-full h-3 bg-gray-200 rounded animate-pulse" />
                <div className="w-1/2 h-3 bg-gray-200 rounded animate-pulse" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-600 bg-green-50 border-green-200';
      case 'negative': return 'text-red-600 bg-red-50 border-red-200';
      case 'neutral': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'negative': return <TrendingDown className="w-4 h-4 text-red-600" />;
      case 'neutral': return <AlertCircle className="w-4 h-4 text-gray-600" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'regulation': return <Shield className="w-4 h-4" />;
      case 'adoption': return <Building className="w-4 h-4" />;
      case 'technology': return <Zap className="w-4 h-4" />;
      case 'market': return <TrendingUp className="w-4 h-4" />;
      case 'defi': return <Coins className="w-4 h-4" />;
      case 'nft': return <Gamepad2 className="w-4 h-4" />;
      default: return <Newspaper className="w-4 h-4" />;
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  // Filter news based on selected filters
  const filteredNews = safeNews.filter(item => {
    const categoryMatch = selectedCategory === 'all' || item.category === selectedCategory;
    const sentimentMatch = selectedSentiment === 'all' || item.sentiment === selectedSentiment;
    return categoryMatch && sentimentMatch;
  });

  // Group news by category for category view
  const newsByCategory = safeNews.reduce((acc, item) => {
    if (!acc[item.category]) acc[item.category] = [];
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, MarketNews[]>);

  const categories = [
    { id: 'all', name: 'All', icon: <Filter className="w-4 h-4" /> },
    { id: 'market', name: 'Market', icon: <TrendingUp className="w-4 h-4" /> },
    { id: 'regulation', name: 'Regulation', icon: <Shield className="w-4 h-4" /> },
    { id: 'adoption', name: 'Adoption', icon: <Building className="w-4 h-4" /> },
    { id: 'technology', name: 'Technology', icon: <Zap className="w-4 h-4" /> },
    { id: 'defi', name: 'DeFi', icon: <Coins className="w-4 h-4" /> },
    { id: 'nft', name: 'NFT', icon: <Gamepad2 className="w-4 h-4" /> }
  ];

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Newspaper className="w-5 h-5" />
          Market News & Analysis
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="timeline" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="sentiment">Sentiment</TabsTrigger>
          </TabsList>

          <TabsContent value="timeline" className="space-y-4">
            {/* Filters */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="flex items-center gap-1"
                >
                  {category.icon}
                  {category.name}
                </Button>
              ))}
            </div>

            {/* News Timeline */}
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {filteredNews.map((item) => (
                <Card key={item.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {/* Header */}
                      <div className="flex items-start justify-between gap-3">
                        <h4 className="font-semibold text-sm leading-tight flex-1">
                          {item.title}
                        </h4>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Badge className={getSentimentColor(item.sentiment)}>
                            {getSentimentIcon(item.sentiment)}
                            {item.sentiment}
                          </Badge>
                          <Badge className={getImpactColor(item.impact)}>
                            {item.impact}
                          </Badge>
                        </div>
                      </div>

                      {/* Summary */}
                      <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                        {item.summary}
                      </p>

                      {/* Footer */}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1">
                            {getCategoryIcon(item.category)}
                            <span className="capitalize">{item.category}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {formatTimeAgo(item.timestamp)}
                          </div>
                          <span>{item.source}</span>
                        </div>
                        
                        {/* Related Assets */}
                        {item.relatedAssets.length > 0 && (
                          <div className="flex items-center gap-1">
                            {item.relatedAssets.slice(0, 3).map((asset) => (
                              <Badge key={asset} variant="outline" className="text-xs">
                                {asset}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <div className="space-y-4">
              {Object.entries(newsByCategory).map(([category, categoryNews]) => (
                <Card key={category}>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-base">
                      {getCategoryIcon(category)}
                      <span className="capitalize">{category}</span>
                      <Badge variant="outline">{categoryNews.length}</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      {categoryNews.slice(0, 3).map((item) => (
                        <div key={item.id} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                          <div className="flex-1">
                            <p className="text-sm font-medium">{item.title}</p>
                            <p className="text-xs text-gray-500">{formatTimeAgo(item.timestamp)}</p>
                          </div>
                          <Badge className={getSentimentColor(item.sentiment)}>
                            {item.sentiment}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="sentiment" className="space-y-4">
            {/* Sentiment Filter */}
            <div className="flex gap-2">
              {['all', 'positive', 'neutral', 'negative'].map((sentiment) => (
                <Button
                  key={sentiment}
                  variant={selectedSentiment === sentiment ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedSentiment(sentiment)}
                  className="flex items-center gap-1"
                >
                  {sentiment !== 'all' && getSentimentIcon(sentiment)}
                  <span className="capitalize">{sentiment}</span>
                </Button>
              ))}
            </div>

            {/* Sentiment Analysis */}
            <div className="grid grid-cols-3 gap-4">
              {['positive', 'neutral', 'negative'].map((sentiment) => {
                const sentimentNews = safeNews.filter(item => item.sentiment === sentiment);
                return (
                  <Card key={sentiment}>
                    <CardContent className="p-4 text-center">
                      <div className="space-y-2">
                        <div className={`inline-flex items-center gap-1 ${getSentimentColor(sentiment)} px-2 py-1 rounded`}>
                          {getSentimentIcon(sentiment)}
                          <span className="capitalize font-medium">{sentiment}</span>
                        </div>
                        <div className="text-2xl font-bold">{sentimentNews.length}</div>
                        <div className="text-sm text-gray-500">articles</div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Filtered News by Sentiment */}
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {filteredNews.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded">
                  <div className="flex-1">
                    <p className="text-sm font-medium">{item.title}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={getImpactColor(item.impact)} variant="outline">
                        {item.impact} impact
                      </Badge>
                      <span className="text-xs text-gray-500">{formatTimeAgo(item.timestamp)}</span>
                    </div>
                  </div>
                  <Badge className={getSentimentColor(item.sentiment)}>
                    {getSentimentIcon(item.sentiment)}
                    {item.sentiment}
                  </Badge>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
