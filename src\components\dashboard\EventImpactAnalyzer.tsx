/**
 * Event-Based Price Impact Analyzer Component
 * Calendar view with impact heat scores and timeline analysis
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Calendar, TrendingUp, TrendingDown, Clock, AlertTriangle, Info } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import {
  fetchEventCalendar,
  analyzeEventImpacts,
  getEventInsights,
  CryptoEvent,
  EventCalendar,
  ImpactAnalysis
} from '@/services/api/eventImpactAnalysisService';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>ltip, <PERSON>sponsive<PERSON><PERSON>r, BarChart, Bar } from 'recharts';

interface EventImpactAnalyzerProps {
  className?: string;
}

export default function EventImpactAnalyzer({ className }: EventImpactAnalyzerProps) {
  const [viewMode, setViewMode] = useState<'calendar' | 'timeline' | 'analysis'>('calendar');
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [eventTypeFilter, setEventTypeFilter] = useState<string>('all');

  const { data: eventCalendar, isLoading: calendarLoading, refetch: refetchCalendar } = useQuery({
    queryKey: ['event-calendar'],
    queryFn: fetchEventCalendar,
    staleTime: 30 * 60 * 1000, // 30 minutes
    retry: 2
  });

  const { data: impactAnalysis, isLoading: analysisLoading } = useQuery({
    queryKey: ['event-impact-analysis'],
    queryFn: analyzeEventImpacts,
    staleTime: 60 * 60 * 1000, // 1 hour
    retry: 2
  });

  const { data: insights } = useQuery({
    queryKey: ['event-insights', eventCalendar],
    queryFn: () => {
      if (!eventCalendar) return Promise.resolve('');
      const upcomingEvents = eventCalendar
        .filter(day => new Date(day.date) >= new Date())
        .flatMap(day => day.events);
      return getEventInsights(upcomingEvents);
    },
    enabled: !!eventCalendar,
    staleTime: 30 * 60 * 1000
  });

  const getImpactColor = (impactScore: number): string => {
    if (impactScore > 30) return '#ef4444'; // High impact - red
    if (impactScore > 15) return '#f59e0b'; // Medium impact - amber
    if (impactScore > 5) return '#10b981'; // Low impact - green
    return '#6b7280'; // Minimal impact - gray
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'listing': return '🏪';
      case 'upgrade': return '⬆️';
      case 'partnership': return '🤝';
      case 'governance': return '🗳️';
      case 'regulation': return '⚖️';
      case 'hack': return '🚨';
      case 'launch': return '🚀';
      default: return '📅';
    }
  };

  const filterEvents = (events: CryptoEvent[]) => {
    if (eventTypeFilter === 'all') return events;
    return events.filter(event => event.type === eventTypeFilter);
  };

  const renderCalendarView = () => {
    if (!eventCalendar) return null;

    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - 7);
    const endDate = new Date(today);
    endDate.setDate(today.getDate() + 30);

    const calendarDays = [];
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      calendarDays.push(new Date(d));
    }

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-7 gap-2">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="text-center text-sm font-medium text-muted-foreground p-2">
              {day}
            </div>
          ))}
          
          {calendarDays.map(date => {
            const dateStr = date.toISOString().split('T')[0];
            const dayEvents = eventCalendar.find(cal => cal.date === dateStr);
            const filteredEvents = dayEvents ? filterEvents(dayEvents.events) : [];
            const totalImpact = filteredEvents.reduce((sum, event) => sum + event.impactScore, 0);
            const isToday = dateStr === today.toISOString().split('T')[0];
            const isPast = date < today;

            return (
              <Card 
                key={dateStr}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedDate === dateStr ? 'ring-2 ring-primary' : ''
                } ${isToday ? 'bg-blue-50 dark:bg-blue-950' : ''}`}
                onClick={() => setSelectedDate(selectedDate === dateStr ? null : dateStr)}
              >
                <CardContent className="p-2">
                  <div className="text-center">
                    <div className={`text-sm ${isToday ? 'font-bold text-blue-600' : isPast ? 'text-muted-foreground' : ''}`}>
                      {date.getDate()}
                    </div>
                    {filteredEvents.length > 0 && (
                      <div className="mt-1 space-y-1">
                        <div 
                          className="w-full h-2 rounded"
                          style={{ backgroundColor: getImpactColor(totalImpact) }}
                        />
                        <div className="text-xs text-muted-foreground">
                          {filteredEvents.length} event{filteredEvents.length !== 1 ? 's' : ''}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {selectedDate && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                Events for {new Date(selectedDate).toLocaleDateString()}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {(() => {
                const dayEvents = eventCalendar.find(cal => cal.date === selectedDate);
                const filteredEvents = dayEvents ? filterEvents(dayEvents.events) : [];
                
                if (filteredEvents.length === 0) {
                  return <p className="text-muted-foreground">No events scheduled for this date.</p>;
                }

                return (
                  <div className="space-y-3">
                    {filteredEvents.map(event => (
                      <div key={event.id} className="border rounded-lg p-3">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{getEventTypeIcon(event.type)}</span>
                            <div>
                              <h4 className="font-semibold">{event.title}</h4>
                              <p className="text-sm text-muted-foreground">{event.description}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge 
                              style={{ backgroundColor: getImpactColor(event.impactScore) }}
                              className="text-white"
                            >
                              {event.impactScore.toFixed(1)}% impact
                            </Badge>
                            <div className="text-xs text-muted-foreground mt-1">
                              {event.confidence.toFixed(0)}% confidence
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm">
                          <span className="flex items-center gap-1">
                            <TrendingUp className="h-3 w-3" />
                            {event.priceImpact > 0 ? '+' : ''}{event.priceImpact.toFixed(1)}%
                          </span>
                          <span>Volume: +{event.volumeImpact.toFixed(1)}%</span>
                          <Badge variant="outline">{event.assetSymbol}</Badge>
                          <Badge variant={event.sentiment === 'positive' ? 'default' : 
                                        event.sentiment === 'negative' ? 'destructive' : 'secondary'}>
                            {event.sentiment}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  const renderTimelineView = () => {
    if (!eventCalendar) return null;

    const allEvents = eventCalendar.flatMap(day => day.events);
    const filteredEvents = filterEvents(allEvents);
    const timelineData = filteredEvents
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(0, 20); // Show last 20 events

    return (
      <div className="space-y-4">
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={timelineData.map((event, index) => ({
              name: event.assetSymbol,
              date: event.date,
              impact: event.impactScore,
              priceChange: event.priceImpact,
              index
            }))}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="index"
                tickFormatter={(index) => timelineData[index]?.date.split('-')[2] || ''}
              />
              <YAxis />
              <Tooltip 
                labelFormatter={(index) => `${timelineData[index]?.title || ''} (${timelineData[index]?.date || ''})`}
                formatter={(value, name) => [
                  `${value}${name === 'impact' ? '%' : '%'}`,
                  name === 'impact' ? 'Impact Score' : 'Price Change'
                ]}
              />
              <Line type="monotone" dataKey="impact" stroke="#3b82f6" strokeWidth={2} />
              <Line type="monotone" dataKey="priceChange" stroke="#10b981" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="grid gap-2 max-h-64 overflow-y-auto">
          {timelineData.map(event => (
            <div key={event.id} className="flex items-center justify-between p-2 border rounded">
              <div className="flex items-center gap-2">
                <span>{getEventTypeIcon(event.type)}</span>
                <div>
                  <span className="font-medium">{event.assetSymbol}</span>
                  <span className="text-sm text-muted-foreground ml-2">{event.title}</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{event.date}</Badge>
                <Badge style={{ backgroundColor: getImpactColor(event.impactScore) }} className="text-white">
                  {event.impactScore.toFixed(1)}%
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderAnalysisView = () => {
    if (!impactAnalysis) return null;

    const chartData = impactAnalysis.map(analysis => ({
      type: analysis.eventType,
      avgImpact: analysis.avgImpact,
      successRate: analysis.successRate,
      volatility: analysis.volatilityIncrease
    }));

    return (
      <div className="space-y-6">
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="type" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="avgImpact" fill="#3b82f6" name="Avg Impact %" />
              <Bar dataKey="successRate" fill="#10b981" name="Success Rate %" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {impactAnalysis.map(analysis => (
            <Card key={analysis.eventType}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold capitalize">{analysis.eventType} Events</h4>
                  <span className="text-lg">{getEventTypeIcon(analysis.eventType)}</span>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Average Impact:</span>
                    <span className="font-medium">{analysis.avgImpact}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Success Rate:</span>
                    <span className="font-medium">{analysis.successRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Time to Impact:</span>
                    <span className="font-medium">{analysis.timeToImpact.toFixed(1)}h</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Volatility Increase:</span>
                    <span className="font-medium">{analysis.volatilityIncrease.toFixed(1)}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const isLoading = calendarLoading || analysisLoading;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-muted-foreground">Loading event analysis...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const eventTypes = eventCalendar ? 
    Array.from(new Set(eventCalendar.flatMap(day => day.events.map(e => e.type)))) : [];

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-primary" />
            <CardTitle>Event Impact Analyzer</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Select value={eventTypeFilter} onValueChange={setEventTypeFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Events</SelectItem>
                {eventTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {getEventTypeIcon(type)} {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button onClick={() => refetchCalendar()} variant="outline" size="sm">
              Refresh
            </Button>
          </div>
        </div>
        
        {insights && (
          <div className="bg-amber-50 dark:bg-amber-950 p-3 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-amber-800 dark:text-amber-200">{insights}</p>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="calendar">Calendar</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
          </TabsList>

          <TabsContent value="calendar" className="mt-4">
            {renderCalendarView()}
          </TabsContent>

          <TabsContent value="timeline" className="mt-4">
            {renderTimelineView()}
          </TabsContent>

          <TabsContent value="analysis" className="mt-4">
            {renderAnalysisView()}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
