
import React from 'react';
import { ArrowDown, ArrowUp } from 'lucide-react';
import { safeNumber } from './utils/formatters';

interface PriceChangeProps {
  value: number | null | undefined;
}

export default function PriceChange({ value }: PriceChangeProps) {
  const numValue = safeNumber(value);
  
  if (numValue === 0 && (value === null || value === undefined)) {
    return <span>--</span>;
  }

  const isPositive = numValue >= 0;
  return (
    <div className={`flex items-center justify-end ${isPositive ? 'text-crypto-positive' : 'text-crypto-negative'}`}>
      {isPositive ? <ArrowUp className="h-3 w-3 mr-1" /> : <ArrowDown className="h-3 w-3 mr-1" />}
      {Math.abs(numValue).toFixed(2)}%
    </div>
  );
}
