
import React from 'react';
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { AuthTabsProps } from './types';

export function AuthTabs({ value, onValueChange, signInContent, signUpContent }: AuthTabsProps) {
  return (
    <Tabs value={value} onValueChange={(v) => onValueChange(v)} className="w-full">
      <TabsList className="grid w-full grid-cols-2 mb-6">
        <TabsTrigger value="sign_in">Sign In</TabsTrigger>
        <TabsTrigger value="sign_up">Sign Up</TabsTrigger>
      </TabsList>
      
      <TabsContent value="sign_in">{signInContent}</TabsContent>
      <TabsContent value="sign_up">{signUpContent}</TabsContent>
    </Tabs>
  );
}
