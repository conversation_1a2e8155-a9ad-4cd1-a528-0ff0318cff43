
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth/useAuth';
import { Navigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import HeaderBar from '@/components/HeaderBar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, AlertTriangle, Settings, BarChart3, Users, Database, Activity, Key } from 'lucide-react';
import { APIMonitoringPanel } from '@/components/admin/APIMonitoringPanel';
import { ErrorLogsPanel } from '@/components/admin/ErrorLogsPanel';
import { UserActivityPanel } from '@/components/admin/UserActivityPanel';
import { CacheManagementPanel } from '@/components/admin/CacheManagementPanel';
import { APIConfigurationPanel } from '@/components/admin/APIConfigurationPanel';
import { SystemStatsPanel } from '@/components/admin/SystemStatsPanel';
import { EnhancedAPIManagementPanel } from '@/components/admin/EnhancedAPIManagementPanel';
import { ApiKeyManagement } from '@/components/admin/ApiKeyManagement';
import { SystemHealthDashboard } from '@/components/admin/SystemHealthDashboard';
import ApiOptimizationDashboard from '@/components/admin/ApiOptimizationDashboard';

export default function AdminDashboard() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Check if user is admin
  const { data: isAdmin, isLoading: checkingAdmin } = useQuery({
    queryKey: ['isAdmin', user?.id],
    queryFn: async () => {
      if (!user) return false;
      const { data } = await supabase.rpc('is_admin', { user_uuid: user.id });
      return data;
    },
    enabled: !!user,
  });

  // Get admin level if user is admin
  const { data: adminData } = useQuery({
    queryKey: ['adminData', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data } = await supabase
        .from('admin_users')
        .select('admin_level')
        .eq('user_id', user.id)
        .single();
      return data;
    },
    enabled: !!user && isAdmin,
  });

  // Redirect if not admin
  if (checkingAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar
        title="Administrator Dashboard"
        description="System monitoring, API management, and user analytics"
      />

      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">

          {/* Admin Status Header */}
          <Card className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border-red-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Shield className="h-8 w-8 text-red-600" />
                  <div>
                    <h2 className="text-xl font-semibold text-red-900 dark:text-red-100">
                      Administrator Access
                    </h2>
                    <p className="text-red-700 dark:text-red-300">
                      You have {adminData?.admin_level || 'admin'} privileges
                    </p>
                  </div>
                </div>
                <Badge variant="destructive" className="px-3 py-1">
                  {adminData?.admin_level?.toUpperCase() || 'ADMIN'}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Admin Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-10">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="health" className="flex items-center gap-2">
                <Activity className="w-4 h-4" />
                Health
              </TabsTrigger>
              <TabsTrigger value="optimization" className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                Optimization
              </TabsTrigger>
              <TabsTrigger value="api-keys" className="flex items-center gap-2">
                <Key className="w-4 h-4" />
                API Keys
              </TabsTrigger>
              <TabsTrigger value="api-management" className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                API Management
              </TabsTrigger>
              <TabsTrigger value="api" className="flex items-center gap-2">
                <Activity className="w-4 h-4" />
                API Monitor
              </TabsTrigger>
              <TabsTrigger value="errors" className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4" />
                Error Logs
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                User Activity
              </TabsTrigger>
              <TabsTrigger value="cache" className="flex items-center gap-2">
                <Database className="w-4 h-4" />
                Cache
              </TabsTrigger>
              <TabsTrigger value="config" className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Config
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6 mt-6">
              <SystemStatsPanel />
            </TabsContent>

            <TabsContent value="health" className="space-y-6 mt-6">
              <SystemHealthDashboard />
            </TabsContent>

            <TabsContent value="optimization" className="space-y-6 mt-6">
              <ApiOptimizationDashboard />
            </TabsContent>

            <TabsContent value="api-keys" className="space-y-6 mt-6">
              <ApiKeyManagement />
            </TabsContent>

            <TabsContent value="api-management" className="space-y-6 mt-6">
              <EnhancedAPIManagementPanel />
            </TabsContent>

            <TabsContent value="api" className="space-y-6 mt-6">
              <APIMonitoringPanel />
            </TabsContent>

            <TabsContent value="errors" className="space-y-6 mt-6">
              <ErrorLogsPanel />
            </TabsContent>

            <TabsContent value="users" className="space-y-6 mt-6">
              <UserActivityPanel />
            </TabsContent>

            <TabsContent value="cache" className="space-y-6 mt-6">
              <CacheManagementPanel />
            </TabsContent>

            <TabsContent value="config" className="space-y-6 mt-6">
              <APIConfigurationPanel />
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}
