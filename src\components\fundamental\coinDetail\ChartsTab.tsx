
import { TrendingUp } from "lucide-react";
import { TokenMetricsChart } from "../TokenMetricsChart";
import { Skeleton } from "@/components/ui/skeleton";

interface ChartsTabProps {
  metricsData: any | null;
  isLoadingDetails: boolean;
}

export function ChartsTab({ metricsData, isLoadingDetails }: ChartsTabProps) {
  return (
    <div className="p-4 space-y-6">
      <h3 className="text-xl font-bold flex items-center gap-2">
        <TrendingUp className="h-5 w-5 text-primary" />
        Fundamental Analysis Charts
      </h3>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {isLoadingDetails ? (
          <>
            <Skeleton className="h-[300px] w-full" />
            <Skeleton className="h-[300px] w-full" />
          </>
        ) : (
          <>
            <TokenMetricsChart 
              data={metricsData?.fundamentalMetrics || []}
              title="Fundamental Metrics"
              description="Key metrics that affect this token's fundamental value"
              isLoading={isLoadingDetails}
            />
            
            <TokenMetricsChart 
              data={metricsData?.marketMetrics || []}
              title="Market Metrics"
              description="Current market performance indicators"
              isLoading={isLoadingDetails}
            />
          </>
        )}
        
        {isLoadingDetails ? (
          <Skeleton className="h-[300px] w-full lg:col-span-2" />
        ) : (
          <TokenMetricsChart 
            volumeData={metricsData?.tradingVolume || []}
            data={[]}
            title="Trading Volume History"
            description="Historical trading volume data"
            isLoading={isLoadingDetails}
          />
        )}
      </div>
    </div>
  );
}
