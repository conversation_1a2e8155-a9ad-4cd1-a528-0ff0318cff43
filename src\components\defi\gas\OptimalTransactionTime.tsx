
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Clock, Settings } from "lucide-react";

interface OptimalTransactionTimeProps {
  optimalTime: string | null;
  formatTimeRemaining: (timeString: string) => string;
}

const OptimalTransactionTime = ({
  optimalTime,
  formatTimeRemaining,
}: OptimalTransactionTimeProps) => {
  return (
    <div className="p-4 border rounded-lg bg-primary/10">
      <h3 className="font-medium mb-2 flex items-center gap-2">
        <Settings className="h-4 w-4" /> Optimal Transaction Time
      </h3>
      <div className="flex items-center gap-3 mb-3">
        <div className="bg-primary/20 p-2 rounded-full">
          <Clock className="h-5 w-5 text-primary" />
        </div>
        <div>
          <p className="font-bold">{optimalTime}</p>
          <p className="text-sm text-muted-foreground">
            In {formatTimeRemaining(optimalTime || '')}
          </p>
        </div>
      </div>
      <Button className="w-full mt-1" size="sm">
        Set Gas Price Reminder
      </Button>
    </div>
  );
};

export default OptimalTransactionTime;
