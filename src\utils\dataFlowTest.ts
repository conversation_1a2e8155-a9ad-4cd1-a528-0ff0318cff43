/**
 * Data Flow Test Utility
 * Comprehensive testing for real-time data display in the application
 */

import { fetchPriceHistory } from '@/services/api/priceHistory';
import { getCoinMarketData } from '@/services/api/coinMarketData';
import { fetchDashboardMarketData } from '@/services/api/coinGeckoClient';

export class DataFlowTest {
  /**
   * Test price history data fetching
   */
  static async testPriceHistory() {
    console.log('🧪 Testing Price History Data...');
    
    try {
      const [btcData, ethData] = await Promise.all([
        fetchPriceHistory('bitcoin', 30),
        fetchPriceHistory('ethereum', 30)
      ]);
      
      console.log('📊 Bitcoin Data:', {
        dataPoints: btcData.data?.length || 0,
        firstPoint: btcData.data?.[0],
        lastPoint: btcData.data?.[btcData.data.length - 1],
        sampleData: btcData.data?.slice(0, 3)
      });
      
      console.log('📊 Ethereum Data:', {
        dataPoints: ethData.data?.length || 0,
        firstPoint: ethData.data?.[0],
        lastPoint: ethData.data?.[ethData.data.length - 1],
        sampleData: ethData.data?.slice(0, 3)
      });
      
      return {
        success: true,
        bitcoin: btcData,
        ethereum: ethData,
        message: 'Price history data fetched successfully'
      };
      
    } catch (error: any) {
      console.error('❌ Price history test failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Price history data fetch failed'
      };
    }
  }

  /**
   * Test market data for heatmap
   */
  static async testHeatmapData() {
    console.log('🧪 Testing Heatmap Market Data...');
    
    const topCoinIds = [
      'bitcoin', 'ethereum', 'solana', 'binancecoin', 'cardano',
      'ripple', 'dogecoin', 'polygon', 'avalanche-2', 'chainlink'
    ];
    
    try {
      const marketData = await getCoinMarketData(topCoinIds);
      
      console.log('🗺️ Heatmap Data:', {
        coinsReturned: Object.keys(marketData || {}).length,
        expectedCoins: topCoinIds.length,
        sampleCoin: marketData?.bitcoin || marketData?.[Object.keys(marketData || {})[0]],
        allCoins: Object.keys(marketData || {})
      });
      
      // Check data structure
      const firstCoin = marketData?.[Object.keys(marketData || {})[0]];
      if (firstCoin) {
        console.log('📈 Sample Coin Data Structure:', {
          id: firstCoin.id,
          name: firstCoin.name,
          symbol: firstCoin.symbol,
          current_price: firstCoin.current_price,
          price_change_percentage_24h: firstCoin.price_change_percentage_24h,
          market_cap: firstCoin.market_cap
        });
      }
      
      return {
        success: true,
        data: marketData,
        coinCount: Object.keys(marketData || {}).length,
        message: 'Heatmap data fetched successfully'
      };
      
    } catch (error: any) {
      console.error('❌ Heatmap data test failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Heatmap data fetch failed'
      };
    }
  }

  /**
   * Test dashboard market stats
   */
  static async testDashboardStats() {
    console.log('🧪 Testing Dashboard Market Stats...');
    
    try {
      const stats = await fetchDashboardMarketData();
      
      console.log('📊 Dashboard Stats:', {
        totalMarketCap: stats.totalMarketCap,
        dailyVolume: stats.dailyVolume,
        btcDominance: stats.btcDominance,
        activeMarkets: stats.activeMarkets,
        change24h: stats.change24h
      });
      
      return {
        success: true,
        data: stats,
        message: 'Dashboard stats fetched successfully'
      };
      
    } catch (error: any) {
      console.error('❌ Dashboard stats test failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Dashboard stats fetch failed'
      };
    }
  }

  /**
   * Test data transformation for charts
   */
  static async testDataTransformation() {
    console.log('🧪 Testing Data Transformation...');
    
    try {
      const btcData = await fetchPriceHistory('bitcoin', 7); // 7 days for quick test
      
      if (!btcData.data || btcData.data.length === 0) {
        throw new Error('No price data returned');
      }
      
      // Test chart data format
      const chartData = btcData.data.map((item, index) => ({
        date: item.date,
        price: item.price,
        timestamp: item.timestamp,
        index: index
      }));
      
      console.log('📈 Chart Data Transformation:', {
        originalDataPoints: btcData.data.length,
        transformedDataPoints: chartData.length,
        sampleTransformed: chartData.slice(0, 3),
        dateFormat: chartData[0]?.date,
        priceFormat: chartData[0]?.price
      });
      
      return {
        success: true,
        originalData: btcData.data,
        transformedData: chartData,
        message: 'Data transformation successful'
      };
      
    } catch (error: any) {
      console.error('❌ Data transformation test failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Data transformation failed'
      };
    }
  }

  /**
   * Run all data flow tests
   */
  static async runAllTests() {
    console.log('🚀 Running Complete Data Flow Test Suite...');
    
    const results = {
      priceHistory: await this.testPriceHistory(),
      heatmapData: await this.testHeatmapData(),
      dashboardStats: await this.testDashboardStats(),
      dataTransformation: await this.testDataTransformation()
    };
    
    const allSuccessful = Object.values(results).every(r => r.success);
    
    console.log('📊 Data Flow Test Summary:', {
      priceHistory: results.priceHistory.success ? '✅' : '❌',
      heatmapData: results.heatmapData.success ? '✅' : '❌',
      dashboardStats: results.dashboardStats.success ? '✅' : '❌',
      dataTransformation: results.dataTransformation.success ? '✅' : '❌',
      overall: allSuccessful ? '✅ All tests passed' : '❌ Some tests failed'
    });
    
    if (allSuccessful) {
      console.log('🎉 All data flow tests passed! Real-time data should be displaying correctly.');
    } else {
      console.log('⚠️ Some data flow tests failed. Check the details above for issues.');
    }
    
    return {
      success: allSuccessful,
      results,
      summary: {
        total: 4,
        passed: Object.values(results).filter(r => r.success).length,
        failed: Object.values(results).filter(r => !r.success).length
      }
    };
  }

  /**
   * Quick test for immediate feedback
   */
  static async quickTest() {
    console.log('⚡ Running Quick Data Flow Test...');
    
    try {
      const btcPrice = await fetchPriceHistory('bitcoin', 1);
      const hasData = btcPrice.data && btcPrice.data.length > 0;
      
      console.log('⚡ Quick Test Result:', {
        hasData,
        dataPoints: btcPrice.data?.length || 0,
        latestPrice: btcPrice.data?.[btcPrice.data.length - 1]?.price,
        status: hasData ? '✅ Data flowing correctly' : '❌ No data received'
      });
      
      return hasData;
      
    } catch (error) {
      console.log('⚡ Quick Test Failed:', error);
      return false;
    }
  }
}

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).dataFlowTest = DataFlowTest;
  
  // Add convenient shortcuts
  (window as any).testDataFlow = () => DataFlowTest.runAllTests();
  (window as any).quickDataTest = () => DataFlowTest.quickTest();
}
