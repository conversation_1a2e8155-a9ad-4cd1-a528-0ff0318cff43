
import React from "react";
import { Badge } from "@/components/ui/badge";

interface ProtocolSelectorProps {
  protocols: any[] | null;
  selectedProtocol: string | null;
  onSelectProtocol: (id: string) => void;
  isLoading: boolean;
}

const ProtocolSelector = ({ 
  protocols, 
  selectedProtocol, 
  onSelectProtocol, 
  isLoading 
}: ProtocolSelectorProps) => {
  if (isLoading || !protocols) return null;

  return (
    <div className="flex flex-wrap gap-2">
      {protocols.map(protocol => (
        <Badge
          key={protocol.id}
          variant={selectedProtocol === protocol.id ? "default" : "outline"}
          className="cursor-pointer"
          onClick={() => onSelectProtocol(protocol.id)}
        >
          {protocol.name}
        </Badge>
      ))}
    </div>
  );
};

export default ProtocolSelector;
