
import { coinGeckoAxios, handleApiError, cacheResponse } from "./coinGeckoClient";
import { fetchTopCoins } from "./coinMarketData";

export interface PortfolioAsset {
  name: string;
  symbol: string;
  allocation: number;
  value: number;
  change: number;
}

export interface PortfolioData {
  totalValue: number;
  dailyChange: number;
  monthlyChange: number;
  assets: PortfolioAsset[];
}

// Generate portfolio data based on top coins
export const fetchPortfolioData = async (): Promise<PortfolioData> => {
  try {
    // Get real market data for top coins
    const topCoins = await fetchTopCoins(10);
    
    // Create a diversified portfolio with weighted allocations
    const totalValue = 100000; // $100,000 portfolio
    
    const assets: PortfolioAsset[] = [];
    let cumulativeAllocation = 0;
    
    // Allocate based on market cap (weighted)
    topCoins.slice(0, 7).forEach((coin, index) => {
      // Weighted allocation - more weight to higher ranked coins
      const baseAllocation = Math.max(5, 30 - (index * 5));
      const allocation = Math.min(baseAllocation, 100 - cumulativeAllocation);
      cumulativeAllocation += allocation;
      
      const value = (totalValue * allocation) / 100;
      
      assets.push({
        name: coin.name,
        symbol: coin.symbol.toUpperCase(),
        allocation,
        value,
        change: coin.price_change_percentage_24h || 0
      });
    });
    
    // Calculate overall metrics
    const dailyChange = assets.reduce(
      (acc, asset) => acc + (asset.change * asset.allocation) / 100,
      0
    );
    
    // Generate a plausible monthly change (roughly 3x daily but with some variation)
    const monthlyFactor = 2.5 + (Math.random() * 1);
    const monthlyChange = dailyChange * monthlyFactor;
    
    const portfolioData: PortfolioData = {
      totalValue,
      dailyChange: parseFloat(dailyChange.toFixed(2)),
      monthlyChange: parseFloat(monthlyChange.toFixed(2)),
      assets
    };
    
    cacheResponse("portfolio", portfolioData);
    return portfolioData;
  } catch (error) {
    return handleApiError(error, {
      key: "portfolio",
      data: {
        totalValue: 100000,
        dailyChange: 0,
        monthlyChange: 0,
        assets: []
      }
    });
  }
};
