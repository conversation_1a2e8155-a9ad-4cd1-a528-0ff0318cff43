
// Types for our rating system
export interface AssetScore {
  id: string;
  name: string;
  symbol: string;
  price: number;
  marketCap: number;
  change24h: number;
  scores: {
    technical: number;
    onChain: number;
    social: number;
    fundamental: number;
  };
  overallScore: number;
}

export interface FactorWeight {
  id: string;
  name: string;
  description: string;
  weight: number;
  category: "technical" | "onChain" | "social" | "fundamental";
}
