
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { getHealthColor } from './utils/formatters';

interface MarketHealthSummaryProps {
  sentiment: any;
  metrics: any;
  volatility: any;
  overallMarketHealth: string;
  marketTrend: string;
  riskLevel: string;
  isEnhancedLoading: boolean;
  refreshAll: () => Promise<void>;
}

export default function MarketHealthSummary({
  sentiment,
  metrics,
  volatility,
  overallMarketHealth,
  marketTrend,
  riskLevel,
  isEnhancedLoading,
  refreshAll
}: MarketHealthSummaryProps) {
  return (
    <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Market Health Overview</h3>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshAll}
              disabled={isEnhancedLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${isEnhancedLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Badge className={getHealthColor(overallMarketHealth)}>
              {overallMarketHealth.charAt(0).toUpperCase() + overallMarketHealth.slice(1)}
            </Badge>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {sentiment?.fearGreedIndex ?? '--'}
            </div>
            <div className="text-sm text-gray-600">Fear & Greed Index</div>
            <div className="text-xs text-gray-500 mt-1">
              {sentiment?.sentiment || 'Loading...'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {marketTrend.charAt(0).toUpperCase() + marketTrend.slice(1)}
            </div>
            <div className="text-sm text-gray-600">Market Trend</div>
            <div className="text-xs text-gray-500 mt-1">
              {metrics && metrics.marketCapChange24h !== null && metrics.marketCapChange24h !== undefined 
                ? `${metrics.marketCapChange24h >= 0 ? '+' : ''}${Number(metrics.marketCapChange24h).toFixed(2)}% (24h)` 
                : 'Loading...'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">
              {riskLevel.charAt(0).toUpperCase() + riskLevel.slice(1)}
            </div>
            <div className="text-sm text-gray-600">Risk Level</div>
            <div className="text-xs text-gray-500 mt-1">
              {volatility && volatility.marketVolatility !== null && volatility.marketVolatility !== undefined 
                ? `${Number(volatility.marketVolatility).toFixed(1)}% volatility` 
                : 'Loading...'}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
