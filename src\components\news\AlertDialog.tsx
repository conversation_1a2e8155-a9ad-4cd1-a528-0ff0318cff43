
import { useState } from "react";
import { AlertRule } from "@/hooks/useNewsSentiment";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";

// Define our notification types explicitly
type NotificationType = "email" | "push" | "in_app";

type AlertDialogProps = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (formData: Omit<AlertRule, "id" | "active">) => void;
};

export default function AlertDialog({ isOpen, onClose, onSubmit }: AlertDialogProps) {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    asset: "BTC",
    condition: "sentiment_below" as AlertRule["condition"],
    threshold: 0,
    timeframe: "1h",
    notifications: ["in_app"] as NotificationType[],
  });

  const handleSubmit = () => {
    onSubmit(formData);
    toast({
      title: "Alert rule created",
      description: `Alert "${formData.name}" has been created.`,
    });
    onClose();
    
    // Reset form for next use
    setFormData({
      name: "",
      asset: "BTC",
      condition: "sentiment_below" as AlertRule["condition"],
      threshold: 0,
      timeframe: "1h",
      notifications: ["in_app"] as NotificationType[],
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create Alert Rule</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="grid w-full items-center gap-2">
            <label htmlFor="name" className="text-sm font-medium">Alert Name</label>
            <input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
              placeholder="E.g., BTC Sentiment Drop"
            />
          </div>

          <div className="grid w-full items-center gap-2">
            <label htmlFor="asset" className="text-sm font-medium">Asset</label>
            <select
              id="asset"
              value={formData.asset}
              onChange={(e) => setFormData({ ...formData, asset: e.target.value })}
              className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
            >
              <option value="BTC">Bitcoin (BTC)</option>
              <option value="ETH">Ethereum (ETH)</option>
              <option value="SOL">Solana (SOL)</option>
              <option value="ADA">Cardano (ADA)</option>
              <option value="DOT">Polkadot (DOT)</option>
              <option value="AVAX">Avalanche (AVAX)</option>
              <option value="MARKET">Overall Market</option>
            </select>
          </div>

          <div className="grid w-full items-center gap-2">
            <label htmlFor="condition" className="text-sm font-medium">Condition</label>
            <select
              id="condition"
              value={formData.condition}
              onChange={(e) => setFormData({ ...formData, condition: e.target.value as AlertRule["condition"] })}
              className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
            >
              <option value="sentiment_below">Sentiment Below</option>
              <option value="sentiment_above">Sentiment Above</option>
              <option value="sentiment_change">Sentiment Change</option>
              <option value="news_mention">News Mentions</option>
            </select>
          </div>

          <div className="grid w-full items-center gap-2">
            <label htmlFor="threshold" className="text-sm font-medium">Threshold</label>
            <input
              id="threshold"
              type="number"
              value={formData.threshold}
              onChange={(e) => setFormData({ ...formData, threshold: parseFloat(e.target.value) })}
              className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
              step={formData.condition === "news_mention" ? "1" : "0.05"}
              min={formData.condition === "news_mention" ? "0" : "-1"}
              max={formData.condition === "news_mention" ? "100" : "1"}
            />
            <span className="text-xs text-muted-foreground">
              {formData.condition === "news_mention" 
                ? "Number of mentions" 
                : formData.condition === "sentiment_change" 
                  ? "Change magnitude (-1 to 1)" 
                  : "Sentiment value (-1 to 1)"}
            </span>
          </div>

          <div className="grid w-full items-center gap-2">
            <label htmlFor="timeframe" className="text-sm font-medium">Timeframe</label>
            <select
              id="timeframe"
              value={formData.timeframe}
              onChange={(e) => setFormData({ ...formData, timeframe: e.target.value })}
              className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
            >
              <option value="15m">15 minutes</option>
              <option value="1h">1 hour</option>
              <option value="4h">4 hours</option>
              <option value="12h">12 hours</option>
              <option value="24h">24 hours</option>
            </select>
          </div>

          <div className="grid w-full items-center gap-2">
            <label className="text-sm font-medium">Notification Methods</label>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="in_app"
                  checked={formData.notifications.includes('in_app')}
                  onChange={(e) => {
                    const notificationType: NotificationType = 'in_app';
                    const notifications = e.target.checked
                      ? [...formData.notifications, notificationType]
                      : formData.notifications.filter(n => n !== notificationType);
                    setFormData({ ...formData, notifications });
                  }}
                  className="h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor="in_app" className="text-sm">In-app</label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="email"
                  checked={formData.notifications.includes('email')}
                  onChange={(e) => {
                    const notificationType: NotificationType = 'email';
                    const notifications = e.target.checked
                      ? [...formData.notifications, notificationType]
                      : formData.notifications.filter(n => n !== notificationType);
                    setFormData({ ...formData, notifications });
                  }}
                  className="h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor="email" className="text-sm">Email</label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="push"
                  checked={formData.notifications.includes('push')}
                  onChange={(e) => {
                    const notificationType: NotificationType = 'push';
                    const notifications = e.target.checked
                      ? [...formData.notifications, notificationType]
                      : formData.notifications.filter(n => n !== notificationType);
                    setFormData({ ...formData, notifications });
                  }}
                  className="h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor="push" className="text-sm">Push</label>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button 
            onClick={handleSubmit}
            disabled={!formData.name || formData.notifications.length === 0}
          >
            Create Alert
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
