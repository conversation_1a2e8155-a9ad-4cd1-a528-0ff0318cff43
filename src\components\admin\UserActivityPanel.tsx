
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Users, Activity, Clock, TrendingUp } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

export function UserActivityPanel() {
  const [timeRange, setTimeRange] = useState('24h');

  // User activity statistics
  const { data: activityStats } = useQuery({
    queryKey: ['activityStats', timeRange],
    queryFn: async () => {
      const hours = timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 1;
      const { data } = await supabase
        .from('user_activity_logs')
        .select('*')
        .gte('created_at', new Date(Date.now() - hours * 60 * 60 * 1000).toISOString());

      // Calculate various metrics
      const uniqueUsers = new Set(data?.map(log => log.user_id)).size;
      const totalSessions = data?.length || 0;
      const pageViews = data?.reduce((acc, log) => {
        acc[log.page] = (acc[log.page] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const averageSessionTime = data?.reduce((sum, log) => {
        return sum + (log.session_duration_minutes || 0);
      }, 0) / (data?.length || 1);

      return {
        uniqueUsers,
        totalSessions,
        pageViews: Object.entries(pageViews || {}).map(([page, views]) => ({ page, views })),
        averageSessionTime: Math.round(averageSessionTime)
      };
    },
    refetchInterval: 60000,
  });

  // Recent user activity
  const { data: recentActivity } = useQuery({
    queryKey: ['recentActivity', timeRange],
    queryFn: async () => {
      const hours = timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 1;
      const { data } = await supabase
        .from('user_activity_logs')
        .select('*')
        .gte('created_at', new Date(Date.now() - hours * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .limit(50);
      return data || [];
    },
    refetchInterval: 30000,
  });

  // Hourly activity distribution
  const { data: hourlyActivity } = useQuery({
    queryKey: ['hourlyActivity', timeRange],
    queryFn: async () => {
      const hours = timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 1;
      const { data } = await supabase
        .from('user_activity_logs')
        .select('created_at')
        .gte('created_at', new Date(Date.now() - hours * 60 * 60 * 1000).toISOString());

      const hourlyData = data?.reduce((acc: any, log) => {
        const hour = new Date(log.created_at).getHours();
        const key = `${hour}:00`;
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {});

      return Object.entries(hourlyData || {}).map(([hour, count]) => ({
        hour,
        activity: count
      })).sort((a, b) => parseInt(a.hour) - parseInt(b.hour));
    },
    refetchInterval: 60000,
  });

  const COLORS = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4'];

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Last Hour</SelectItem>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Activity Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activityStats?.uniqueUsers || 0}</div>
            <p className="text-xs text-muted-foreground">Unique users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activityStats?.totalSessions || 0}</div>
            <p className="text-xs text-muted-foreground">User sessions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Session Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activityStats?.averageSessionTime || 0}m</div>
            <p className="text-xs text-muted-foreground">Minutes per session</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Page</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold truncate">
              {activityStats?.pageViews?.[0]?.page || 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              {activityStats?.pageViews?.[0]?.views || 0} views
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Hourly Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Activity by Hour</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={hourlyActivity}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="activity" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Page Views Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Page Views Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={activityStats?.pageViews?.slice(0, 6)}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ page, percent }) => `${page}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="views"
                >
                  {activityStats?.pageViews?.slice(0, 6).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent User Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>User ID</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Page</TableHead>
                <TableHead>Session Duration</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentActivity?.map((activity) => (
                <TableRow key={activity.id}>
                  <TableCell className="text-xs">
                    {new Date(activity.created_at).toLocaleString()}
                  </TableCell>
                  <TableCell className="text-xs font-mono">
                    {activity.user_id.substring(0, 8)}...
                  </TableCell>
                  <TableCell>{activity.action}</TableCell>
                  <TableCell>{activity.page}</TableCell>
                  <TableCell>
                    {activity.session_duration_minutes ? `${activity.session_duration_minutes}m` : 'N/A'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
