
// Safe helper function to handle null/undefined values
export const safeNumber = (value: any, defaultValue: number = 0): number => {
  if (value === null || value === undefined || value === '') return defaultValue;
  const num = Number(value);
  return isNaN(num) || !isFinite(num) ? defaultValue : num;
};

export const formatLargeNumber = (num: number | null | undefined): string => {
  const numValue = safeNumber(num);
  
  if (numValue === 0) return 'N/A';
  
  if (numValue >= **********) {
    return (numValue / **********).toFixed(2) + 'B';
  }
  if (numValue >= 1000000) {
    return (numValue / 1000000).toFixed(2) + 'M';
  }
  if (numValue >= 1000) {
    return (numValue / 1000).toFixed(2) + 'K';
  }
  return numValue.toString();
};

export const getHealthColor = (health: string) => {
  switch (health) {
    case 'excellent': return 'text-emerald-600 bg-emerald-50 border-emerald-200';
    case 'good': return 'text-green-600 bg-green-50 border-green-200';
    case 'neutral': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'poor': return 'text-orange-600 bg-orange-50 border-orange-200';
    case 'critical': return 'text-red-600 bg-red-50 border-red-200';
    default: return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};
