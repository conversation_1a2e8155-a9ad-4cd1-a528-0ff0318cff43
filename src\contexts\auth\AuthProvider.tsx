
import React, { createContext, useState, useEffect, ReactNode } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { AuthContextType, Profile } from './types';
import { fetchUserProfile, updateUserProfile } from './profileUtils';
import { useAuthActions } from './authActions';

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [authCheckComplete, setAuthCheckComplete] = useState(false); // New flag to track auth check completion
  const { toast } = useToast();
  const { signIn, signUp, signOut } = useAuthActions();

  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates after unmount

    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, newSession) => {
        console.log("Auth state change event:", event);

        if (!isMounted) return; // Skip if component is unmounted

        setSession(newSession);
        setUser(newSession?.user ?? null);

        if (newSession?.user) {
          // Use setTimeout to prevent potential deadlocks with Supabase auth
          setTimeout(() => {
            if (isMounted) {
              handleFetchProfile(newSession.user.id);
            }
          }, 0);
        } else {
          setProfile(null);
        }
      }
    );

    // THEN check for existing session - make it non-blocking
    const initializeAuth = async () => {
      try {
        // Set loading to false immediately to prevent blocking
        if (isMounted) {
          setIsLoading(false);
          setAuthCheckComplete(true);
        }

        const { data: { session } } = await supabase.auth.getSession();
        console.log("Initial session check:", session ? "Found session" : "No session");

        if (!isMounted) return; // Skip if component is unmounted

        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Don't await this to prevent blocking
          handleFetchProfile(session.user.id).catch(error => {
            console.error("Error fetching profile:", error);
          });
        }
      } catch (error) {
        console.error("Error checking auth session:", error);
        // Still set loading to false even on error
        if (isMounted) {
          setIsLoading(false);
          setAuthCheckComplete(true);
        }
      }
    };

    // Don't await this to prevent blocking
    initializeAuth();

    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const handleFetchProfile = async (userId: string) => {
    try {
      const profileData = await fetchUserProfile(userId);
      if (profileData) {
        setProfile(profileData);
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    try {
      if (!user) throw new Error('User not authenticated');

      await updateUserProfile(user.id, updates);
      setProfile(prev => prev ? { ...prev, ...updates } : null);

      toast({
        title: 'Profile updated',
        description: 'Your profile has been successfully updated.',
      });
    } catch (error: any) {
      toast({
        title: 'Update failed',
        description: error.message || 'An error occurred while updating your profile.',
        variant: 'destructive',
      });
    }
  };

  const value = {
    session,
    user,
    profile,
    isLoading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
