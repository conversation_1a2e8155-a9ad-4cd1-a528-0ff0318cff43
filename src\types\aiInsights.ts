
// Pattern Recognition Types
export type ChartPattern = {
  id: string;
  name: string;
  type: "bullish" | "bearish" | "neutral";
  confidence: number;
  description: string;
  startIndex: number;
  endIndex: number;
  pricePoints: number[];
};

// Anomaly Detection Types
export type AnomalyAlert = {
  id: string;
  type: "price_movement" | "trading_volume" | "whale_movement" | "network_activity";
  asset: string;
  symbol: string;
  severity: "low" | "medium" | "high";
  description: string;
  timestamp: string;
  metrics: any; // Additional metrics specific to anomaly type
};

// Sentiment Analysis Types
export type SentimentSource = {
  id: string;
  name: string;
  type: "social_media" | "news" | "forum" | "blog";
  sentiment: "positive" | "neutral" | "negative";
  sentimentScore: number; // 0-100
  volume: number;
  keyPhrases: string[];
  change24h: number;
  timeperiod: string;
};

// Predictive Analytics Types
export type PredictionModel = {
  coinId: string;
  historicalPrices: {
    date: string;
    price: number;
  }[];
  forecast: {
    date: string;
    price: number;
  }[];
  upperBound: {
    date: string;
    price: number;
  }[];
  lowerBound: {
    date: string;
    price: number;
  }[];
  metrics: {
    volatility: number;
    r2Score: number;
    mape: number;
    confidence: number;
  };
  modelDetails: {
    name: string;
    features: string[];
    lastUpdated: string;
  };
};

// Risk Assessment Types
export type RiskFactor = {
  name: string;
  score: number; // 0-100
  description: string;
};

export type RiskAssessment = {
  id: string;
  name: string;
  symbol: string;
  overallRisk: {
    score: number;
    label: string;
    color: string;
  };
  factors: RiskFactor[];
  recommendations: string[];
};
