
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  TutorialHeader,
  OverviewContent,
  CexVsDexContent,
  FeesExplainedContent,
  OrderTypesContent,
  CalculatorContent
} from '@/components/education/buying-crypto';

interface BuyingFirstCryptoProps {
  loading?: boolean;
}

export default function BuyingFirstCrypto({ loading = false }: BuyingFirstCryptoProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [progress, setProgress] = useState(0);

  // Update progress as user navigates
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    
    // Update progress based on tab
    const progressMap: {[key: string]: number} = {
      'overview': 20,
      'cex-vs-dex': 40,
      'fees-explained': 60,
      'order-types': 80,
      'calculator': 100
    };
    
    setProgress(progressMap[value] || 0);
  };

  if (loading) {
    return (
      <div className="space-y-4 p-4">
        <div className="h-8 bg-muted rounded animate-pulse w-1/3 mb-4"></div>
        <div className="h-4 bg-muted rounded animate-pulse w-full mb-2"></div>
        <div className="h-4 bg-muted rounded animate-pulse w-full mb-2"></div>
        <div className="h-4 bg-muted rounded animate-pulse w-3/4"></div>
        
        <div className="h-64 bg-muted rounded animate-pulse w-full my-8"></div>
        
        <div className="h-8 bg-muted rounded animate-pulse w-1/4 mb-4"></div>
        <div className="h-4 bg-muted rounded animate-pulse w-full mb-2"></div>
        <div className="h-4 bg-muted rounded animate-pulse w-full mb-2"></div>
        <div className="h-4 bg-muted rounded animate-pulse w-5/6"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <TutorialHeader progress={progress} />

      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid grid-cols-5 mb-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="cex-vs-dex">CEX vs DEX</TabsTrigger>
          <TabsTrigger value="fees-explained">Understanding Fees</TabsTrigger>
          <TabsTrigger value="order-types">Order Types</TabsTrigger>
          <TabsTrigger value="calculator">Fee Calculator</TabsTrigger>
        </TabsList>

        {/* Overview Content */}
        <TabsContent value="overview">
          <OverviewContent onContinue={() => handleTabChange('cex-vs-dex')} />
        </TabsContent>

        {/* CEX vs DEX Content */}
        <TabsContent value="cex-vs-dex">
          <CexVsDexContent onContinue={() => handleTabChange('fees-explained')} />
        </TabsContent>

        {/* Understanding Fees Content */}
        <TabsContent value="fees-explained">
          <FeesExplainedContent onContinue={() => handleTabChange('order-types')} />
        </TabsContent>

        {/* Order Types Content */}
        <TabsContent value="order-types">
          <OrderTypesContent onContinue={() => handleTabChange('calculator')} />
        </TabsContent>

        {/* Fee Calculator Content */}
        <TabsContent value="calculator">
          <CalculatorContent onReset={() => handleTabChange('overview')} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
