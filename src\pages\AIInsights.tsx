
import DashboardLayout from "@/components/DashboardLayout";
import Header<PERSON><PERSON> from "@/components/HeaderBar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Brain, 
  TrendingUp, 
  AlertTriangle, 
  Target, 
  BarChart3, 
  RefreshCw,
  Eye,
  Zap,
  Activity,
  Filter,
  Download
} from "lucide-react";
import { useAIInsights } from "@/hooks/useAIInsights";
import PatternRecognition from "@/components/insights/PatternRecognition";
import AnomalyDetection from "@/components/insights/AnomalyDetection";
import SentimentAnalysis from "@/components/insights/SentimentAnalysis";
import PredictiveAnalytics from "@/components/insights/PredictiveAnalytics";
import RiskAssessment from "@/components/insights/RiskAssessment";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";

export default function AIInsights() {
  const [selectedAsset, setSelectedAsset] = useState("bitcoin");
  const [timeframe, setTimeframe] = useState(30);
  
  const {
    patternData,
    anomalyData,
    sentimentData,
    predictionData,
    riskData,
    isLoading,
    refreshAll
  } = useAIInsights(selectedAsset);

  const handleRefresh = () => {
    refreshAll();
    toast({
      title: "Refreshing AI insights",
      description: "Fetching latest AI analysis and predictions...",
    });
  };

  const handleExport = () => {
    toast({
      title: "Exporting insights",
      description: "AI insights report will be downloaded shortly...",
    });
  };

  const assets = [
    { value: "bitcoin", label: "Bitcoin (BTC)" },
    { value: "ethereum", label: "Ethereum (ETH)" },
    { value: "solana", label: "Solana (SOL)" },
    { value: "cardano", label: "Cardano (ADA)" },
    { value: "polkadot", label: "Polkadot (DOT)" }
  ];

  const timeframes = [
    { value: 7, label: "7 Days" },
    { value: 30, label: "30 Days" },
    { value: 90, label: "90 Days" }
  ];

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar 
        title="AI-Powered Market Insights" 
        description="Advanced machine learning analysis for crypto market intelligence"
      />
      
      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          {/* Header Controls */}
          <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Brain className="h-6 w-6 text-purple-500" />
                <h2 className="text-2xl font-bold">AI Market Intelligence</h2>
              </div>
              <Badge variant="secondary" className="flex items-center gap-1">
                <Zap size={12} />
                Live Analysis
              </Badge>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {/* Asset Selector */}
              <div className="flex gap-1">
                {assets.map((asset) => (
                  <Button
                    key={asset.value}
                    variant={selectedAsset === asset.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedAsset(asset.value)}
                  >
                    {asset.label.split(' ')[0]}
                  </Button>
                ))}
              </div>
              
              {/* Timeframe Selector */}
              <div className="flex gap-1">
                {timeframes.map((tf) => (
                  <Button
                    key={tf.value}
                    variant={timeframe === tf.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setTimeframe(tf.value)}
                  >
                    {tf.label}
                  </Button>
                ))}
              </div>
              
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefresh}
                disabled={isLoading}
                className="flex items-center gap-1"
              >
                <RefreshCw size={14} className={isLoading ? "animate-spin" : ""} />
                Refresh
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleExport}
                className="flex items-center gap-1"
              >
                <Download size={14} />
                Export
              </Button>
            </div>
          </div>

          {/* AI Insights Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Eye size={16} className="text-purple-600" />
                  Patterns Detected
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {patternData?.length || 0}
                </div>
                <p className="text-xs text-purple-600 dark:text-purple-400">
                  Active technical patterns
                </p>
              </CardContent>
            </Card>

            <Card className="border-red-200 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <AlertTriangle size={16} className="text-red-600" />
                  Anomalies Found
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-700 dark:text-red-300">
                  {anomalyData?.length || 0}
                </div>
                <p className="text-xs text-red-600 dark:text-red-400">
                  Market anomalies detected
                </p>
              </CardContent>
            </Card>

            <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Activity size={16} className="text-blue-600" />
                  Sentiment Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {sentimentData?.length > 0 ? 
                    Math.round(sentimentData.reduce((acc, s) => acc + s.sentimentScore, 0) / sentimentData.length) : 
                    '--'
                  }%
                </div>
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  Average market sentiment
                </p>
              </CardContent>
            </Card>

            <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Target size={16} className="text-green-600" />
                  Prediction Accuracy
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {predictionData ? Math.round(predictionData.metrics.r2Score * 100) : '--'}%
                </div>
                <p className="text-xs text-green-600 dark:text-green-400">
                  Model R² score
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main AI Insights Tabs */}
          <Tabs defaultValue="patterns" className="space-y-4">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="patterns" className="flex items-center gap-2">
                <BarChart3 size={16} />
                Patterns
              </TabsTrigger>
              <TabsTrigger value="anomalies" className="flex items-center gap-2">
                <AlertTriangle size={16} />
                Anomalies
              </TabsTrigger>
              <TabsTrigger value="sentiment" className="flex items-center gap-2">
                <Activity size={16} />
                Sentiment
              </TabsTrigger>
              <TabsTrigger value="predictions" className="flex items-center gap-2">
                <TrendingUp size={16} />
                Predictions
              </TabsTrigger>
              <TabsTrigger value="risk" className="flex items-center gap-2">
                <Target size={16} />
                Risk
              </TabsTrigger>
            </TabsList>

            <TabsContent value="patterns">
              <PatternRecognition 
                patterns={patternData || []}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="anomalies">
              <AnomalyDetection 
                alerts={anomalyData || []}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="sentiment">
              <SentimentAnalysis 
                sources={sentimentData || []}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="predictions">
              <PredictiveAnalytics 
                prediction={predictionData}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="risk">
              <RiskAssessment 
                assessments={riskData || []}
                isLoading={isLoading}
              />
            </TabsContent>
          </Tabs>

          {/* AI Model Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain size={20} className="text-purple-500" />
                AI Model Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">Pattern Recognition</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Advanced technical analysis using machine learning to identify chart patterns and trends.
                  </p>
                  <div className="flex gap-2">
                    <Badge variant="outline">CNN</Badge>
                    <Badge variant="outline">LSTM</Badge>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Anomaly Detection</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Real-time monitoring of unusual market activities and price movements.
                  </p>
                  <div className="flex gap-2">
                    <Badge variant="outline">Isolation Forest</Badge>
                    <Badge variant="outline">Z-Score</Badge>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Sentiment Analysis</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    NLP-powered analysis of social media, news, and market sentiment.
                  </p>
                  <div className="flex gap-2">
                    <Badge variant="outline">BERT</Badge>
                    <Badge variant="outline">RoBERTa</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
