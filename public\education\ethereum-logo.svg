<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">
  <defs>
    <linearGradient id="ethGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#627EEA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3C3C3D;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="32" cy="32" r="32" fill="url(#ethGradient)"/>
  
  <!-- Ethereum diamond -->
  <g transform="translate(32, 32)">
    <!-- Top part -->
    <path d="M 0 -20 L -8 -4 L 0 0 L 8 -4 Z" fill="#FFFFFF" opacity="0.8"/>
    <!-- Bottom part -->
    <path d="M 0 0 L -8 -4 L 0 20 L 8 -4 Z" fill="#FFFFFF" opacity="0.6"/>
    <!-- Inner lines -->
    <path d="M 0 -20 L 0 0" stroke="#627EEA" stroke-width="1"/>
    <path d="M 0 0 L 0 20" stroke="#627EEA" stroke-width="1"/>
  </g>
  
  <!-- ETH text -->
  <text x="32" y="52" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">ETH</text>
</svg>
