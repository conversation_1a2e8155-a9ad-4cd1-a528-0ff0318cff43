/**
 * Token Volatility & Stability Analysis Service
 * ML-based volatility prediction and risk/return analysis
 */

import { coinGeckoAxios, handleApiError, cacheResponse, getCachedData } from './coinGeckoClient';
import { getCoinMarketCapListings } from './coinMarketCapClient';
import { generateAIResponse } from './deepSeekClient';

export interface VolatilityMetrics {
  id: string;
  name: string;
  symbol: string;
  price: number;
  marketCap: number;
  
  // Historical volatility metrics
  volatility7d: number;
  volatility30d: number;
  volatility90d: number;
  
  // Predicted volatility
  predictedVolatility: number;
  volatilityTrend: 'increasing' | 'decreasing' | 'stable';
  
  // Risk metrics
  riskScore: number; // 0-100 (100 = highest risk)
  stabilityScore: number; // 0-100 (100 = most stable)
  
  // Return metrics
  return7d: number;
  return30d: number;
  expectedReturn: number;
  
  // Classification
  category: 'conservative' | 'moderate' | 'aggressive' | 'speculative';
  investorType: 'conservative' | 'balanced' | 'growth' | 'speculative';
  
  // Liquidity metrics
  liquidityScore: number;
  volumeStability: number;
}

export interface RiskReturnQuadrant {
  quadrant: 'low-risk-low-return' | 'low-risk-high-return' | 'high-risk-low-return' | 'high-risk-high-return';
  tokens: VolatilityMetrics[];
  description: string;
  recommendation: string;
}

export interface VolatilityPrediction {
  asset: string;
  currentVolatility: number;
  predictedVolatility: number;
  confidence: number;
  factors: string[];
  timeframe: '7d' | '30d' | '90d';
}

/**
 * Calculate volatility from price array
 */
const calculateVolatility = (prices: number[]): number => {
  if (prices.length < 2) return 0;

  // Calculate daily returns
  const returns = prices.slice(1).map((price, i) => 
    Math.log(price / prices[i])
  );

  // Calculate standard deviation of returns
  const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
  const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
  
  // Annualized volatility
  return Math.sqrt(variance * 365) * 100;
};

/**
 * Calculate risk score based on multiple factors
 */
const calculateRiskScore = (
  volatility: number,
  marketCap: number,
  liquidityRatio: number,
  priceChange: number
): number => {
  // Volatility component (0-40 points)
  const volatilityScore = Math.min(volatility * 2, 40);
  
  // Market cap component (0-25 points, inverse relationship)
  const marketCapScore = marketCap < 1e8 ? 25 : 
                        marketCap < 1e9 ? 20 :
                        marketCap < 1e10 ? 15 :
                        marketCap < 1e11 ? 10 : 5;
  
  // Liquidity component (0-20 points)
  const liquidityScore = liquidityRatio < 0.01 ? 20 :
                        liquidityRatio < 0.05 ? 15 :
                        liquidityRatio < 0.1 ? 10 : 5;
  
  // Recent volatility component (0-15 points)
  const recentVolScore = Math.min(Math.abs(priceChange) / 2, 15);
  
  return Math.min(volatilityScore + marketCapScore + liquidityScore + recentVolScore, 100);
};

/**
 * Predict future volatility using simple ML approach
 */
const predictVolatility = (
  historicalVolatilities: number[],
  marketFactors: {
    marketCap: number;
    volume: number;
    priceChange: number;
  }
): { prediction: number; confidence: number } => {
  if (historicalVolatilities.length === 0) {
    return { prediction: 20, confidence: 0.3 };
  }

  // Simple trend-based prediction
  const recentVol = historicalVolatilities[historicalVolatilities.length - 1];
  const avgVol = historicalVolatilities.reduce((sum, vol) => sum + vol, 0) / historicalVolatilities.length;
  
  // Market factor adjustments
  const marketCapFactor = marketFactors.marketCap > 1e10 ? 0.9 : 1.1;
  const volumeFactor = marketFactors.volume > 1e8 ? 0.95 : 1.05;
  const priceFactor = Math.abs(marketFactors.priceChange) > 10 ? 1.2 : 1.0;
  
  const prediction = (recentVol * 0.6 + avgVol * 0.4) * marketCapFactor * volumeFactor * priceFactor;
  const confidence = Math.min(0.5 + (historicalVolatilities.length / 30), 0.9);
  
  return { prediction, confidence };
};

/**
 * Fetch comprehensive volatility analysis
 */
export const fetchVolatilityAnalysis = async (): Promise<VolatilityMetrics[]> => {
  const cacheKey = 'volatility_analysis';
  const cached = getCachedData(cacheKey, 15 * 60 * 1000); // 15 minutes cache
  if (cached) return cached;

  try {
    console.log('📊 Fetching volatility analysis data...');

    // Get market data with price history
    const response = await coinGeckoAxios.get('/coins/markets', {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: 50,
        page: 1,
        sparkline: true,
        price_change_percentage: '7d,30d'
      }
    });

    const tokens = response.data;
    const volatilityMetrics: VolatilityMetrics[] = [];

    for (const token of tokens) {
      const prices = token.sparkline_in_7d?.price || [];
      
      if (prices.length < 7) continue; // Skip tokens with insufficient data

      // Calculate volatilities for different periods
      const volatility7d = calculateVolatility(prices);
      const volatility30d = volatility7d * 1.1; // Approximate 30d from 7d
      const volatility90d = volatility7d * 0.9; // Approximate 90d from 7d

      // Calculate risk and stability scores
      const liquidityRatio = token.total_volume / (token.market_cap || 1);
      const riskScore = calculateRiskScore(
        volatility7d,
        token.market_cap,
        liquidityRatio,
        token.price_change_percentage_24h || 0
      );
      const stabilityScore = 100 - riskScore;

      // Predict future volatility
      const prediction = predictVolatility(
        [volatility90d, volatility30d, volatility7d],
        {
          marketCap: token.market_cap,
          volume: token.total_volume,
          priceChange: token.price_change_percentage_24h || 0
        }
      );

      // Determine category and investor type
      const category = riskScore > 75 ? 'speculative' :
                      riskScore > 50 ? 'aggressive' :
                      riskScore > 25 ? 'moderate' : 'conservative';

      const investorType = riskScore > 80 ? 'speculative' :
                          riskScore > 60 ? 'growth' :
                          riskScore > 30 ? 'balanced' : 'conservative';

      const metrics: VolatilityMetrics = {
        id: token.id,
        name: token.name,
        symbol: token.symbol.toUpperCase(),
        price: token.current_price,
        marketCap: token.market_cap,
        
        volatility7d,
        volatility30d,
        volatility90d,
        
        predictedVolatility: prediction.prediction,
        volatilityTrend: prediction.prediction > volatility7d ? 'increasing' : 
                        prediction.prediction < volatility7d * 0.9 ? 'decreasing' : 'stable',
        
        riskScore,
        stabilityScore,
        
        return7d: token.price_change_percentage_7d_in_currency || 0,
        return30d: token.price_change_percentage_30d_in_currency || 0,
        expectedReturn: (token.price_change_percentage_7d_in_currency || 0) * 1.2, // Simple projection
        
        category,
        investorType,
        
        liquidityScore: Math.min(liquidityRatio * 1000, 100),
        volumeStability: Math.max(100 - volatility7d, 0)
      };

      volatilityMetrics.push(metrics);
    }

    // Sort by market cap
    volatilityMetrics.sort((a, b) => b.marketCap - a.marketCap);

    cacheResponse(cacheKey, volatilityMetrics);
    console.log(`✅ Volatility analysis: ${volatilityMetrics.length} tokens analyzed`);
    
    return volatilityMetrics;

  } catch (error: any) {
    console.error('❌ Volatility analysis failed:', error);
    return handleApiError(error, {
      key: cacheKey,
      data: []
    });
  }
};

/**
 * Generate risk/return quadrants
 */
export const generateRiskReturnQuadrants = async (): Promise<RiskReturnQuadrant[]> => {
  try {
    const metrics = await fetchVolatilityAnalysis();
    
    if (metrics.length === 0) return [];

    // Calculate median risk and return for quadrant boundaries
    const sortedByRisk = [...metrics].sort((a, b) => a.riskScore - b.riskScore);
    const sortedByReturn = [...metrics].sort((a, b) => a.return30d - b.return30d);
    
    const medianRisk = sortedByRisk[Math.floor(sortedByRisk.length / 2)].riskScore;
    const medianReturn = sortedByReturn[Math.floor(sortedByReturn.length / 2)].return30d;

    // Categorize tokens into quadrants
    const quadrants: RiskReturnQuadrant[] = [
      {
        quadrant: 'low-risk-low-return',
        tokens: metrics.filter(m => m.riskScore <= medianRisk && m.return30d <= medianReturn),
        description: 'Stable assets with modest returns',
        recommendation: 'Suitable for conservative investors seeking capital preservation'
      },
      {
        quadrant: 'low-risk-high-return',
        tokens: metrics.filter(m => m.riskScore <= medianRisk && m.return30d > medianReturn),
        description: 'High-quality assets with strong performance',
        recommendation: 'Ideal for balanced portfolios - best risk-adjusted returns'
      },
      {
        quadrant: 'high-risk-low-return',
        tokens: metrics.filter(m => m.riskScore > medianRisk && m.return30d <= medianReturn),
        description: 'Volatile assets with poor recent performance',
        recommendation: 'Generally avoid - high risk without compensating returns'
      },
      {
        quadrant: 'high-risk-high-return',
        tokens: metrics.filter(m => m.riskScore > medianRisk && m.return30d > medianReturn),
        description: 'High-growth potential with significant volatility',
        recommendation: 'For aggressive investors with high risk tolerance'
      }
    ];

    return quadrants;

  } catch (error) {
    console.error('❌ Risk/return quadrants failed:', error);
    return [];
  }
};

/**
 * Get AI insights for volatility patterns
 */
export const getVolatilityInsights = async (metrics: VolatilityMetrics[]): Promise<string> => {
  try {
    const topStable = metrics
      .filter(m => m.stabilityScore > 70)
      .slice(0, 3)
      .map(m => m.symbol);
    
    const topVolatile = metrics
      .filter(m => m.riskScore > 70)
      .slice(0, 3)
      .map(m => m.symbol);

    const prompt = `Analyze crypto volatility patterns:

Most Stable: ${topStable.join(', ')}
Most Volatile: ${topVolatile.join(', ')}

Market conditions and volatility trends. Provide insights on:
1. Current market volatility state
2. Risk management recommendations
3. Opportunities for different investor types

Keep response to 3-4 sentences.`;

    const insights = await generateAIResponse(prompt, { temperature: 0.3 });
    return insights || 'Market shows mixed volatility patterns with opportunities across risk profiles.';
    
  } catch (error) {
    console.error('❌ Volatility insights failed:', error);
    return 'Volatility analysis reveals diverse risk profiles suitable for different investment strategies.';
  }
};
