import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { getCoinMarketData } from "@/services/api/coinMarketData";

type HeatmapItem = {
  id: string;
  name: string;
  symbol: string;
  sentiment: "very-high" | "high" | "medium" | "low" | "very-low";
  whaleCount: number;
  volumeChange: number;
  price: number;
  priceChange24h: number;
  marketCap: number;
};

// Mock data for token heatmap with all required properties
const mockHeatmapData: HeatmapItem[] = [
  { id: "1", name: "Bitcoin", symbol: "BTC", sentiment: "very-high", whaleCount: 12, volumeChange: 23.4, price: 43250.00, priceChange24h: 3.2, marketCap: 850000000000 },
  { id: "2", name: "Ethereum", symbol: "ETH", sentiment: "high", whaleCount: 10, volumeChange: 15.2, price: 2650.00, priceChange24h: 2.1, marketCap: 320000000000 },
  { id: "3", name: "<PERSON><PERSON>", symbol: "SOL", sentiment: "very-high", whaleCount: 8, volumeChange: 32.1, price: 98.50, priceChange24h: 5.7, marketCap: 45000000000 },
  { id: "4", name: "Binance Coin", symbol: "BNB", sentiment: "medium", whaleCount: 6, volumeChange: 5.7, price: 315.20, priceChange24h: 1.2, marketCap: 48000000000 },
  { id: "5", name: "Cardano", symbol: "ADA", sentiment: "low", whaleCount: 4, volumeChange: -2.3, price: 0.52, priceChange24h: -1.8, marketCap: 18000000000 },
  { id: "6", name: "XRP", symbol: "XRP", sentiment: "medium", whaleCount: 5, volumeChange: 8.9, price: 0.58, priceChange24h: 2.4, marketCap: 32000000000 },
  { id: "7", name: "Dogecoin", symbol: "DOGE", sentiment: "very-low", whaleCount: 3, volumeChange: -12.6, price: 0.078, priceChange24h: -4.2, marketCap: 11000000000 },
  { id: "8", name: "Polygon", symbol: "MATIC", sentiment: "high", whaleCount: 7, volumeChange: 18.3, price: 0.95, priceChange24h: 3.1, marketCap: 9000000000 },
  { id: "9", name: "Avalanche", symbol: "AVAX", sentiment: "high", whaleCount: 7, volumeChange: 14.2, price: 38.20, priceChange24h: 2.8, marketCap: 15000000000 },
  { id: "10", name: "Chainlink", symbol: "LINK", sentiment: "medium", whaleCount: 5, volumeChange: 7.8, price: 14.50, priceChange24h: 1.5, marketCap: 8500000000 },
  { id: "11", name: "Cosmos", symbol: "ATOM", sentiment: "low", whaleCount: 4, volumeChange: 3.1, price: 9.85, priceChange24h: 0.8, marketCap: 3800000000 },
  { id: "12", name: "Arbitrum", symbol: "ARB", sentiment: "high", whaleCount: 8, volumeChange: 21.5, price: 1.25, priceChange24h: 4.2, marketCap: 4200000000 },
  { id: "13", name: "Uniswap", symbol: "UNI", sentiment: "medium", whaleCount: 6, volumeChange: 9.7, price: 6.80, priceChange24h: 1.9, marketCap: 5200000000 },
  { id: "14", name: "Polkadot", symbol: "DOT", sentiment: "low", whaleCount: 3, volumeChange: -1.8, price: 6.45, priceChange24h: -0.5, marketCap: 8900000000 },
  { id: "15", name: "Shiba Inu", symbol: "SHIB", sentiment: "very-low", whaleCount: 2, volumeChange: -8.9, price: 0.000024, priceChange24h: -2.1, marketCap: 14000000000 }
];

export default function TokenHeatmap() {
  // Top coins to fetch for heatmap
  const topCoinIds = [
    'bitcoin', 'ethereum', 'solana', 'binancecoin', 'cardano',
    'ripple', 'dogecoin', 'polygon', 'avalanche-2', 'chainlink',
    'cosmos', 'arbitrum', 'uniswap', 'polkadot', 'shiba-inu'
  ];

  // Fetch real market data
  const { data: marketData, isLoading, error } = useQuery({
    queryKey: ['heatmap-market-data', topCoinIds],
    queryFn: () => getCoinMarketData(topCoinIds),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });

  // Transform market data to heatmap format
  const transformToHeatmapData = (data: any): HeatmapItem[] => {
    if (!data || typeof data !== 'object') return [];

    return topCoinIds.map((coinId, index) => {
      const coin = data[coinId];
      if (!coin) {
        // Fallback data if coin not found
        return {
          id: coinId,
          name: coinId.charAt(0).toUpperCase() + coinId.slice(1),
          symbol: coinId.substring(0, 3).toUpperCase(),
          sentiment: "medium" as const,
          whaleCount: Math.floor(Math.random() * 10) + 1,
          volumeChange: (Math.random() - 0.5) * 40,
          price: 0,
          priceChange24h: 0,
          marketCap: 0
        };
      }

      const priceChange = coin.price_change_percentage_24h || 0;

      // Determine sentiment based on price change and volume
      let sentiment: HeatmapItem["sentiment"];
      if (priceChange > 15) sentiment = "very-high";
      else if (priceChange > 5) sentiment = "high";
      else if (priceChange > -5) sentiment = "medium";
      else if (priceChange > -15) sentiment = "low";
      else sentiment = "very-low";

      return {
        id: coin.id,
        name: coin.name,
        symbol: coin.symbol.toUpperCase(),
        sentiment,
        whaleCount: Math.floor(Math.random() * 15) + 1, // Simulated whale count
        volumeChange: priceChange,
        price: coin.current_price || 0,
        priceChange24h: priceChange,
        marketCap: coin.market_cap || 0
      };
    });
  };

  const heatmapData = marketData ? transformToHeatmapData(marketData) : [];

  const getSentimentColor = (sentiment: HeatmapItem["sentiment"]) => {
    switch(sentiment) {
      case "very-high": return "bg-emerald-500";
      case "high": return "bg-green-400";
      case "medium": return "bg-amber-400";
      case "low": return "bg-orange-400";
      case "very-low": return "bg-red-400";
      default: return "bg-gray-300";
    }
  };

  if (isLoading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground">Loading real-time heatmap data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-2">Failed to load heatmap data</p>
          <p className="text-sm text-muted-foreground">Please check your API connection</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex text-sm text-muted-foreground mb-4">
        <div className="flex items-center mr-4">
          <span className="w-3 h-3 inline-block rounded-sm bg-emerald-500 mr-1"></span> Very High
        </div>
        <div className="flex items-center mr-4">
          <span className="w-3 h-3 inline-block rounded-sm bg-green-400 mr-1"></span> High
        </div>
        <div className="flex items-center mr-4">
          <span className="w-3 h-3 inline-block rounded-sm bg-amber-400 mr-1"></span> Medium
        </div>
        <div className="flex items-center mr-4">
          <span className="w-3 h-3 inline-block rounded-sm bg-orange-400 mr-1"></span> Low
        </div>
        <div className="flex items-center">
          <span className="w-3 h-3 inline-block rounded-sm bg-red-400 mr-1"></span> Very Low
        </div>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
        {heatmapData.map((item) => (
          <div
            key={item.id}
            className={`${getSentimentColor(item.sentiment)} rounded-lg p-4 text-white hover:opacity-90 transition-opacity cursor-pointer`}
            title={`${item.name} - $${item.price.toFixed(item.price < 1 ? 6 : 2)}`}
          >
            <div className="font-bold">{item.symbol}</div>
            <div className="text-sm truncate">{item.name}</div>
            <div className="mt-1 text-xs">
              ${item.price < 1 ? item.price.toFixed(6) : item.price.toFixed(2)}
            </div>
            <div className="mt-2 text-xs flex justify-between">
              <span>Whales: {item.whaleCount}</span>
              <span className={item.priceChange24h >= 0 ? "text-emerald-100" : "text-red-100"}>
                {item.priceChange24h > 0 ? "+" : ""}{item.priceChange24h.toFixed(1)}%
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
