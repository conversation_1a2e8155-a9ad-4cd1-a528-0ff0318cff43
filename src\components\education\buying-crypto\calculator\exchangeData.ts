
import { Exchange } from "./types";

// Sample exchange data with more realistic values and better documentation
export const exchanges: Record<string, Exchange> = {
  'cex-1': { 
    name: 'Coinbase', 
    type: 'CEX', 
    tradingFee: 0.6, 
    withdrawalFee: 5, 
    security: 'High', 
    liquidity: 'High',
    description: 'User-friendly platform with strong regulatory compliance and insurance protection.'
  },
  'cex-2': { 
    name: 'Binance', 
    type: 'CEX', 
    tradingFee: 0.1, 
    withdrawalFee: 3, 
    security: 'High', 
    liquidity: 'Very High',
    description: 'Largest global exchange by volume with competitive fees and extensive coin selection.'
  },
  'cex-3': { 
    name: '<PERSON><PERSON><PERSON>', 
    type: 'CEX', 
    tradingFee: 0.26, 
    withdrawalFee: 4, 
    security: 'High', 
    liquidity: 'High',
    description: 'Strong security focus with advanced trading features and good customer support.'
  },
  'dex-1': { 
    name: 'Uniswap', 
    type: 'DEX', 
    tradingFee: 0.3, 
    withdrawalFee: 0, 
    security: 'Medium', 
    liquidity: 'Medium', 
    gasEstimate: 25,
    description: 'Popular Ethereum DEX using automated market makers with widespread token support.'
  },
  'dex-2': { 
    name: 'SushiSwap', 
    type: 'DEX', 
    tradingFee: 0.3, 
    withdrawalFee: 0, 
    security: 'Medium', 
    liquidity: 'Medium', 
    gasEstimate: 20,
    description: 'Fork of Uniswap with additional features like yield farming and staking rewards.'
  },
  'dex-3': { 
    name: 'Curve', 
    type: 'DEX', 
    tradingFee: 0.04, 
    withdrawalFee: 0, 
    security: 'Medium', 
    liquidity: 'High', 
    gasEstimate: 30,
    description: 'Specialized DEX optimized for stablecoin trading with low slippage and fees.'
  }
};
