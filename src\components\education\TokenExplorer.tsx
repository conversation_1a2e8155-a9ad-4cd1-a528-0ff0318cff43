
import React from 'react';
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from "recharts";
import { ChevronLeft, ExternalLink, Calendar } from "lucide-react";
import { TokenDeepDive } from "@/hooks/useEducation.tsx";

interface TokenExplorerProps {
  token: TokenDeepDive;
}

export default function TokenExplorer({ token }: TokenExplorerProps) {
  const navigate = useNavigate();

  // Colors for the distribution chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

  // Format distribution data for the pie chart
  const distributionData = token.tokenomics.distribution.map(item => ({
    name: item.name,
    value: item.percentage
  }));

  return (
    <div className="space-y-8">
      {/* Header with back button */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-1 w-fit"
          onClick={() => navigate("/education")}
        >
          <ChevronLeft size={16} />
          Back to Education
        </Button>
      </div>

      {/* Token Header */}
      <div className="flex flex-col md:flex-row gap-6 items-start">
        {/* Logo */}
        <div className="w-20 h-20 md:w-24 md:h-24 rounded-lg bg-muted flex items-center justify-center overflow-hidden">
          <img
            src={token.logoUrl}
            alt={token.name}
            className="w-16 h-16 md:w-20 md:h-20 object-contain"
            onError={(e) => {
              (e.target as HTMLImageElement).src = '/placeholder.svg';
            }}
          />
        </div>

        {/* Title and basic info */}
        <div className="flex-1 space-y-2">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
            <h1 className="text-3xl font-bold tracking-tight">{token.name}</h1>
            <Badge variant="outline" className="text-base h-auto py-1 px-2">{token.symbol}</Badge>
          </div>

          <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
            <Badge variant="secondary">{token.category}</Badge>
            <div className="flex items-center gap-1">
              <Calendar size={14} />
              <span>Founded in {token.foundedIn}</span>
            </div>
          </div>

          <p className="text-muted-foreground max-w-3xl">{token.description}</p>
        </div>
      </div>

      <Separator />

      {/* Token Information Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Founder and Use Case */}
        <Card>
          <CardHeader>
            <CardTitle>Founder & Use Case</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-medium mb-1">Founded By</h3>
              <p>{token.founder}</p>
            </div>
            <div>
              <h3 className="font-medium mb-1">Primary Use Case</h3>
              <p>{token.useCase}</p>
            </div>
          </CardContent>
        </Card>

        {/* Tokenomics */}
        <Card>
          <CardHeader>
            <CardTitle>Tokenomics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Total Supply</h3>
                <p className="font-semibold">{token.tokenomics.totalSupply}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Circulating Supply</h3>
                <p className="font-semibold">{token.tokenomics.circulatingSupply}</p>
              </div>
            </div>

            <div>
              <h3 className="font-medium mb-2">Initial Token Distribution</h3>
              <div className="h-[200px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={distributionData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {distributionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value}%`} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technology */}
        <Card>
          <CardHeader>
            <CardTitle>Technology</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{token.technology}</p>
          </CardContent>
        </Card>

        {/* Ecosystem */}
        <Card>
          <CardHeader>
            <CardTitle>Ecosystem</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{token.ecosystem}</p>
          </CardContent>
        </Card>

        {/* Partnerships */}
        <Card>
          <CardHeader>
            <CardTitle>Key Partnerships</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {token.partnerships.map((partner, index) => (
                <Badge key={index} variant="outline">{partner}</Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Roadmap */}
        <Card>
          <CardHeader>
            <CardTitle>Development Roadmap</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {token.roadmap.map((phase, index) => (
              <div key={index}>
                <h3 className="font-medium">{phase.quarter}</h3>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  {phase.milestones.map((milestone, i) => (
                    <li key={i} className="text-muted-foreground text-sm">{milestone}</li>
                  ))}
                </ul>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Risks */}
        <Card>
          <CardHeader>
            <CardTitle>Potential Risks</CardTitle>
            <CardDescription>Important factors to consider before investing</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {token.risks.map((risk, index) => (
                <li key={index} className="flex gap-2 items-start">
                  <span className="inline-block h-1.5 w-1.5 rounded-full bg-destructive mt-2"></span>
                  <span>{risk}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Official Links */}
        <Card>
          <CardHeader>
            <CardTitle>Official Resources</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {token.links.map((link, index) => (
                <a
                  key={index}
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-primary hover:underline"
                >
                  <span>{link.name}</span>
                  <ExternalLink size={14} />
                </a>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
