
/**
 * Format currency values with appropriate suffixes
 */
export const formatCurrency = (value: number): string => {
  if (value >= 1000000000) {
    return `$${(value / 1000000000).toFixed(2)}B`;
  } else if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(2)}M`;
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(2)}K`;
  } else {
    return `$${value.toFixed(2)}`;
  }
};

/**
 * Process security data for pie chart visualization
 */
export const processSecurityPieData = (protocol: any | null) => {
  if (!protocol || !protocol.audits) return [];
  
  const data = [];
  let totalIssues = 0;
  
  // Sum up issues
  protocol.audits.forEach((audit: any) => {
    if (audit.issues) {
      totalIssues += audit.issues.critical + audit.issues.high + audit.issues.medium + audit.issues.low;
      
      if (audit.issues.critical > 0) {
        data.push({ name: "Critical", value: audit.issues.critical, color: "#ef4444" });
      }
      if (audit.issues.high > 0) {
        data.push({ name: "High", value: audit.issues.high, color: "#f97316" });
      }
      if (audit.issues.medium > 0) {
        data.push({ name: "Medium", value: audit.issues.medium, color: "#eab308" });
      }
      if (audit.issues.low > 0) {
        data.push({ name: "Low", value: audit.issues.low, color: "#84cc16" });
      }
    }
  });
  
  // If no issues, add a "No issues" entry
  if (totalIssues === 0) {
    data.push({ name: "No issues", value: 1, color: "#22c55e" });
  }
  
  return data;
};
