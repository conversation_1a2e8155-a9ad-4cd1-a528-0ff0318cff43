/**
 * Token Correlation Mapper Component
 * Interactive network visualization showing token correlations and clusters
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Network, TrendingUp, TrendingDown, Info } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import {
  fetchCorrelationMatrix,
  getCorrelationInsights,
  CorrelationNode,
  CorrelationLink,
  CorrelationCluster
} from '@/services/api/correlationAnalysisService';
import NetworkGraph from '@/components/NetworkGraph';

interface TokenCorrelationMapperProps {
  className?: string;
}

export default function TokenCorrelationMapper({ className }: TokenCorrelationMapperProps) {
  const [timeframe, setTimeframe] = useState('30');
  const [selectedCluster, setSelectedCluster] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<'network' | 'clusters' | 'matrix'>('network');

  const { data: correlationData, isLoading, error, refetch } = useQuery({
    queryKey: ['correlation-matrix', timeframe],
    queryFn: () => fetchCorrelationMatrix(parseInt(timeframe)),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2
  });

  const { data: insights } = useQuery({
    queryKey: ['correlation-insights', correlationData?.clusters],
    queryFn: () => correlationData?.clusters ? getCorrelationInsights(correlationData.clusters) : Promise.resolve(''),
    enabled: !!correlationData?.clusters,
    staleTime: 30 * 60 * 1000 // 30 minutes
  });

  const getClusterColor = (clusterId: number): string => {
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
    return colors[clusterId % colors.length];
  };

  const getRiskColor = (riskScore: number): string => {
    if (riskScore > 0.7) return '#ef4444'; // High risk - red
    if (riskScore > 0.4) return '#f59e0b'; // Medium risk - amber
    return '#10b981'; // Low risk - green
  };

  const formatNetworkData = () => {
    if (!correlationData) return { nodes: [], links: [] };

    const nodes = correlationData.nodes.map(node => ({
      id: node.id,
      name: node.name,
      symbol: node.symbol,
      group: node.cluster.toString(),
      size: Math.log(node.marketCap) * 2,
      color: getClusterColor(node.cluster),
      riskScore: node.riskScore,
      rewardScore: node.rewardScore,
      price: node.price,
      change24h: node.change24h,
      value: node.marketCap // Adding required 'value' property
    }));

    const links = correlationData.links.map(link => ({
      source: link.source,
      target: link.target,
      value: Math.abs(link.correlation),
      color: link.type === 'positive' ? '#10b981' : '#ef4444',
      strength: link.strength,
      type: link.type // Adding required 'type' property
    }));

    return { nodes, links };
  };

  const renderClusterAnalysis = () => {
    if (!correlationData?.clusters) return null;

    return (
      <div className="space-y-4">
        {correlationData.clusters.map((cluster) => (
          <Card key={cluster.id} className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => setSelectedCluster(cluster.id)}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: getClusterColor(cluster.id) }}
                  />
                  <h4 className="font-semibold">{cluster.name}</h4>
                  <Badge variant={cluster.riskLevel === 'high' ? 'destructive' :
                                cluster.riskLevel === 'medium' ? 'default' : 'secondary'}>
                    {cluster.riskLevel} risk
                  </Badge>
                </div>
                <span className="text-sm text-muted-foreground">
                  {cluster.tokens.length} tokens
                </span>
              </div>

              <p className="text-sm text-muted-foreground mb-2">{cluster.description}</p>

              <div className="flex flex-wrap gap-1">
                {cluster.tokens.slice(0, 8).map((token) => (
                  <Badge key={token} variant="outline" className="text-xs">
                    {token}
                  </Badge>
                ))}
                {cluster.tokens.length > 8 && (
                  <Badge variant="outline" className="text-xs">
                    +{cluster.tokens.length - 8} more
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderCorrelationMatrix = () => {
    if (!correlationData?.nodes) return null;

    const topNodes = correlationData.nodes.slice(0, 10); // Show top 10 for readability

    return (
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr>
              <th className="text-left p-2">Asset</th>
              {topNodes.map(node => (
                <th key={node.id} className="text-center p-1 min-w-[60px]">
                  {node.symbol}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {topNodes.map(rowNode => (
              <tr key={rowNode.id}>
                <td className="p-2 font-medium">{rowNode.symbol}</td>
                {topNodes.map(colNode => {
                  const correlation = rowNode.id === colNode.id ? 1 :
                                    rowNode.correlations[colNode.id] || 0;
                  const intensity = Math.abs(correlation);
                  const isPositive = correlation > 0;

                  return (
                    <td key={colNode.id} className="p-1 text-center">
                      <div
                        className={`w-full h-8 rounded flex items-center justify-center text-xs font-medium ${
                          rowNode.id === colNode.id ? 'bg-gray-200 text-gray-800' :
                          isPositive ? 'text-white' : 'text-white'
                        }`}
                        style={{
                          backgroundColor: rowNode.id === colNode.id ? undefined :
                                         isPositive ? `rgba(16, 185, 129, ${intensity})` :
                                         `rgba(239, 68, 68, ${intensity})`
                        }}
                      >
                        {correlation.toFixed(2)}
                      </div>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Network className="h-5 w-5 text-primary" />
            <CardTitle>Token Correlation Mapper</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-muted-foreground">Analyzing token correlations...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    console.error('🔗 TokenCorrelationMapper error:', error);
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Network className="h-5 w-5 text-primary" />
            <CardTitle>Token Correlation Mapper</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-muted-foreground mb-4">Failed to load correlation data</p>
            <p className="text-xs text-red-500 mb-4">{error?.message || 'Unknown error'}</p>
            <Button onClick={() => refetch()} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const networkData = formatNetworkData();

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Network className="h-5 w-5 text-primary" />
            <CardTitle>Token Correlation Mapper</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">7 Days</SelectItem>
                <SelectItem value="30">30 Days</SelectItem>
                <SelectItem value="90">90 Days</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={() => refetch()} variant="outline" size="sm">
              Refresh
            </Button>
          </div>
        </div>

        {insights && (
          <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-blue-800 dark:text-blue-200">{insights}</p>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="network">Network View</TabsTrigger>
            <TabsTrigger value="clusters">Clusters</TabsTrigger>
            <TabsTrigger value="matrix">Matrix</TabsTrigger>
          </TabsList>

          <TabsContent value="network" className="mt-4">
            <div className="h-96">
              <NetworkGraph
                title="Token Correlation Network"
                description="Interactive visualization of token correlations"
                nodes={networkData.nodes}
                links={networkData.links}
                height={380}
                layoutOptions={{
                  enableSimulation: true,
                  chargeStrength: -200,
                  linkDistance: 100
                }}
              />
            </div>
          </TabsContent>

          <TabsContent value="clusters" className="mt-4">
            <div className="max-h-96 overflow-y-auto">
              {renderClusterAnalysis()}
            </div>
          </TabsContent>

          <TabsContent value="matrix" className="mt-4">
            <div className="max-h-96 overflow-y-auto">
              {renderCorrelationMatrix()}
            </div>
          </TabsContent>
        </Tabs>

        {correlationData && (
          <div className="mt-4 grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">
                {correlationData.nodes.length}
              </div>
              <div className="text-sm text-muted-foreground">Tokens Analyzed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {correlationData.links.length}
              </div>
              <div className="text-sm text-muted-foreground">Correlations Found</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {correlationData.clusters.length}
              </div>
              <div className="text-sm text-muted-foreground">Market Clusters</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
