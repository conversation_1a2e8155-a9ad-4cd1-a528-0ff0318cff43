# 🔧 Troubleshooting Guide

## Common Issues and Solutions

This guide provides solutions for common issues encountered in CryptoVision Pro, organized by category for quick resolution.

## 🚨 Enhanced Analytics Issues

### **Issue: Enhanced Analytics Components Not Loading**

**Symptoms**:
- Dashboard page loads but enhanced analytics section is missing
- Components show loading state indefinitely
- Error messages in browser console

**Diagnosis Steps**:
1. Check browser console for JavaScript errors
2. Verify API keys are properly configured
3. Check network tab for failed API requests
4. Confirm components are properly imported

**Solutions**:

**1. API Key Configuration**
```bash
# Check .env file
VITE_COINGECKO_API_KEY=your_actual_api_key
VITE_DEEPSEEK_API_KEY=your_actual_api_key

# Restart development server after .env changes
npm run dev
```

**2. Component Import Issues**
```typescript
// Verify imports in Dashboard.tsx
import TokenCorrelationMapper from "@/components/dashboard/TokenCorrelationMapper";
import EventImpactAnalyzer from "@/components/dashboard/EventImpactAnalyzer";
import VolatilityDashboard from "@/components/dashboard/VolatilityDashboard";
import TeamScreener from "@/components/dashboard/TeamScreener";
```

**3. React Query Configuration**
```typescript
// Ensure QueryClient is properly configured
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      retry: 2,
      refetchOnWindowFocus: false
    }
  }
});
```

### **Issue: API Rate Limiting**

**Symptoms**:
- 429 "Too Many Requests" errors
- Components showing cached/stale data
- Intermittent loading failures

**Solutions**:

**1. Implement Request Throttling**
```typescript
// Add delay between requests
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export async function fetchWithThrottle<T>(
  apiCall: () => Promise<T>,
  delayMs: number = 1000
): Promise<T> {
  await delay(delayMs);
  return apiCall();
}
```

**2. Increase Cache Duration**
```typescript
// Extend stale time to reduce API calls
const { data } = useQuery({
  queryKey: ['analytics-data'],
  queryFn: fetchAnalyticsData,
  staleTime: 15 * 60 * 1000, // 15 minutes instead of 10
  cacheTime: 30 * 60 * 1000  // 30 minutes
});
```

## 🔌 API Integration Issues

### **Issue: CoinGecko API Errors**

**Common Error Codes**:
- `429`: Rate limit exceeded
- `404`: Coin not found
- `500`: Server error
- `NETWORK_ERROR`: Connection issues

**Solutions**:

**1. Rate Limit Handling**
```typescript
const handleCoinGeckoError = (error: any) => {
  if (error.response?.status === 429) {
    // Use cached data
    return getCachedMarketData();
  }
  
  if (error.response?.status === 404) {
    // Coin not found, use fallback
    return getDefaultCoinData();
  }
  
  // Network error, retry with exponential backoff
  if (error.code === 'NETWORK_ERROR') {
    return retryWithBackoff(() => fetchMarketData());
  }
  
  throw error;
};
```

**2. Fallback Data Strategy**
```typescript
const fetchMarketDataWithFallback = async () => {
  try {
    return await coinGeckoApi.getMarketData();
  } catch (error) {
    console.warn('CoinGecko API failed, using cached data');
    const cached = localStorage.getItem('marketData');
    if (cached) {
      return JSON.parse(cached);
    }
    return getMockMarketData();
  }
};
```

### **Issue: DeepSeek AI API Errors**

**Common Issues**:
- Authentication failures
- Timeout errors
- Invalid response format

**Solutions**:

**1. Authentication Check**
```typescript
const validateDeepSeekAuth = async () => {
  try {
    const response = await deepSeekAxios.get('/models');
    return response.status === 200;
  } catch (error) {
    console.error('DeepSeek authentication failed:', error);
    return false;
  }
};
```

**2. Timeout Handling**
```typescript
const deepSeekAxios = axios.create({
  baseURL: 'https://api.deepseek.com',
  timeout: 45000, // Increase timeout for AI requests
  headers: {
    'Authorization': `Bearer ${process.env.VITE_DEEPSEEK_API_KEY}`
  }
});
```

## 🎨 UI/UX Issues

### **Issue: Components Not Rendering Properly**

**Symptoms**:
- Blank components or missing content
- Layout issues on different screen sizes
- Charts not displaying data

**Solutions**:

**1. Data Validation**
```typescript
const validateComponentData = (data: any) => {
  if (!data || typeof data !== 'object') {
    console.warn('Invalid component data:', data);
    return false;
  }
  
  if (Array.isArray(data) && data.length === 0) {
    console.warn('Empty data array');
    return false;
  }
  
  return true;
};
```

**2. Responsive Design Fixes**
```css
/* Ensure proper responsive behavior */
.analytics-component {
  @apply w-full min-h-[400px];
}

@media (max-width: 768px) {
  .analytics-component {
    @apply min-h-[300px];
  }
}
```

**3. Chart Rendering Issues**
```typescript
// Ensure chart container has proper dimensions
useEffect(() => {
  const container = chartRef.current;
  if (container && data) {
    const { width, height } = container.getBoundingClientRect();
    if (width > 0 && height > 0) {
      renderChart(data, { width, height });
    }
  }
}, [data]);
```

### **Issue: Dark/Light Theme Issues**

**Symptoms**:
- Components not respecting theme changes
- Inconsistent styling across components
- Poor contrast in certain themes

**Solutions**:

**1. Theme-Aware Styling**
```typescript
// Use CSS variables for theme-aware colors
const getThemeColors = () => ({
  background: 'hsl(var(--background))',
  foreground: 'hsl(var(--foreground))',
  primary: 'hsl(var(--primary))',
  secondary: 'hsl(var(--secondary))'
});
```

**2. Chart Theme Integration**
```typescript
const chartTheme = {
  light: {
    backgroundColor: '#ffffff',
    textColor: '#000000',
    gridColor: '#e5e5e5'
  },
  dark: {
    backgroundColor: '#1a1a1a',
    textColor: '#ffffff',
    gridColor: '#404040'
  }
};
```

## 🔄 Performance Issues

### **Issue: Slow Loading Times**

**Symptoms**:
- Components take long time to load
- Browser becomes unresponsive
- High memory usage

**Solutions**:

**1. Implement Virtual Scrolling**
```typescript
import { FixedSizeList as List } from 'react-window';

const VirtualizedList = ({ items }) => (
  <List
    height={400}
    itemCount={items.length}
    itemSize={50}
    itemData={items}
  >
    {({ index, style, data }) => (
      <div style={style}>
        {data[index]}
      </div>
    )}
  </List>
);
```

**2. Optimize Re-renders**
```typescript
// Memoize expensive calculations
const expensiveCalculation = useMemo(() => {
  return processLargeDataset(data);
}, [data]);

// Memoize components
const MemoizedComponent = memo(Component, (prev, next) => {
  return prev.data.id === next.data.id;
});
```

**3. Lazy Load Components**
```typescript
const LazyAnalyticsComponent = lazy(() => 
  import('./AnalyticsComponent').then(module => ({
    default: module.AnalyticsComponent
  }))
);
```

## 🗄️ Database Issues

### **Issue: Supabase Connection Problems**

**Symptoms**:
- Authentication failures
- Database query errors
- Session management issues

**Solutions**:

**1. Connection Validation**
```typescript
const validateSupabaseConnection = async () => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Supabase connection failed:', error);
    return false;
  }
};
```

**2. Session Management**
```typescript
// Proper session handling
useEffect(() => {
  const { data: { subscription } } = supabase.auth.onAuthStateChange(
    (event, session) => {
      if (event === 'SIGNED_OUT' || !session) {
        // Clear local state
        queryClient.clear();
        localStorage.clear();
      }
    }
  );

  return () => subscription.unsubscribe();
}, []);
```

## 🔍 Debugging Tools

### **Browser Developer Tools**

**1. Console Debugging**
```typescript
// Add debug logging
const DEBUG = process.env.NODE_ENV === 'development';

const debugLog = (message: string, data?: any) => {
  if (DEBUG) {
    console.log(`[DEBUG] ${message}`, data);
  }
};
```

**2. Network Monitoring**
- Open Network tab in DevTools
- Filter by XHR/Fetch to see API calls
- Check for failed requests (red status codes)
- Verify request headers and payloads

**3. React Developer Tools**
- Install React DevTools extension
- Monitor component state and props
- Check for unnecessary re-renders
- Profile component performance

### **Application Monitoring**

**1. Error Boundary Implementation**
```typescript
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    // Send to error reporting service
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}
```

**2. Performance Monitoring**
```typescript
// Monitor API response times
const monitorApiPerformance = (apiName: string, startTime: number) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  if (duration > 5000) {
    console.warn(`Slow API response: ${apiName} took ${duration}ms`);
  }
};
```

## 📞 Getting Help

### **When to Seek Additional Support**

1. **Persistent API failures** across multiple services
2. **Database corruption** or data inconsistency
3. **Security vulnerabilities** discovered
4. **Performance issues** that can't be resolved with optimization

### **Information to Provide**

When reporting issues, include:
- Browser version and operating system
- Steps to reproduce the issue
- Console error messages
- Network request details
- Component state when error occurred

### **Emergency Procedures**

**1. Service Outage**
- Switch to cached data mode
- Display maintenance message
- Disable non-essential features

**2. Data Corruption**
- Stop write operations
- Backup current state
- Restore from last known good backup
- Investigate root cause

This troubleshooting guide covers the most common issues and provides systematic approaches to resolution. Regular monitoring and proactive maintenance help prevent many of these issues from occurring.
