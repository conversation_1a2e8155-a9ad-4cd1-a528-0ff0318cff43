
/**
 * Calculate bridge fees
 */
export const calculateFees = (
  bridge: any, 
  amount: number, 
  sourceChain: string
): { percentage: number; fixed: number; total: number } => {
  if (!bridge) {
    return { percentage: 0, fixed: 0, total: 0 };
  }
  
  const percentageFee = amount * bridge.fees.percentage;
  const fixedFee = bridge.fees.fixed[sourceChain.toLowerCase()] || 0;
  
  return {
    percentage: percentageFee,
    fixed: fixedFee,
    total: percentageFee + fixedFee
  };
};

/**
 * Format currency values
 */
export const formatCurrency = (value: number) => {
  if (isNaN(value)) return "$0.00";
  
  if (value >= 1000000000) {
    return `$${(value / 1000000000).toFixed(2)}B`;
  } else if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(2)}M`;
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(2)}K`;
  } else {
    return `$${value.toFixed(2)}`;
  }
};
