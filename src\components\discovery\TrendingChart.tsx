
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON>s, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Cell } from "recharts";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface TrendingChartProps {
  data: any[];
  isLoading: boolean;
}

export default function TrendingChart({ data, isLoading }: TrendingChartProps) {
  if (isLoading) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="animate-pulse w-full h-full bg-gray-200 rounded"></div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center">
        <p className="text-muted-foreground">No trending data available</p>
      </div>
    );
  }

  const getBarColor = (momentum: number) => {
    return momentum >= 0 ? "#22c55e" : "#ef4444";
  };

  return (
    <div className="space-y-4">
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="name" 
              fontSize={12}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis fontSize={12} />
            <Tooltip 
              formatter={(value: any, name: string) => [
                `${typeof value === 'number' && value > 0 ? '+' : ''}${Number(value).toFixed(2)}%`,
                'Momentum'
              ]}
              labelFormatter={(label) => `Token: ${label}`}
            />
            <Bar 
              dataKey="momentum" 
              radius={[4, 4, 0, 0]}
            >
              {data.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={getBarColor(entry.momentum)} 
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
      
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-muted-foreground">Top Performer</span>
          <p className="font-medium">
            {data.reduce((prev, current) => 
              (prev.momentum > current.momentum) ? prev : current
            ).name}
          </p>
        </div>
        <div>
          <span className="text-muted-foreground">Avg Momentum</span>
          <p className="font-medium">
            {(data.reduce((sum, item) => sum + item.momentum, 0) / data.length).toFixed(1)}%
          </p>
        </div>
      </div>
    </div>
  );
}
