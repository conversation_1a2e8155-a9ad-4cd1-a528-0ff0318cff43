/**
 * API Diagnostic Utility
 * Comprehensive testing and debugging for API connectivity issues
 */

import axios from 'axios';
import { coinGeckoAxios } from '@/services/api/coinGeckoClient';

export interface ApiDiagnosticResult {
  service: string;
  endpoint: string;
  success: boolean;
  status?: number;
  error?: string;
  responseTime?: number;
  headers?: Record<string, string>;
  data?: any;
}

export class ApiDiagnostic {
  /**
   * Test CoinGecko API with multiple endpoints and methods
   */
  static async testCoinGeckoAPI(): Promise<ApiDiagnosticResult[]> {
    console.log('🔍 Running CoinGecko API Diagnostics...');
    
    const apiKey = import.meta.env.VITE_COINGECKO_API_KEY;
    console.log('🔑 API Key configured:', !!(apiKey && apiKey !== 'demo_key'));
    console.log('🔑 API Key format:', apiKey ? `${apiKey.substring(0, 5)}...` : 'Not configured');
    
    const tests: Array<{
      name: string;
      endpoint: string;
      method: 'GET' | 'POST';
      params?: any;
      useAxios?: boolean;
    }> = [
      {
        name: 'Basic Ping Test (Fetch)',
        endpoint: 'https://api.coingecko.com/api/v3/ping',
        method: 'GET',
        useAxios: false
      },
      {
        name: 'Basic Ping Test (Axios)',
        endpoint: '/ping',
        method: 'GET',
        useAxios: true
      },
      {
        name: 'Simple Price Test (Fetch)',
        endpoint: 'https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd',
        method: 'GET',
        useAxios: false
      },
      {
        name: 'Simple Price Test (Axios)',
        endpoint: '/simple/price',
        method: 'GET',
        params: { ids: 'bitcoin', vs_currencies: 'usd' },
        useAxios: true
      },
      {
        name: 'Global Market Data',
        endpoint: '/global',
        method: 'GET',
        useAxios: true
      }
    ];

    const results: ApiDiagnosticResult[] = [];

    for (const test of tests) {
      const startTime = Date.now();
      const result: ApiDiagnosticResult = {
        service: 'CoinGecko',
        endpoint: test.endpoint,
        success: false
      };

      try {
        let response;
        
        if (test.useAxios) {
          // Use configured axios instance
          response = await coinGeckoAxios.request({
            method: test.method,
            url: test.endpoint,
            params: test.params
          });
        } else {
          // Use fetch for direct testing
          const url = test.params ? 
            `${test.endpoint}?${new URLSearchParams(test.params).toString()}` : 
            test.endpoint;
            
          response = await fetch(url, {
            method: test.method,
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              ...(apiKey && apiKey !== 'demo_key' ? {
                'x-cg-demo-api-key': apiKey
              } : {})
            }
          });
          
          // Convert fetch response to axios-like format
          const data = await response.json();
          response = {
            status: response.status,
            data: data,
            headers: Object.fromEntries(response.headers.entries())
          };
        }

        result.success = response.status >= 200 && response.status < 300;
        result.status = response.status;
        result.responseTime = Date.now() - startTime;
        result.headers = response.headers;
        result.data = response.data;

        console.log(`✅ ${test.name}: Success (${response.status}) in ${result.responseTime}ms`);
        
      } catch (error: any) {
        result.success = false;
        result.responseTime = Date.now() - startTime;
        result.error = error.message;
        result.status = error.response?.status;
        
        console.log(`❌ ${test.name}: Failed - ${error.message}`);
        
        if (error.response) {
          console.log(`   Status: ${error.response.status}`);
          console.log(`   Data:`, error.response.data);
        }
      }

      results.push(result);
    }

    return results;
  }

  /**
   * Test network connectivity
   */
  static async testNetworkConnectivity(): Promise<ApiDiagnosticResult[]> {
    console.log('🌐 Testing Network Connectivity...');
    
    const tests = [
      'https://httpbin.org/get',
      'https://api.github.com',
      'https://jsonplaceholder.typicode.com/posts/1'
    ];

    const results: ApiDiagnosticResult[] = [];

    for (const url of tests) {
      const startTime = Date.now();
      const result: ApiDiagnosticResult = {
        service: 'Network Test',
        endpoint: url,
        success: false
      };

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: { 'Accept': 'application/json' }
        });

        result.success = response.ok;
        result.status = response.status;
        result.responseTime = Date.now() - startTime;

        console.log(`✅ ${url}: ${response.status} in ${result.responseTime}ms`);
        
      } catch (error: any) {
        result.success = false;
        result.responseTime = Date.now() - startTime;
        result.error = error.message;
        
        console.log(`❌ ${url}: ${error.message}`);
      }

      results.push(result);
    }

    return results;
  }

  /**
   * Run complete diagnostic suite
   */
  static async runCompleteDiagnostic(): Promise<{
    network: ApiDiagnosticResult[];
    coinGecko: ApiDiagnosticResult[];
    summary: {
      networkConnectivity: boolean;
      coinGeckoConnectivity: boolean;
      recommendations: string[];
    };
  }> {
    console.log('🔬 Running Complete API Diagnostic Suite...');
    
    const network = await this.testNetworkConnectivity();
    const coinGecko = await this.testCoinGeckoAPI();
    
    const networkConnectivity = network.some(r => r.success);
    const coinGeckoConnectivity = coinGecko.some(r => r.success);
    
    const recommendations: string[] = [];
    
    if (!networkConnectivity) {
      recommendations.push('Check internet connection and firewall settings');
    }
    
    if (!coinGeckoConnectivity) {
      if (networkConnectivity) {
        recommendations.push('CoinGecko API key may be invalid or expired');
        recommendations.push('Check API key format (should start with CG-)');
        recommendations.push('Verify API key permissions and rate limits');
      } else {
        recommendations.push('Network connectivity issues preventing API access');
      }
    }
    
    if (coinGeckoConnectivity) {
      recommendations.push('CoinGecko API is working correctly');
    }
    
    const summary = {
      networkConnectivity,
      coinGeckoConnectivity,
      recommendations
    };
    
    console.log('📊 Diagnostic Summary:', summary);
    
    return {
      network,
      coinGecko,
      summary
    };
  }
}

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).apiDiagnostic = ApiDiagnostic;
}
