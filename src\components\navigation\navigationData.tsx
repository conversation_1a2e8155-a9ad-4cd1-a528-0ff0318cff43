
import { 
  BarChart3, 
  <PERSON><PERSON>dingUp, 
  Wallet, 
  Brain, 
  Shield, 
  Newspaper, 
  GraduationCap, 
  ChartCandlestick,
  Activity,
  AlertTriangle,
  Star,
  Eye,
  Layers,
  Sparkles,
  Search,
  Settings
} from "lucide-react";

export interface NavigationItem {
  title: string;
  href: string;
  icon: React.ComponentType<any>;
  description: string;
  adminOnly?: boolean;
}

export interface NavigationSection {
  title: string;
  items: NavigationItem[];
}

export const navigationSections: NavigationSection[] = [
  {
    title: "Core Analytics",
    items: [
      {
        title: "Dashboard",
        href: "/dashboard",
        icon: BarChart3,
        description: "Main overview and key metrics"
      },
      {
        title: "Market Insights",
        href: "/market-insights",
        icon: TrendingUp,
        description: "Real-time market analysis"
      },
      {
        title: "Portfolio",
        href: "/portfolio",
        icon: Wallet,
        description: "Track your crypto holdings"
      }
    ]
  },
  {
    title: "Advanced Analytics",
    items: [
      {
        title: "AI Insights",
        href: "/ai-insights",
        icon: Brain,
        description: "AI-powered market analysis"
      },
      {
        title: "Smart Money",
        href: "/smart-money",
        icon: Eye,
        description: "Track institutional movements"
      },
      {
        title: "On-Chain Analytics",
        href: "/onchain-analytics",
        icon: Activity,
        description: "Blockchain data analysis"
      },
      {
        title: "DeFi Opportunities",
        href: "/defi-opportunities",
        icon: Layers,
        description: "Yield farming and DeFi protocols"
      }
    ]
  },
  {
    title: "Research Tools",
    items: [
      {
        title: "Token Scam Detector",
        href: "/token-scam-detector",
        icon: Shield,
        description: "Identify risky tokens"
      },
      {
        title: "Coin Discovery",
        href: "/coin-discovery",
        icon: Search,
        description: "Find emerging opportunities"
      },
      {
        title: "Fundamental Analysis",
        href: "/fundamental-analysis",
        icon: ChartCandlestick,
        description: "Deep dive into project fundamentals"
      },
      {
        title: "News & Sentiment",
        href: "/news-sentiment",
        icon: Newspaper,
        description: "Market sentiment analysis"
      }
    ]
  },
  {
    title: "Advanced Features",
    items: [
      {
        title: "Anomaly Detection",
        href: "/anomalies",
        icon: AlertTriangle,
        description: "Detect unusual market patterns"
      },
      {
        title: "Ratings & Scores",
        href: "/ratings",
        icon: Star,
        description: "Comprehensive asset ratings"
      },
      {
        title: "Forecasting",
        href: "/forecasting",
        icon: Sparkles,
        description: "Price prediction models"
      },
      {
        title: "Visualizations",
        href: "/advanced-visualizations",
        icon: BarChart3,
        description: "Interactive charts and graphs"
      }
    ]
  },
  {
    title: "Learning & Support",
    items: [
      {
        title: "Education Hub",
        href: "/education",
        icon: GraduationCap,
        description: "Learn about crypto and DeFi"
      }
    ]
  },
  {
    title: "Administration",
    items: [
      {
        title: "Admin Dashboard",
        href: "/admin",
        icon: Settings,
        description: "System administration",
        adminOnly: true
      }
    ]
  }
];
