
import React from "react";
import { Badge } from "@/components/ui/badge";

interface SecurityIssuesBadgeProps {
  audit: {
    issues?: {
      critical: number;
      high: number;
      medium: number;
      low: number;
    }
  } | null;
}

const SecurityIssuesBadge = ({ audit }: SecurityIssuesBadgeProps) => {
  if (!audit || !audit.issues) return <Badge variant="outline" className="bg-green-100 text-green-800">N/A</Badge>;
  
  const { critical, high, medium, low } = audit.issues;
  
  if (critical === 0 && high === 0 && medium === 0 && low === 0) {
    return <Badge variant="outline" className="bg-green-100 text-green-800">None</Badge>;
  }
  
  return (
    <div className="flex gap-2">
      {critical > 0 && <Badge variant="outline" className="bg-red-100 text-red-800">C: {critical}</Badge>}
      {high > 0 && <Badge variant="outline" className="bg-orange-100 text-orange-800">H: {high}</Badge>}
      {medium > 0 && <Badge variant="outline" className="bg-yellow-100 text-yellow-800">M: {medium}</Badge>}
      {low > 0 && <Badge variant="outline" className="bg-green-100 text-green-800">L: {low}</Badge>}
    </div>
  );
};

export default SecurityIssuesBadge;
