
import React, { useMemo } from "react";
import { Card, CardHeader, CardTitle, CardContent, CardDescription, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartTooltip, ResponsiveContainer, Cell } from "recharts";
import { Network, Clock, AlertTriangle, CheckCircle } from "lucide-react";
import SecurityBadge from "./SecurityBadge";
import { calculateFees, formatCurrency } from "./bridgeUtils";

interface BridgeDetailsProps {
  isLoading: boolean;
  sourceChain: string;
  targetChain: string;
  amount: string;
  bridges: any[];
  selectedBridge: string | null;
}

const BridgeDetails = ({
  isLoading,
  sourceChain,
  targetChain,
  amount,
  bridges,
  selectedBridge,
}: BridgeDetailsProps) => {
  
  // Filter and sort bridges
  const filteredBridges = useMemo(() => {
    if (!bridges || bridges.length === 0) return [];
    
    return bridges.filter(bridge => 
      bridge.supportedNetworks.includes(sourceChain) &&
      bridge.supportedNetworks.includes(targetChain) &&
      sourceChain !== targetChain
    );
  }, [bridges, sourceChain, targetChain]);
  
  // Currently selected bridge data
  const currentBridge = useMemo(() => {
    if (!selectedBridge && filteredBridges.length > 0) {
      return filteredBridges[0];
    }
    return filteredBridges.find(b => b.id === selectedBridge) || filteredBridges[0];
  }, [filteredBridges, selectedBridge]);
  
  if (!currentBridge || isLoading) {
    return null;
  }
  
  const fees = calculateFees(currentBridge, parseFloat(amount), sourceChain);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{currentBridge.name}</CardTitle>
        <CardDescription>
          Bridge details and security information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-5">
        <div>
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-medium">Transfer Details</h3>
            <Badge>{sourceChain} → {targetChain}</Badge>
          </div>
          
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 rounded-md bg-secondary/50">
                <p className="text-xs text-muted-foreground">Est. Transfer Time</p>
                <p className="text-lg font-medium flex items-center">
                  <Clock className="h-4 w-4 mr-1 inline" />
                  {currentBridge.speed[sourceChain.toLowerCase()] || "N/A"} min
                </p>
              </div>
              <div className="p-3 rounded-md bg-secondary/50">
                <p className="text-xs text-muted-foreground">Security Score</p>
                <p className="text-lg font-medium">
                  <SecurityBadge score={currentBridge.securityScore} showText={true} />
                </p>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium mb-2">Fee Breakdown</h4>
              <div className="bg-secondary/30 p-3 rounded-md space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Fixed Fee:</span>
                  <span>${currentBridge.fees.fixed[sourceChain.toLowerCase()]?.toFixed(2) || "0.00"}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Percentage Fee:</span>
                  <span>{(currentBridge.fees.percentage * 100).toFixed(2)}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Amount:</span>
                  <span>${parseFloat(amount).toFixed(2)}</span>
                </div>
                <div className="border-t pt-2 flex justify-between font-medium">
                  <span>Total Fee:</span>
                  <span>
                    ${fees.total.toFixed(2)}
                  </span>
                </div>
                <div className="border-t pt-2 flex justify-between font-medium">
                  <span>You Receive:</span>
                  <span className="text-green-600 dark:text-green-400">
                    ${(parseFloat(amount) - fees.total).toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <Tabs defaultValue="security">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="networks">Networks</TabsTrigger>
          </TabsList>
          
          <TabsContent value="security" className="pt-4">
            <BridgeSecurityInfo bridge={currentBridge} />
          </TabsContent>
          
          <TabsContent value="networks" className="pt-4">
            <BridgeNetworkInfo bridge={currentBridge} sourceChain={sourceChain} />
          </TabsContent>
        </Tabs>
        
        <Button className="w-full">
          Use This Bridge
        </Button>
      </CardContent>
    </Card>
  );
};

// Security Info Sub-component
const BridgeSecurityInfo = ({ bridge }: { bridge: any }) => {
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <span className="text-sm">Audited</span>
        <Badge variant={bridge.securityDetails.audited ? "success" : "destructive"}>
          {bridge.securityDetails.audited ? "Yes" : "No"}
        </Badge>
      </div>
      
      {bridge.securityDetails.audited && (
        <div>
          <p className="text-sm font-medium mb-1">Audit Firms:</p>
          <div className="flex flex-wrap gap-1">
            {bridge.securityDetails.auditors.map((auditor: string) => (
              <Badge key={auditor} variant="outline">{auditor}</Badge>
            ))}
          </div>
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <span className="text-sm">TVL Locked</span>
        <span className="font-medium">{formatCurrency(bridge.securityDetails.tvlLocked)}</span>
      </div>
      
      {bridge.securityDetails.incidentHistory && 
       bridge.securityDetails.incidentHistory.length > 0 && (
        <div>
          <p className="text-sm font-medium mb-1">Security Incidents:</p>
          <div className="space-y-2">
            {bridge.securityDetails.incidentHistory.map((incident: any, idx: number) => (
              <div key={idx} className="text-sm bg-red-50 dark:bg-red-900/10 p-2 rounded border border-red-100 dark:border-red-900/20">
                <p className="flex items-center text-red-700 dark:text-red-400">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  {incident.date}: {incident.description}
                </p>
                <p className="text-xs text-red-600 dark:text-red-500 mt-1">
                  Severity: {incident.severity}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {(!bridge.securityDetails.incidentHistory || 
        bridge.securityDetails.incidentHistory.length === 0) && (
        <div className="text-sm bg-green-50 dark:bg-green-900/10 p-2 rounded border border-green-100 dark:border-green-900/20">
          <p className="flex items-center text-green-700 dark:text-green-400">
            <CheckCircle className="h-3 w-3 mr-1" />
            No security incidents reported
          </p>
        </div>
      )}
    </div>
  );
};

// Network Info Sub-component
const BridgeNetworkInfo = ({ bridge, sourceChain }: { bridge: any, sourceChain: string }) => {
  return (
    <div className="space-y-3">
      <p className="text-sm font-medium">Supported Networks:</p>
      <div className="flex flex-wrap gap-1">
        {bridge.supportedNetworks.map((network: string) => (
          <Badge key={network} variant="outline" className="flex items-center gap-1">
            <Network className="h-3 w-3" /> {network}
          </Badge>
        ))}
      </div>
      
      <div className="mt-3">
        <p className="text-sm font-medium mb-1">Network Speed Comparison:</p>
        <div className="h-[150px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={Object.entries(bridge.speed)
                .filter(([network, _]) => bridge.supportedNetworks.includes(network.charAt(0).toUpperCase() + network.slice(1)))
                .map(([network, time]) => ({
                  network: network.charAt(0).toUpperCase() + network.slice(1),
                  time
                }))
              }
              margin={{ top: 5, right: 5, left: 5, bottom: 25 }}
            >
              <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
              <XAxis 
                dataKey="network" 
                tick={{ fontSize: 12 }}
                dy={10}
                angle={-45}
                textAnchor="end"
              />
              <YAxis
                label={{ value: 'Minutes', angle: -90, position: 'insideLeft', offset: 0, fontSize: 12 }}
                tick={{ fontSize: 12 }}
              />
              <RechartTooltip formatter={(value: any) => [`${value} minutes`, 'Transfer Time']} />
              <Bar dataKey="time" fill="#8884d8">
                {Object.entries(bridge.speed).map((_, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={index % 2 === 0 ? '#8884d8' : '#82ca9d'} 
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
      
      <div className="mt-3">
        <p className="text-sm font-medium">24h Bridge Volume:</p>
        <p className="text-lg font-medium">{formatCurrency(bridge.volume24h)}</p>
      </div>
    </div>
  );
};

export default BridgeDetails;
