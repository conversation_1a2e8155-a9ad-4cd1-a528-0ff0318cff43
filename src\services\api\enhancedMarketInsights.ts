
import { coinGeckoAxios, handleApiError, cacheResponse } from "./coinGeckoClient";

// Type definitions for enhanced market insights
export interface MarketSentiment {
  fearGreedIndex: number;
  classification: string;
  trend: string;
  lastUpdated: string;
}

export interface MarketMetrics {
  totalMarketCap: number;
  marketCapChange: number;
  marketCapChange24h: number;
  totalVolume: number;
  btcDominance: number;
  ethDominance: number;
  gainers: any[];
  losers: any[];
  activeCoins: number;
  markets: number;
  lastUpdated: string;
}

export interface SectorPerformance {
  sector: string;
  change24h: number;
  marketCap: number;
  volume: number;
  topCoins: string[];
}

export interface MarketNews {
  id: string;
  title: string;
  description: string;
  url: string;
  source: string;
  publishedAt: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  impact: 'high' | 'medium' | 'low';
}

export interface LiquidityMetrics {
  totalLiquidity: number;
  liquidityChange24h: number;
  topPairs: Array<{
    pair: string;
    liquidity: number;
    volume24h: number;
    exchange: string;
  }>;
  liquidityDistribution: Record<string, number>;
}

export interface VolatilityData {
  marketVolatility: number;
  volatilityChange24h: number;
  highVolatilityCoins: Array<{
    symbol: string;
    volatility: number;
    priceChange24h: number;
  }>;
  volatilityTrend: 'increasing' | 'decreasing' | 'stable';
}

// Enhanced market insights with better error handling and type safety
export const fetchEnhancedMarketMetrics = async () => {
  try {
    const [globalResponse, marketsResponse] = await Promise.all([
      coinGeckoAxios.get("/global").catch(() => ({ data: { data: {} } })),
      coinGeckoAxios.get("/coins/markets", {
        params: {
          vs_currency: 'usd',
          order: 'market_cap_desc',
          per_page: 250,
          page: 1,
          sparkline: false,
          price_change_percentage: '1h,24h,7d'
        }
      }).catch(() => ({ data: [] }))
    ]);

    const globalData = globalResponse.data?.data || {};
    const marketData = Array.isArray(marketsResponse.data) ? marketsResponse.data : [];

    // Calculate metrics with proper fallbacks
    const totalMarketCap = globalData.total_market_cap?.usd || 0;
    const marketCapChange = globalData.market_cap_change_percentage_24h_usd || 0;
    const totalVolume = globalData.total_volume?.usd || 0;
    
    // Process market data safely
    const gainers = marketData
      .filter(coin => coin && typeof coin.price_change_percentage_24h === 'number' && coin.price_change_percentage_24h > 0)
      .sort((a, b) => (b.price_change_percentage_24h || 0) - (a.price_change_percentage_24h || 0))
      .slice(0, 10);
      
    const losers = marketData
      .filter(coin => coin && typeof coin.price_change_percentage_24h === 'number' && coin.price_change_percentage_24h < 0)
      .sort((a, b) => (a.price_change_percentage_24h || 0) - (b.price_change_percentage_24h || 0))
      .slice(0, 10);

    const result = {
      totalMarketCap,
      marketCapChange,
      marketCapChange24h: marketCapChange, // Add the required property
      totalVolume,
      btcDominance: globalData.market_cap_percentage?.btc || 0,
      ethDominance: globalData.market_cap_percentage?.eth || 0,
      gainers,
      losers,
      activeCoins: globalData.active_cryptocurrencies || 0,
      markets: globalData.markets || 0,
      lastUpdated: new Date().toISOString()
    };

    cacheResponse("enhanced_market_metrics", result, 300);
    return result;
  } catch (error) {
    return handleApiError(error, "enhanced_market_metrics", {
      totalMarketCap: 0,
      marketCapChange: 0,
      marketCapChange24h: 0,
      totalVolume: 0,
      btcDominance: 0,
      ethDominance: 0,
      gainers: [],
      losers: [],
      activeCoins: 0,
      markets: 0,
      lastUpdated: new Date().toISOString()
    });
  }
};

// Fetch market sentiment with proper error handling
export const fetchMarketSentiment = async () => {
  try {
    // Mock sentiment data with realistic values
    const sentimentData = {
      fearGreedIndex: Math.floor(Math.random() * 100),
      classification: 'Neutral',
      trend: Math.random() > 0.5 ? 'up' : 'down',
      lastUpdated: new Date().toISOString()
    };

    const fearGreedValue = sentimentData.fearGreedIndex;
    if (fearGreedValue <= 25) sentimentData.classification = 'Extreme Fear';
    else if (fearGreedValue <= 45) sentimentData.classification = 'Fear';
    else if (fearGreedValue <= 55) sentimentData.classification = 'Neutral';
    else if (fearGreedValue <= 75) sentimentData.classification = 'Greed';
    else sentimentData.classification = 'Extreme Greed';

    cacheResponse("market_sentiment", sentimentData, 300);
    return sentimentData;
  } catch (error) {
    return handleApiError(error, "market_sentiment", {
      fearGreedIndex: 50,
      classification: 'Neutral',
      trend: 'neutral',
      lastUpdated: new Date().toISOString()
    });
  }
};

// Fetch Fear & Greed Index
export const fetchFearGreedIndex = async () => {
  try {
    // Simulate Fear & Greed Index data
    const fearGreedData = {
      value: Math.floor(Math.random() * 100),
      value_classification: 'Neutral',
      timestamp: Date.now(),
      time_until_update: 3600000 // 1 hour in milliseconds
    };

    const value = fearGreedData.value;
    if (value <= 25) fearGreedData.value_classification = 'Extreme Fear';
    else if (value <= 45) fearGreedData.value_classification = 'Fear';
    else if (value <= 55) fearGreedData.value_classification = 'Neutral';
    else if (value <= 75) fearGreedData.value_classification = 'Greed';
    else fearGreedData.value_classification = 'Extreme Greed';

    cacheResponse("fear_greed_index", fearGreedData, 300);
    return fearGreedData;
  } catch (error) {
    return handleApiError(error, "fear_greed_index", {
      value: 50,
      value_classification: 'Neutral',
      timestamp: Date.now(),
      time_until_update: 3600000
    });
  }
};

// Fetch TVL data
export const fetchTvlData = async () => {
  try {
    // Mock TVL data - in real implementation would use DeFiLlama or similar
    const tvlData = {
      total_tvl: 45000000000 + Math.random() * 10000000000,
      change_24h: (Math.random() - 0.5) * 10,
      top_protocols: [
        { name: 'Aave', tvl: 12000000000, change_24h: Math.random() * 5 },
        { name: 'Compound', tvl: 8500000000, change_24h: Math.random() * 5 },
        { name: 'Uniswap', tvl: 6200000000, change_24h: Math.random() * 5 },
        { name: 'MakerDAO', tvl: 5800000000, change_24h: Math.random() * 5 },
        { name: 'Curve', tvl: 4100000000, change_24h: Math.random() * 5 }
      ]
    };

    cacheResponse("tvl_data", tvlData, 300);
    return tvlData;
  } catch (error) {
    return handleApiError(error, "tvl_data", {
      total_tvl: 45000000000,
      change_24h: 0,
      top_protocols: []
    });
  }
};

// Fetch recently added cryptocurrencies
export const fetchRecentlyAdded = async (limit = 10) => {
  try {
    const response = await coinGeckoAxios.get("/coins/markets", {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: 100,
        page: 1,
        sparkline: false
      }
    });

    if (!Array.isArray(response.data)) {
      throw new Error('Invalid response format');
    }

    // Filter for newer/smaller market cap coins as proxy for "recently added"
    const recentCoins = response.data
      .filter(coin => coin && coin.market_cap && coin.market_cap < 100000000) // Under $100M market cap
      .slice(0, limit);

    cacheResponse("recently_added", recentCoins, 300);
    return recentCoins;
  } catch (error) {
    return handleApiError(error, "recently_added", []);
  }
};

// Fetch sector performance data
export const fetchSectorPerformance = async (): Promise<SectorPerformance[]> => {
  try {
    // Mock sector performance data - in real implementation would use specialized APIs
    const sectors: SectorPerformance[] = [
      {
        sector: 'DeFi',
        change24h: Math.random() * 10 - 5,
        marketCap: 45000000000 + Math.random() * 10000000000,
        volume: 2500000000 + Math.random() * 1000000000,
        topCoins: ['UNI', 'AAVE', 'COMP', 'MKR', 'SNX']
      },
      {
        sector: 'Layer 1',
        change24h: Math.random() * 10 - 5,
        marketCap: 120000000000 + Math.random() * 20000000000,
        volume: 8500000000 + Math.random() * 2000000000,
        topCoins: ['ETH', 'BNB', 'ADA', 'SOL', 'DOT']
      },
      {
        sector: 'Gaming',
        change24h: Math.random() * 15 - 7.5,
        marketCap: 8500000000 + Math.random() * 3000000000,
        volume: 450000000 + Math.random() * 200000000,
        topCoins: ['AXS', 'SAND', 'MANA', 'ENJ', 'GALA']
      },
      {
        sector: 'NFT',
        change24h: Math.random() * 20 - 10,
        marketCap: 3200000000 + Math.random() * 1500000000,
        volume: 180000000 + Math.random() * 100000000,
        topCoins: ['FLOW', 'CHZ', 'THETA', 'ENJ', 'WAXP']
      },
      {
        sector: 'Meme',
        change24h: Math.random() * 25 - 12.5,
        marketCap: 15000000000 + Math.random() * 8000000000,
        volume: 1200000000 + Math.random() * 800000000,
        topCoins: ['DOGE', 'SHIB', 'PEPE', 'FLOKI', 'BONK']
      }
    ];

    cacheResponse("sector_performance", sectors, 300);
    return sectors;
  } catch (error) {
    return handleApiError(error, "sector_performance", []);
  }
};

// Fetch market news
export const fetchMarketNews = async (): Promise<MarketNews[]> => {
  try {
    // Mock news data - in real implementation would use news APIs
    const newsItems: MarketNews[] = [
      {
        id: '1',
        title: 'Bitcoin Reaches New All-Time High Amid Institutional Adoption',
        description: 'Major corporations continue to add Bitcoin to their treasury reserves, driving price to unprecedented levels.',
        url: '#',
        source: 'CryptoNews',
        publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        sentiment: 'positive',
        impact: 'high'
      },
      {
        id: '2',
        title: 'Ethereum 2.0 Staking Rewards Attract More Validators',
        description: 'The number of ETH validators continues to grow as staking rewards remain attractive.',
        url: '#',
        source: 'DeFi Pulse',
        publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        sentiment: 'positive',
        impact: 'medium'
      },
      {
        id: '3',
        title: 'Regulatory Clarity Boosts DeFi Sector Performance',
        description: 'New regulatory guidelines provide clearer framework for DeFi protocols.',
        url: '#',
        source: 'Blockchain Today',
        publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        sentiment: 'positive',
        impact: 'medium'
      },
      {
        id: '4',
        title: 'Market Volatility Increases Amid Global Economic Uncertainty',
        description: 'Crypto markets show increased volatility as traditional markets face headwinds.',
        url: '#',
        source: 'Market Watch',
        publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        sentiment: 'negative',
        impact: 'medium'
      },
      {
        id: '5',
        title: 'New Layer 2 Solutions Show Promise for Scaling',
        description: 'Several new Layer 2 protocols demonstrate significant improvements in transaction throughput.',
        url: '#',
        source: 'Tech Crypto',
        publishedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
        sentiment: 'positive',
        impact: 'low'
      }
    ];

    cacheResponse("market_news", newsItems, 300);
    return newsItems;
  } catch (error) {
    return handleApiError(error, "market_news", []);
  }
};

// Fetch liquidity metrics
export const fetchLiquidityMetrics = async (): Promise<LiquidityMetrics> => {
  try {
    // Mock liquidity data - in real implementation would use DEX aggregator APIs
    const liquidityData: LiquidityMetrics = {
      totalLiquidity: 12500000000 + Math.random() * 2500000000,
      liquidityChange24h: (Math.random() - 0.5) * 10,
      topPairs: [
        {
          pair: 'ETH/USDC',
          liquidity: 850000000 + Math.random() * 150000000,
          volume24h: 125000000 + Math.random() * 25000000,
          exchange: 'Uniswap V3'
        },
        {
          pair: 'BTC/ETH',
          liquidity: 620000000 + Math.random() * 120000000,
          volume24h: 95000000 + Math.random() * 20000000,
          exchange: 'Uniswap V3'
        },
        {
          pair: 'USDC/USDT',
          liquidity: 580000000 + Math.random() * 100000000,
          volume24h: 180000000 + Math.random() * 40000000,
          exchange: 'Curve'
        },
        {
          pair: 'ETH/WBTC',
          liquidity: 420000000 + Math.random() * 80000000,
          volume24h: 65000000 + Math.random() * 15000000,
          exchange: 'SushiSwap'
        },
        {
          pair: 'BNB/BUSD',
          liquidity: 380000000 + Math.random() * 70000000,
          volume24h: 85000000 + Math.random() * 18000000,
          exchange: 'PancakeSwap'
        }
      ],
      liquidityDistribution: {
        'Uniswap V3': 35.2,
        'Curve': 18.7,
        'SushiSwap': 12.4,
        'PancakeSwap': 11.8,
        'Balancer': 8.3,
        'Others': 13.6
      }
    };

    cacheResponse("liquidity_metrics", liquidityData, 300);
    return liquidityData;
  } catch (error) {
    return handleApiError(error, "liquidity_metrics", {
      totalLiquidity: 12500000000,
      liquidityChange24h: 0,
      topPairs: [],
      liquidityDistribution: {}
    });
  }
};

// Fetch volatility data
export const fetchVolatilityData = async (): Promise<VolatilityData> => {
  try {
    // Mock volatility data - in real implementation would calculate from price history
    const volatilityData: VolatilityData = {
      marketVolatility: 3.5 + Math.random() * 4, // 3.5-7.5% volatility
      volatilityChange24h: (Math.random() - 0.5) * 2,
      highVolatilityCoins: [
        {
          symbol: 'PEPE',
          volatility: 15.2 + Math.random() * 10,
          priceChange24h: (Math.random() - 0.5) * 30
        },
        {
          symbol: 'SHIB',
          volatility: 12.8 + Math.random() * 8,
          priceChange24h: (Math.random() - 0.5) * 25
        },
        {
          symbol: 'DOGE',
          volatility: 11.5 + Math.random() * 7,
          priceChange24h: (Math.random() - 0.5) * 20
        },
        {
          symbol: 'AXS',
          volatility: 10.2 + Math.random() * 6,
          priceChange24h: (Math.random() - 0.5) * 18
        },
        {
          symbol: 'SAND',
          volatility: 9.8 + Math.random() * 5,
          priceChange24h: (Math.random() - 0.5) * 16
        }
      ],
      volatilityTrend: Math.random() > 0.6 ? 'increasing' : Math.random() > 0.3 ? 'decreasing' : 'stable'
    };

    cacheResponse("volatility_data", volatilityData, 300);
    return volatilityData;
  } catch (error) {
    return handleApiError(error, "volatility_data", {
      marketVolatility: 5.0,
      volatilityChange24h: 0,
      highVolatilityCoins: [],
      volatilityTrend: 'stable'
    });
  }
};
