
import { Skeleton } from "@/components/ui/skeleton";

export function RatingTableSkeleton() {
  return (
    <div className="rounded-lg border border-border bg-card p-5 gradient-border animate-scale-in">
      <div className="mb-4">
        <h3 className="text-lg font-semibold">Asset Ratings</h3>
        <p className="text-sm text-muted-foreground">
          Loading rating data...
        </p>
      </div>
      
      <div className="space-y-4">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
