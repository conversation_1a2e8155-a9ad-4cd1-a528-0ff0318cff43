/**
 * API Key Management Component
 * Displays API key status and validation results in the admin dashboard
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Key,
  Shield,
  Activity
} from 'lucide-react';
import {
  validateAllApiKeys,
  type ApiValidationResult,
  type ApiKeyStatus
} from '@/services/api/apiKeyValidation';
import { validateAllDatabaseProviders } from '@/services/api/dynamicApiKeyValidation';

export function ApiKeyManagement() {
  const [validationResult, setValidationResult] = useState<ApiValidationResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [useDynamicValidation, setUseDynamicValidation] = useState(true);

  const validateKeys = async () => {
    setIsLoading(true);
    try {
      console.log('🔑 Starting API key validation...');
      console.log('📊 Using dynamic validation:', useDynamicValidation);

      const result = useDynamicValidation
        ? await validateAllDatabaseProviders()
        : await validateAllApiKeys();

      setValidationResult(result);
      setLastChecked(new Date());

      console.log('✅ Validation completed:', result);
    } catch (error) {
      console.error('❌ API key validation failed:', error);

      // Fallback to static validation if dynamic fails
      if (useDynamicValidation) {
        console.log('🔄 Falling back to static validation...');
        try {
          const fallbackResult = await validateAllApiKeys();
          setValidationResult(fallbackResult);
          setLastChecked(new Date());
        } catch (fallbackError) {
          console.error('❌ Fallback validation also failed:', fallbackError);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    validateKeys();
  }, []);

  const getStatusIcon = (service: ApiKeyStatus) => {
    if (service.isValid) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    } else if (service.isRequired) {
      return <XCircle className="h-4 w-4 text-red-600" />;
    } else {
      return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusBadge = (service: ApiKeyStatus) => {
    if (service.isValid) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Valid</Badge>;
    } else if (service.isRequired) {
      return <Badge variant="destructive">Invalid</Badge>;
    } else {
      return <Badge variant="secondary">Optional</Badge>;
    }
  };

  const getOverallStatus = () => {
    if (!validationResult) return null;

    if (validationResult.allValid) {
      return (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            All API keys are valid and working correctly.
          </AlertDescription>
        </Alert>
      );
    } else if (validationResult.requiredValid) {
      return (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            Required API keys are valid. Some optional services may not be available.
          </AlertDescription>
        </Alert>
      );
    } else {
      return (
        <Alert className="border-red-200 bg-red-50">
          <XCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            Some required API keys are invalid. Core functionality may be limited.
          </AlertDescription>
        </Alert>
      );
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              API Key Management
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant={useDynamicValidation ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setUseDynamicValidation(!useDynamicValidation);
                  // Re-validate with new mode
                  setTimeout(validateKeys, 100);
                }}
                disabled={isLoading}
              >
                <Activity className="h-4 w-4 mr-2" />
                {useDynamicValidation ? 'Database' : 'Static'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={validateKeys}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Validate Keys
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-sm text-muted-foreground space-y-1">
            <div>
              <strong>Mode:</strong> {useDynamicValidation ? 'Database' : 'Static'} -
              {useDynamicValidation
                ? ' Validates all API providers from database (includes newly added providers)'
                : ' Validates only hardcoded services (CoinGecko, DeepSeek, Supabase)'
              }
            </div>
            <div className="text-xs text-amber-600">
              <strong>Note:</strong> Some APIs (like CoinMarketCap) block direct browser requests due to CORS policy.
              This is normal - validation is based on configuration format.
            </div>
          </div>

          {getOverallStatus()}

          {validationResult && (
            <>
              {/* Summary Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">
                    {validationResult.summary.total}
                  </div>
                  <div className="text-sm text-gray-600">Total Services</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {validationResult.summary.valid}
                  </div>
                  <div className="text-sm text-gray-600">Valid Keys</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {validationResult.summary.required}
                  </div>
                  <div className="text-sm text-gray-600">Required</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {validationResult.summary.configured}
                  </div>
                  <div className="text-sm text-gray-600">Configured</div>
                </div>
              </div>

              {/* Service Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Service Status
                </h3>

                {validationResult.services.map((service) => (
                  <div
                    key={service.service}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      {getStatusIcon(service)}
                      <div>
                        <div className="font-medium">{service.service}</div>
                        <div className="text-sm text-gray-600">
                          {service.isRequired ? 'Required' : 'Optional'} •
                          {service.isConfigured ? ' Configured' : ' Not configured'}
                        </div>
                        {service.error && (
                          <div className="text-sm text-red-600 mt-1">
                            {service.error}
                          </div>
                        )}
                        {service.rateLimit && (
                          <div className="text-sm text-blue-600 mt-1">
                            Rate limit: {service.rateLimit.remaining} requests remaining
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(service)}
                      {service.isValid && (
                        <Activity className="h-4 w-4 text-green-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {lastChecked && (
                <div className="text-sm text-gray-500 text-center">
                  Last checked: {lastChecked.toLocaleString()}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
