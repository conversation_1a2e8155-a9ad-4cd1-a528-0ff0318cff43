
// Generate stories based on market data
export const generateStories = (count: number, sentiment: 'positive' | 'neutral' | 'negative', topCoins: any[], trendingCoins: any[]) => {
  const stories = [];
  const allCoins = [...topCoins, ...(trendingCoins || []).map((t: any) => t.item)].filter(Boolean);
  
  // News sources
  const sources = ['CryptoNews', 'BlockchainTimes', 'CoinDesk', 'CoinTelegraph', 'Decrypt'];
  
  // Impact mapping
  const impactMap = {
    positive: ['high', 'medium', 'medium', 'low'],
    neutral: ['medium', 'low', 'low', 'low'],
    negative: ['high', 'high', 'medium', 'low']
  };
  
  // Template titles
  const titleTemplates = {
    positive: [
      "{coin} Surges as {reason}",
      "Bullish Outlook for {coin} After {reason}",
      "{coin} Gains Momentum Following {reason}",
      "Analysts Predict Further Growth for {coin}",
      "{coin} Price Target Raised by Analysts",
      "{coin} Breaks Resistance Level, Bulls Take Control"
    ],
    neutral: [
      "{coin} Stabilizes After Recent Volatility",
      "New {coin} Update Released with Mixed Reception",
      "{coin} Team Announces Roadmap Updates",
      "Market Remains Undecided on {coin} Future",
      "{coin} Trading Volume Remains Consistent",
      "Experts Weigh In On {coin}'s Market Position"
    ],
    negative: [
      "{coin} Drops Following {reason}",
      "Bearish Signals for {coin} as {reason}",
      "{coin} Faces Pressure After {reason}",
      "Investors Cautious on {coin} Amid {reason}",
      "{coin} Breaks Support Level as Sellers Dominate",
      "Analysts Lower {coin} Price Targets"
    ]
  };
  
  // Reason phrases
  const reasonPhrases = {
    positive: [
      "Partnership Announcement",
      "Successful Network Upgrade",
      "Major Exchange Listing",
      "Institutional Adoption News",
      "New Feature Launch",
      "Positive Regulatory Development",
      "Increased Developer Activity"
    ],
    neutral: [
      "Protocol Update Discussion",
      "Community Governance Vote",
      "Market Consolidation",
      "Technical Analysis Indicators Neutral",
      "Mixed Economic Signals",
      "Team Restructuring"
    ],
    negative: [
      "Regulatory Concerns",
      "Profit Taking",
      "Technical Issues",
      "Market-Wide Correction",
      "Competitor Advancement",
      "Lower Than Expected Adoption",
      "Security Vulnerability Disclosure"
    ]
  };
  
  for (let i = 0; i < count; i++) {
    // Select a random coin
    const randomCoin = allCoins[Math.floor(Math.random() * allCoins.length)];
    if (!randomCoin) continue;
    
    // Get the coin symbol
    const symbol = randomCoin.symbol?.toUpperCase() || 'BTC';
    const name = randomCoin.name || 'Bitcoin';
    
    // Generate a score based on sentiment
    const scoreMap = { positive: 0.4, neutral: 0, negative: -0.4 };
    const baseScore = scoreMap[sentiment];
    const sentimentScore = baseScore + (Math.random() * 0.3) * (sentiment === 'negative' ? -1 : 1);
    
    // Select a random template and reason
    const templates = titleTemplates[sentiment];
    const reasons = reasonPhrases[sentiment];
    const template = templates[Math.floor(Math.random() * templates.length)];
    const reason = reasons[Math.floor(Math.random() * reasons.length)];
    
    // Generate title
    const title = template
      .replace('{coin}', name)
      .replace('{reason}', reason);
    
    // Generate related assets (1-3 coins)
    const relatedCount = Math.floor(Math.random() * 3) + 1;
    const relatedAssets = [];
    for (let j = 0; j < relatedCount; j++) {
      const relatedCoin = allCoins[Math.floor(Math.random() * allCoins.length)];
      if (relatedCoin && relatedCoin.symbol) {
        relatedAssets.push(relatedCoin.symbol.toUpperCase());
      }
    }
    
    // Generate summary based on title and sentiment
    const summary = `${title}. Analysis suggests this could ${
      sentiment === 'positive' ? 'positively impact' :
      sentiment === 'negative' ? 'negatively affect' :
      'have mixed effects on'
    } the market in the short term.`;
    
    // Generate a realistic published time
    const hoursAgo = Math.floor(Math.random() * 12);
    const publishedDate = new Date(Date.now() - hoursAgo * 60 * 60 * 1000).toISOString();
    
    // Impact level
    const impacts = impactMap[sentiment];
    const impact = impacts[Math.floor(Math.random() * impacts.length)] as 'high' | 'medium' | 'low';
    
    // Add the story
    stories.push({
      id: `news-${Date.now()}-${i}`,
      title,
      source: sources[Math.floor(Math.random() * sources.length)],
      url: "#",
      published: publishedDate,
      summary,
      sentiment,
      sentimentScore,
      impact,
      relatedAssets: Array.from(new Set(relatedAssets))
    });
  }
  
  return stories;
};
