
import { FundamentalCoin } from "@/hooks/useFundamentalAnalysis";

interface TokenomicsTabProps {
  coin: FundamentalCoin;
}

export function TokenomicsTab({ coin }: TokenomicsTabProps) {
  const { tokenomics } = coin;
  
  if (!tokenomics) {
    return (
      <div className="text-center py-12 text-muted-foreground">
        No tokenomics data available
      </div>
    );
  }

  return (
    <div className="p-4 space-y-4">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div className="bg-secondary/40 p-4 rounded-lg">
          <div className="text-sm text-muted-foreground">Circulating Supply</div>
          <div className="text-lg font-medium">
            {tokenomics.circulating_supply?.toLocaleString(undefined, {maximumFractionDigits: 0})}
          </div>
        </div>
        <div className="bg-secondary/40 p-4 rounded-lg">
          <div className="text-sm text-muted-foreground">Total Supply</div>
          <div className="text-lg font-medium">
            {tokenomics.total_supply?.toLocaleString(undefined, {maximumFractionDigits: 0}) || 'Unlimited'}
          </div>
        </div>
        <div className="bg-secondary/40 p-4 rounded-lg">
          <div className="text-sm text-muted-foreground">Max Supply</div>
          <div className="text-lg font-medium">
            {tokenomics.max_supply?.toLocaleString(undefined, {maximumFractionDigits: 0}) || 'Unlimited'}
          </div>
        </div>
      </div>
      
      <div className="bg-secondary/40 p-4 rounded-lg">
        <div className="text-sm text-muted-foreground mb-2">Supply Distribution</div>
        <div className="h-4 w-full bg-secondary rounded-full overflow-hidden">
          <div 
            className="h-full bg-primary" 
            style={{ width: `${tokenomics.supply_percentage?.toFixed(2)}%` }}
          ></div>
        </div>
        <div className="text-xs text-muted-foreground mt-2">
          {tokenomics.supply_percentage?.toFixed(2)}% of total supply in circulation
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-secondary/40 p-4 rounded-lg">
          <div className="text-sm text-muted-foreground">Market Cap</div>
          <div className="text-lg font-medium">
            ${tokenomics.market_cap?.toLocaleString()}
          </div>
        </div>
        <div className="bg-secondary/40 p-4 rounded-lg">
          <div className="text-sm text-muted-foreground">Fully Diluted Valuation</div>
          <div className="text-lg font-medium">
            ${tokenomics.fully_diluted_valuation?.toLocaleString() || 'N/A'}
          </div>
        </div>
      </div>
    </div>
  );
}
