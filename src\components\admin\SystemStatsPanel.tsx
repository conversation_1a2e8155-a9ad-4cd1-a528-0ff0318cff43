
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Activity, Users, AlertTriangle, Database, TrendingUp, Globe } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

export function SystemStatsPanel() {
  // System overview stats
  const { data: systemStats } = useQuery({
    queryKey: ['systemStats'],
    queryFn: async () => {
      const [
        { count: totalUsers },
        { count: totalErrors },
        { count: totalAPIRequests },
        { count: activeUsers }
      ] = await Promise.all([
        supabase.from('profiles').select('*', { count: 'exact', head: true }),
        supabase.from('app_error_logs').select('*', { count: 'exact', head: true }),
        supabase.from('api_usage_logs').select('*', { count: 'exact', head: true }),
        supabase.from('user_activity_logs')
          .select('*', { count: 'exact', head: true })
          .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      ]);

      return { totalUsers, totalErrors, totalAPIRequests, activeUsers };
    },
    refetchInterval: 30000,
  });

  // API usage by provider
  const { data: apiUsageByProvider } = useQuery({
    queryKey: ['apiUsageByProvider'],
    queryFn: async () => {
      const { data } = await supabase
        .from('api_usage_logs')
        .select('provider, request_count')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      const usage = data?.reduce((acc: any, log) => {
        acc[log.provider] = (acc[log.provider] || 0) + log.request_count;
        return acc;
      }, {});

      return Object.entries(usage || {}).map(([provider, count]) => ({
        provider,
        count
      }));
    },
    refetchInterval: 60000,
  });

  // Error trends over last 24 hours
  const { data: errorTrends } = useQuery({
    queryKey: ['errorTrends'],
    queryFn: async () => {
      const { data } = await supabase
        .from('app_error_logs')
        .select('created_at, error_type')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: true });

      // Group by hour
      const hourlyErrors = data?.reduce((acc: any, error) => {
        const hour = new Date(error.created_at).getHours();
        const key = `${hour}:00`;
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {});

      return Object.entries(hourlyErrors || {}).map(([time, count]) => ({
        time,
        errors: count
      }));
    },
    refetchInterval: 60000,
  });

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats?.totalUsers || 0}</div>
            <p className="text-xs text-muted-foreground">Registered users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users (24h)</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats?.activeUsers || 0}</div>
            <p className="text-xs text-muted-foreground">Users active today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Requests</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats?.totalAPIRequests || 0}</div>
            <p className="text-xs text-muted-foreground">Total API calls</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Count</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{systemStats?.totalErrors || 0}</div>
            <p className="text-xs text-muted-foreground">Application errors</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* API Usage by Provider */}
        <Card>
          <CardHeader>
            <CardTitle>API Usage by Provider (24h)</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={apiUsageByProvider}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="provider" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Error Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Error Trends (24h)</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={errorTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="errors" stroke="#ef4444" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
