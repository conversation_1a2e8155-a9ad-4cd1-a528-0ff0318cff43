
import { cn } from "@/lib/utils";
import { NavigationLink } from "./NavigationLink";
import { NavigationItem } from "./navigationData";

interface NavigationSectionProps {
  items: NavigationItem[];
  currentPath: string;
}

export function NavigationSection({ items, currentPath }: NavigationSectionProps) {
  return (
    <div className="grid gap-1">
      <div className="grid gap-1">
        {items.map((item) => (
          <NavigationLink
            key={item.href}
            href={item.href}
            icon={<item.icon className="w-4 h-4" />}
            title={item.title}
            isActive={currentPath === item.href}
            collapsed={false}
          />
        ))}
      </div>
    </div>
  );
}
