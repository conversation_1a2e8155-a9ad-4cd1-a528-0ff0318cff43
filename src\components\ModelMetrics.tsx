
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface MetricProps {
  label: string;
  value: string | number;
  description?: string;
  className?: string;
}

function Metric({ label, value, description, className }: MetricProps) {
  return (
    <div className={cn("space-y-1", className)}>
      <p className="text-sm font-medium text-muted-foreground">{label}</p>
      <p className="text-2xl font-bold">{value}</p>
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
    </div>
  );
}

interface ModelMetricsProps {
  metrics: {
    rmse: number;
    mae: number;
    rSquared: number;
    mape: number;
  };
  assetName: string;
  modelType?: string;
}

export default function ModelMetrics({
  metrics,
  assetName,
  modelType = "Hybrid ARIMA-LSTM",
}: ModelMetricsProps) {
  const { rmse, mae, rSquared, mape } = metrics;
  
  const getPerformanceColor = (value: number, threshold: number, reverse = false) => {
    const isGood = reverse ? value < threshold : value > threshold;
    return isGood ? "text-green-500" : "text-red-500";
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">
          {assetName} {modelType} Performance
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <Metric 
            label="RMSE" 
            value={rmse.toFixed(2)} 
            description="Root Mean Square Error"
          />
          <Metric 
            label="MAE" 
            value={mae.toFixed(2)} 
            description="Mean Absolute Error"
          />
          <Metric 
            label="R²" 
            value={rSquared.toFixed(2)} 
            description="Coefficient of Determination"
            className={getPerformanceColor(rSquared, 0.7)}
          />
          <Metric 
            label="MAPE" 
            value={`${mape.toFixed(2)}%`} 
            description="Mean Absolute Percentage Error"
            className={getPerformanceColor(mape, 10, true)}
          />
        </div>
      </CardContent>
    </Card>
  );
}
