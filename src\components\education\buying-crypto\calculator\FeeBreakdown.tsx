
import { Exchange, FeeCalculationResult } from "./types";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Settings, Building, Activity, BarChart } from "lucide-react";

interface FeeBreakdownProps {
  exchange: Exchange;
  fees: FeeCalculationResult;
  investmentAmount: number;
  slippagePercentage: number;
}

export default function FeeBreakdown({ exchange, fees, investmentAmount, slippagePercentage }: FeeBreakdownProps) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center">
          <Settings className="mr-2 h-5 w-5 text-primary" />
          Fee Breakdown
        </CardTitle>
        <CardDescription>
          {exchange.type === 'CEX' ? 
            <span className="flex items-center"><Building className="mr-1 h-4 w-4" /> {exchange.name}</span> :
            <span className="flex items-center"><Activity className="mr-1 h-4 w-4" /> {exchange.name}</span>
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Trading Fee</h3>
              <p className="text-2xl font-semibold">${fees.tradingFee.toFixed(2)}</p>
              <p className="text-xs text-muted-foreground">
                {exchange.tradingFee}% of transaction
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Withdrawal Fee</h3>
              <p className="text-2xl font-semibold">${fees.withdrawalFee.toFixed(2)}</p>
              <p className="text-xs text-muted-foreground">Fixed network fee</p>
            </div>
            
            {exchange.type === 'DEX' && (
              <>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Gas Fee (est.)</h3>
                  <p className="text-2xl font-semibold">${fees.gasFee.toFixed(2)}</p>
                  <p className="text-xs text-muted-foreground">Network transaction cost</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Slippage Impact</h3>
                  <p className="text-2xl font-semibold">${fees.slippageCost.toFixed(2)}</p>
                  <p className="text-xs text-muted-foreground">{slippagePercentage}% price impact</p>
                </div>
              </>
            )}
          </div>
          
          <Separator />
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Total Costs</h3>
              <p className="text-3xl font-bold text-primary">${fees.totalCost.toFixed(2)}</p>
              <p className="text-xs text-muted-foreground">
                {(fees.totalCost / investmentAmount * 100).toFixed(2)}% of investment
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Crypto Received</h3>
              <p className="text-3xl font-bold">${fees.receivedCrypto.toFixed(2)}</p>
              <p className="text-xs text-muted-foreground">
                {(fees.receivedCrypto / investmentAmount * 100).toFixed(2)}% of investment
              </p>
            </div>
          </div>
          
          <div className="bg-muted p-4 rounded-md">
            <h3 className="font-medium mb-2 flex items-center">
              <BarChart className="mr-2 h-4 w-4" />
              Platform Insights
            </h3>
            <div className="grid grid-cols-2 gap-x-6 gap-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Security</span>
                <span className="text-sm font-medium">{exchange.security}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Liquidity</span>
                <span className="text-sm font-medium">{exchange.liquidity}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
