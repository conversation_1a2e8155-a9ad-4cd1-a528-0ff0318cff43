
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertTriangle, RefreshCw, Eye, Code } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';

export function ErrorLogsPanel() {
  const [timeRange, setTimeRange] = useState('24h');
  const [errorType, setErrorType] = useState('all');

  const { data: errorLogs, isLoading, refetch } = useQuery({
    queryKey: ['errorLogs', timeRange, errorType],
    queryFn: async () => {
      const hours = timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 1;
      let query = supabase
        .from('app_error_logs')
        .select('*')
        .gte('created_at', new Date(Date.now() - hours * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .limit(100);

      if (errorType !== 'all') {
        query = query.eq('error_type', errorType);
      }

      const { data } = await query;
      return data || [];
    },
    refetchInterval: 30000,
  });

  const { data: errorTypes } = useQuery({
    queryKey: ['errorTypes'],
    queryFn: async () => {
      const { data } = await supabase
        .from('app_error_logs')
        .select('error_type')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

      const types = [...new Set(data?.map(log => log.error_type) || [])];
      return types;
    },
  });

  const getErrorTypeBadge = (type: string) => {
    const colors: { [key: string]: string } = {
      'network': 'bg-blue-100 text-blue-800',
      'validation': 'bg-yellow-100 text-yellow-800',
      'authentication': 'bg-red-100 text-red-800',
      'database': 'bg-purple-100 text-purple-800',
      'api': 'bg-green-100 text-green-800',
      'ui': 'bg-orange-100 text-orange-800'
    };
    
    return (
      <Badge className={colors[type] || 'bg-gray-100 text-gray-800'}>
        {type}
      </Badge>
    );
  };

  const ErrorDetailsDialog = ({ error }: { error: any }) => (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <Eye className="w-4 h-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            Error Details
          </DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh]">
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Basic Information</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Type:</span> {getErrorTypeBadge(error.error_type)}
                </div>
                <div>
                  <span className="font-medium">Timestamp:</span> {new Date(error.created_at).toLocaleString()}
                </div>
                <div>
                  <span className="font-medium">User ID:</span> {error.user_id || 'N/A'}
                </div>
                <div>
                  <span className="font-medium">Page URL:</span> {error.page_url || 'N/A'}
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Error Message</h4>
              <div className="bg-red-50 p-3 rounded border text-sm">
                {error.error_message}
              </div>
            </div>

            {error.stack_trace && (
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Code className="w-4 h-4" />
                  Stack Trace
                </h4>
                <div className="bg-gray-50 p-3 rounded border text-xs font-mono whitespace-pre-wrap">
                  {error.stack_trace}
                </div>
              </div>
            )}

            {error.user_agent && (
              <div>
                <h4 className="font-semibold mb-2">User Agent</h4>
                <div className="bg-gray-50 p-3 rounded border text-xs">
                  {error.user_agent}
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Last Hour</SelectItem>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
            </SelectContent>
          </Select>

          <Select value={errorType} onValueChange={setErrorType}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Error type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              {errorTypes?.map((type) => (
                <SelectItem key={type} value={type}>{type}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Error Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Application Error Logs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Message</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Page</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {errorLogs?.map((error) => (
                <TableRow key={error.id}>
                  <TableCell className="text-xs">
                    {new Date(error.created_at).toLocaleString()}
                  </TableCell>
                  <TableCell>{getErrorTypeBadge(error.error_type)}</TableCell>
                  <TableCell className="max-w-md truncate">
                    {error.error_message}
                  </TableCell>
                  <TableCell className="text-xs">
                    {error.user_id ? error.user_id.substring(0, 8) + '...' : 'Anonymous'}
                  </TableCell>
                  <TableCell className="text-xs max-w-xs truncate">
                    {error.page_url}
                  </TableCell>
                  <TableCell>
                    <ErrorDetailsDialog error={error} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
