
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import HeatMapChart from "@/components/HeatMapChart";

interface MarketHeatmapTabProps {
  heatmapData: any[];
  isLoadingHeatmap: boolean;
}

export default function MarketHeatmapTab({
  heatmapData,
  isLoadingHeatmap
}: MarketHeatmapTabProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Market Sector Performance Heatmap</CardTitle>
        <CardDescription>
          Visual representation of market performance across different sectors.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <HeatMapChart 
          title="Market Sector Performance"
          data={heatmapData || []}
          isLoading={isLoadingHeatmap}
          colorScale="performance"
        />
      </CardContent>
    </Card>
  );
}
