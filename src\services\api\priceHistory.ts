/**
 * Price History API Service
 * Fetches historical price data for cryptocurrencies
 */

import { coinGeckoAxios } from './coinGeckoClient';
import type { PriceHistoryResponse } from './priceHistory/types';

// Import centralized types
export type {
  PriceDataPoint,
  CandleData,
  ForecastDataResponse,
  ForecastMetrics,
  PriceHistoryResponse
} from './priceHistory/types';

// Re-export the main forecast function
export { getPriceForecastData } from './priceHistory/forecastService';

/**
 * Fetch historical price data for a specific cryptocurrency
 * @param coinId - CoinGecko coin ID (e.g., 'bitcoin', 'ethereum')
 * @param days - Number of days of historical data (1-365)
 * @returns Promise with price history data
 */
export async function fetchPriceHistory(
  coinId: string,
  days: number = 30
): Promise<PriceHistoryResponse> {
  try {
    console.log(`📈 Fetching ${days} days of price history for ${coinId}`);

    // Validate inputs
    if (!coinId || typeof coinId !== 'string') {
      throw new Error('Invalid coin ID provided');
    }

    if (days < 1 || days > 365) {
      throw new Error('Days must be between 1 and 365');
    }

    // Fetch price history from CoinGecko
    const response = await coinGeckoAxios.get(`/coins/${coinId}/market_chart`, {
      params: {
        vs_currency: 'usd',
        days: days,
        interval: days <= 1 ? 'hourly' : days <= 90 ? 'daily' : 'weekly'
      }
    });

    if (!response.data || !response.data.prices) {
      throw new Error('Invalid response format from CoinGecko API');
    }

    // Transform the data to our format
    const priceData = response.data.prices.map((pricePoint: [number, number], index: number) => {
      const [timestamp, price] = pricePoint;
      const volumePoint = response.data.total_volumes?.[index];
      const marketCapPoint = response.data.market_caps?.[index];

      return {
        date: new Date(timestamp).toISOString().split('T')[0], // Format as YYYY-MM-DD
        price: price,
        volume: volumePoint ? volumePoint[1] : undefined,
        marketCap: marketCapPoint ? marketCapPoint[1] : undefined,
        timestamp: timestamp // Add timestamp for compatibility
      };
    });

    console.log(`✅ Successfully fetched ${priceData.length} price data points for ${coinId}`);

    return {
      data: priceData,
      success: true
    };

  } catch (error: any) {
    console.error(`❌ Error fetching price history for ${coinId}:`, error);

    // Handle specific error cases
    if (error.response?.status === 404) {
      return {
        data: [],
        success: false,
        error: `Cryptocurrency '${coinId}' not found`
      };
    }

    if (error.response?.status === 429) {
      return {
        data: [],
        success: false,
        error: 'Rate limit exceeded. Please try again later.'
      };
    }

    return {
      data: [],
      success: false,
      error: error.message || 'Failed to fetch price history'
    };
  }
}

/**
 * Fetch price history for multiple cryptocurrencies
 * @param coinIds - Array of CoinGecko coin IDs
 * @param days - Number of days of historical data
 * @returns Promise with price history data for all coins
 */
export async function fetchMultiplePriceHistories(
  coinIds: string[],
  days: number = 30
): Promise<Record<string, PriceHistoryResponse>> {
  try {
    console.log(`📈 Fetching price histories for ${coinIds.length} coins`);

    const promises = coinIds.map(coinId =>
      fetchPriceHistory(coinId, days).then(result => ({ coinId, result }))
    );

    const results = await Promise.all(promises);

    const priceHistories: Record<string, PriceHistoryResponse> = {};
    results.forEach(({ coinId, result }) => {
      priceHistories[coinId] = result;
    });

    console.log(`✅ Successfully fetched price histories for ${coinIds.length} coins`);
    return priceHistories;

  } catch (error: any) {
    console.error('❌ Error fetching multiple price histories:', error);

    // Return empty results for all coins in case of error
    const emptyResults: Record<string, PriceHistoryResponse> = {};
    coinIds.forEach(coinId => {
      emptyResults[coinId] = {
        data: [],
        success: false,
        error: error.message || 'Failed to fetch price history'
      };
    });

    return emptyResults;
  }
}

/**
 * Get price change percentage between two data points
 * @param oldPrice - Previous price
 * @param newPrice - Current price
 * @returns Percentage change
 */
export function calculatePriceChange(oldPrice: number, newPrice: number): number {
  if (!oldPrice || oldPrice === 0) return 0;
  return ((newPrice - oldPrice) / oldPrice) * 100;
}

/**
 * Get the latest price from price history data
 * @param priceData - Array of price data points
 * @returns Latest price or null if no data
 */
export function getLatestPrice(priceData: any[]): number | null {
  if (!priceData || priceData.length === 0) return null;
  return priceData[priceData.length - 1].price;
}

/**
 * Get price change over the entire period
 * @param priceData - Array of price data points
 * @returns Percentage change from first to last price
 */
export function getPeriodPriceChange(priceData: any[]): number {
  if (!priceData || priceData.length < 2) return 0;

  const firstPrice = priceData[0].price;
  const lastPrice = priceData[priceData.length - 1].price;

  return calculatePriceChange(firstPrice, lastPrice);
}
