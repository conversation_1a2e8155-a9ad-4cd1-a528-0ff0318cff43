import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Link } from "react-router-dom";
import { Calculator, ChartBar } from "lucide-react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { exchanges } from "./exchangeData";

interface InputParametersProps {
  investmentAmount: number;
  setInvestmentAmount: (amount: number) => void;
  selectedExchange: string;
  setSelectedExchange: (exchange: string) => void;
  slippagePercentage: number;
  setSlippagePercentage: (percentage: number) => void;
}

export default function InputParameters({
  investmentAmount,
  setInvestmentAmount,
  selectedExchange,
  setSelectedExchange,
  slippagePercentage,
  setSlippagePercentage
}: InputParametersProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center">
          <Calculator className="mr-2 h-5 w-5 text-primary" />
          Input Parameters
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">Investment Amount (USD)</label>
          <Input
            type="number"
            value={investmentAmount}
            onChange={(e) => setInvestmentAmount(Number(e.target.value))}
            min="10"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">Select Exchange</label>
          <select
            className="w-full p-2 border rounded-md bg-background"
            value={selectedExchange}
            onChange={(e) => setSelectedExchange(e.target.value)}
          >
            <optgroup label="Centralized Exchanges">
              <option value="cex-1">Coinbase</option>
              <option value="cex-2">Binance</option>
              <option value="cex-3">Kraken</option>
            </optgroup>
            <optgroup label="Decentralized Exchanges">
              <option value="dex-1">Uniswap</option>
              <option value="dex-2">SushiSwap</option>
              <option value="dex-3">Curve</option>
            </optgroup>
          </select>
        </div>
        
        {/* Only show slippage for DEXs */}
        {selectedExchange.startsWith('dex') && (
          <div>
            <div className="flex justify-between mb-2">
              <label className="block text-sm font-medium">Slippage (%)</label>
              <span>{slippagePercentage}%</span>
            </div>
            <Slider 
              value={[slippagePercentage]} 
              min={0.1} 
              max={5} 
              step={0.1} 
              onValueChange={(value) => setSlippagePercentage(value[0])}
            />
            <p className="text-xs text-muted-foreground mt-1">Estimated price impact based on liquidity and order size.</p>
          </div>
        )}
        
        <div className="pt-4">
          <Link to="/market-insights" className="block w-full">
            <Button variant="secondary" className="w-full">
              <ChartBar className="mr-2 h-4 w-4" />
              Compare Live Exchange Rates
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
