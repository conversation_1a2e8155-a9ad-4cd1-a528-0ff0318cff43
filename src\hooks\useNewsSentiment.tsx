
import { useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchNewsItems } from "@/services/api";
import { generateSentimentData } from "@/services/api/sentimentAnalysis";
import { fetchAlertRules, createAlertRule, updateAlertRule, deleteAlertRule } from "@/services/newsApi";

// Types
export interface NewsItem {
  id: string;
  title: string;
  source: string;
  url: string;
  published: string;
  summary: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  sentimentScore: number;
  impact: 'high' | 'medium' | 'low';
  relatedAssets: string[];
}

export interface SentimentData {
  overallMarketSentiment: number;
  sentimentByAsset: {
    asset: string;
    current: number;
    change24h: number;
    sentiment: 'positive' | 'neutral' | 'negative';
  }[];
  topPositiveStories: NewsItem[];
  topNegativeStories: NewsItem[];
  sentimentTrend: {
    date: string;
    value: number;
  }[];
}

export interface AlertRule {
  id: string;
  name: string;
  asset: string;
  condition: 'sentiment_below' | 'sentiment_above' | 'sentiment_change' | 'news_mention';
  threshold: number;
  timeframe: string;
  active: boolean;
  notifications: ('email' | 'push' | 'in_app')[];
}

export type NotificationType = 'email' | 'push' | 'in_app';

// Mock data for alert rules - keep these as fallback
const mockAlertRules: AlertRule[] = [
  {
    id: '1',
    name: 'BTC Extremely Negative Sentiment',
    asset: 'BTC',
    condition: 'sentiment_below',
    threshold: -0.6,
    timeframe: '1h',
    active: true,
    notifications: ['push', 'in_app']
  },
  {
    id: '2',
    name: 'ETH Sentiment Rapid Change',
    asset: 'ETH',
    condition: 'sentiment_change',
    threshold: 0.3,
    timeframe: '4h',
    active: true,
    notifications: ['email', 'in_app']
  },
  {
    id: '3',
    name: 'SOL Positive News Mentions',
    asset: 'SOL',
    condition: 'news_mention',
    threshold: 10,
    timeframe: '24h',
    active: false,
    notifications: ['in_app']
  }
];

export const useNewsSentiment = () => {
  const queryClient = useQueryClient();
  
  // Use React Query for data fetching with real data from CoinGecko
  const { 
    data: newsData = [], 
    isLoading: isLoadingNews 
  } = useQuery({
    queryKey: ['news'],
    queryFn: fetchNewsItems,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  const { 
    data: sentimentData, 
    isLoading: isLoadingSentiment 
  } = useQuery({
    queryKey: ['sentiment'],
    queryFn: generateSentimentData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  const { 
    data: alertRules = [], 
    isLoading: isLoadingAlerts 
  } = useQuery({
    queryKey: ['alertRules'],
    queryFn: fetchAlertRules,
    staleTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: mockAlertRules,
    retry: 1,
  });

  // Mutations for alert rules
  const addAlertRuleMutation = useMutation({
    mutationFn: (rule: Omit<AlertRule, 'id'>) => createAlertRule(rule),
    onSuccess: () => {
      // Invalidate and refetch alert rules after adding
      queryClient.invalidateQueries({ queryKey: ['alertRules'] });
    }
  });

  const toggleAlertRuleMutation = useMutation({
    mutationFn: (id: string) => {
      const rule = alertRules.find(r => r.id === id);
      if (!rule) throw new Error('Alert rule not found');
      return updateAlertRule(id, { active: !rule.active });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alertRules'] });
    }
  });

  const deleteAlertRuleMutation = useMutation({
    mutationFn: (id: string) => deleteAlertRule(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alertRules'] });
    }
  });

  // Function to trigger a manual refresh
  const refreshData = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['news'] });
    queryClient.invalidateQueries({ queryKey: ['sentiment'] });
    queryClient.invalidateQueries({ queryKey: ['alertRules'] });
  }, [queryClient]);

  // Check if any data is still loading
  const isLoading = isLoadingNews || isLoadingSentiment || isLoadingAlerts;

  return {
    newsData,
    sentimentData,
    alertRules,
    refreshData,
    isLoading,
    addAlertRule: addAlertRuleMutation.mutate,
    toggleAlertRule: toggleAlertRuleMutation.mutate,
    deleteAlertRule: deleteAlertRuleMutation.mutate
  };
};
