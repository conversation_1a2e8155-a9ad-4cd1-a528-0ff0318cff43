# 📁 Codebase Structure

## Overview

CryptoVision Pro follows a feature-driven architecture with clear separation of concerns. The codebase is organized into logical modules that promote maintainability, reusability, and scalability.

## 🗂️ Directory Structure

```
cryptovisionpro/
├── 📄 Configuration Files
│   ├── package.json              # Dependencies & scripts
│   ├── vite.config.ts            # Vite build configuration
│   ├── tsconfig.json             # TypeScript configuration
│   ├── tailwind.config.ts        # Tailwind CSS configuration
│   ├── components.json           # Shadcn/ui configuration
│   ├── .env.example              # Environment variables template
│   └── index.html                # HTML entry point
│
├── 📂 public/                    # Static assets
│   ├── education/                # Educational SVG illustrations
│   ├── favicon.ico               # Application favicon
│   ├── og-image.png             # Open Graph image
│   └── placeholder.svg          # Fallback image
│
├── 📂 src/                       # Main source directory
│   ├── main.tsx                  # Application entry point
│   ├── index.css                 # Global styles
│   ├── App.css                   # Application-specific styles
│   └── vite-env.d.ts            # Vite type definitions
```

## 🎯 Source Code Organization

### **Pages Layer** (19 Routes)
```
src/pages/
├── Index.tsx                     # Landing/Dashboard page
├── Dashboard.tsx                 # Main analytics dashboard
├── MarketInsights.tsx           # Unified market intelligence
├── CoinDiscovery.tsx            # Emerging coin discovery
├── DeFiOpportunities.tsx        # DeFi yield farming
├── OnChainAnalytics.tsx         # Blockchain analytics
├── Portfolio.tsx                # Portfolio management
├── NewsSentiment.tsx            # News & sentiment analysis
├── Education.tsx                # Educational platform
├── SmartMoney.tsx               # Smart money tracking
├── FundamentalAnalysis.tsx      # Token fundamentals
├── Anomalies.tsx                # Anomaly detection
├── Ratings.tsx                  # Token rating system
├── Forecasting.tsx              # Price predictions
├── AdvancedVisualizations.tsx   # Advanced charts
├── AIInsights.tsx               # AI-powered insights
├── TokenScamDetector.tsx        # Scam detection
├── Auth.tsx                     # Authentication
├── ProfileSettings.tsx          # User settings
└── NotFound.tsx                 # 404 page
```

### **Components Layer** (80+ Components)
```
src/components/
├── 📂 ui/                       # Base UI components (Shadcn/ui)
│   ├── button.tsx, card.tsx, table.tsx, dialog.tsx
│   ├── form.tsx, input.tsx, select.tsx, checkbox.tsx
│   ├── tabs.tsx, accordion.tsx, tooltip.tsx, badge.tsx
│   └── 📂 chart/                # Chart components
│       ├── chart-container.tsx
│       ├── chart-tooltip.tsx
│       └── index.tsx
│
├── 📂 auth/                     # Authentication components
│   ├── AuthLayout.tsx           # Authentication layout
│   ├── AuthTabs.tsx             # Sign in/up tabs
│   ├── SignInForm.tsx           # Sign in form
│   ├── SignUpForm.tsx           # Sign up form
│   ├── SocialAuth.tsx           # Social authentication
│   └── AuthAlerts.tsx           # Authentication alerts
│
├── 📂 dashboard/                # Dashboard components
│   ├── DashboardContainer.tsx   # Main dashboard container
│   ├── MarketStatusHeader.tsx   # Market status display
│   ├── KeyMetricsBar.tsx        # Key metrics bar
│   ├── PrimaryAnalyticsGrid.tsx # Main analytics grid
│   ├── LeftAnalyticsPanel.tsx   # Left sidebar analytics
│   ├── RightAnalyticsPanel.tsx  # Right sidebar analytics
│   ├── LoadingState.tsx         # Loading state component
│   ├── ErrorState.tsx           # Error state component
│   ├── TokenCorrelationMapper.tsx # Token correlation analysis
│   ├── EventImpactAnalyzer.tsx  # Event impact analysis
│   ├── VolatilityDashboard.tsx  # Volatility analysis
│   └── TeamScreener.tsx         # Team analysis
│
├── 📂 market/                   # Market analysis components
│   ├── GlobalMarketStats.tsx    # Global market statistics
│   ├── MarketTrends.tsx         # Trending coins & recent additions
│   ├── ExchangesList.tsx        # Exchange rankings
│   ├── TopCoinsTable.tsx        # Top coins table
│   ├── PriceCorrelation.tsx     # Price correlation analysis
│   ├── PriceChange.tsx          # Price change indicators
│   ├── MarketHealthSummary.tsx  # Market health overview
│   ├── MarketInsightsTabs.tsx   # Market insights tabs
│   └── 📂 charts/               # Market charts
│       ├── FearGreedIndex.tsx
│       ├── SectorPerformanceChart.tsx
│       ├── LiquidityMetrics.tsx
│       └── MarketNewsPanel.tsx
│
├── 📂 education/                # Educational components
│   ├── BlockchainEducation.tsx  # Blockchain fundamentals
│   ├── CryptoInvestmentStrategies.tsx # Investment strategies
│   ├── WalletSecurity.tsx       # Wallet security guide
│   ├── ProtocolExplainers.tsx   # DeFi protocol explanations
│   ├── TokenExplorer.tsx        # Token deep dives
│   ├── EducationTutorial.tsx    # Tutorial renderer
│   ├── TutorialContentRenderer.tsx # Content renderer
│   ├── BuyingFirstCrypto.tsx    # Buying crypto guide
│   ├── IntroductionToDeFi.tsx   # DeFi introduction
│   ├── StrategyBuilder.tsx      # Strategy builder
│   ├── StrategyEducation.tsx    # Strategy education
│   ├── Glossary.tsx             # Crypto glossary
│   ├── 📂 buying-crypto/        # Buying crypto tutorial
│   │   ├── OverviewContent.tsx
│   │   ├── CexVsDexContent.tsx
│   │   ├── FeesExplainedContent.tsx
│   │   ├── OrderTypesContent.tsx
│   │   └── NextStepsSection.tsx
│   └── 📂 tabs/                 # Education tabs
│       ├── TutorialsTab.tsx
│       ├── TokensTab.tsx
│       ├── GlossaryTab.tsx
│       └── ProtocolsTab.tsx
```

### **Hooks Layer** (18 Custom Hooks)
```
src/hooks/
├── index.ts                     # Hook exports
├── use-mobile.tsx               # Mobile detection
├── use-toast.ts                 # Toast notifications
├── useStats.tsx                 # Basic market statistics
├── useEnhancedMarketInsights.tsx # Advanced market analytics
├── useDefiData.ts               # DeFi opportunities
├── useFundamentalAnalysis.tsx   # Fundamental analysis
├── useAIInsights.tsx            # AI-powered insights
├── useTokenScamAnalysis.tsx     # Scam detection
├── useOnChainAnalytics.tsx      # On-chain analytics
├── useNewsSentiment.tsx         # News sentiment
├── useRatingSystem.tsx          # Token ratings
├── useSmartMoney.tsx            # Smart money tracking
├── useRealSmartMoney.tsx        # Real smart money data
├── useEducation.tsx             # Educational content
├── useSearch.tsx                # Search functionality
├── useAdminAuth.tsx             # Admin authentication
└── useApiProviders.ts           # API provider management
```

### **Services Layer** (40+ API Services)
```
src/services/
├── 📂 api/                      # API service layer
│   ├── index.ts                 # API exports
│   ├── coinGeckoClient.ts       # CoinGecko API client
│   ├── coinMarketCapClient.ts   # CoinMarketCap API client
│   ├── deepSeekClient.ts        # DeepSeek AI client
│   ├── coinMarketData.ts        # Market data services
│   ├── enhancedMarketInsights.ts # Enhanced analytics
│   ├── marketInsightsApi.ts     # Market insights
│   ├── sentimentAnalysis.ts     # Sentiment analysis
│   ├── whaleAlertApi.ts         # Whale tracking
│   ├── newsItems.ts             # News aggregation
│   ├── newsUtils.ts             # News utilities
│   ├── searchService.ts         # Search functionality
│   ├── anomalyDetectionService.ts # Anomaly detection
│   ├── portfolioService.ts      # Portfolio management
│   ├── fundamentalAnalysisService.ts # Fundamental analysis
│   ├── aiInsightsService.ts     # AI insights
│   ├── coinDiscoveryService.ts  # Coin discovery
│   ├── priceHistory.ts          # Price history service
│   ├── correlationAnalysisService.ts # Token correlation
│   ├── eventImpactAnalysisService.ts # Event impact
│   ├── volatilityAnalysisService.ts # Volatility analysis
│   ├── teamAnalysisService.ts   # Team analysis
│   ├── apiKeyValidation.ts      # API key validation
│   ├── dynamicApiKeyValidation.ts # Dynamic validation
│   │
│   ├── 📂 ai/                   # AI services
│   │   └── AiOptimizationService.ts
│   │
│   ├── 📂 defi/                 # DeFi services
│   │   ├── index.ts, yieldApi.ts, riskApi.ts
│   │   ├── strategyApi.ts, liquidityApi.ts
│   │   ├── protocolApi.ts, gasApi.ts, bridgeApi.ts
│   │   └── types.ts
│   │
│   ├── 📂 discovery/            # Coin discovery services
│   │   ├── emergingCoinsService.ts
│   │   ├── personalizedService.ts
│   │   ├── coinAnalysisService.ts
│   │   ├── trendingDataService.ts
│   │   └── enhancedDiscoveryService.ts
│   │
│   ├── 📂 fundamental/          # Fundamental analysis services
│   │   ├── index.ts, coinAnalysisService.ts
│   │   ├── tokenomicsService.ts, developerService.ts
│   │   ├── onChainService.ts, scoringService.ts
│   │   ├── metricsService.ts, historicalService.ts
│   │   └── types.ts
│   │
│   ├── 📂 analysis/             # Analysis services
│   │   └── coinAnalysisService.ts
│   │
│   ├── 📂 priceHistory/         # Price history & forecasting
│   │   ├── index.ts, historyClient.ts
│   │   ├── forecastService.ts, predictions.ts
│   │   └── technicalIndicators.ts
│   │
│   └── 📂 providers/            # API provider management
│       ├── ApiProviderManager.ts
│       ├── apiProviderService.ts
│       └── integratedApiProviderService.ts
│
├── geckoTerminalApi.ts          # GeckoTerminal API
└── newsApi.ts                   # News API
```

### **Context Layer** (Global State)
```
src/contexts/
├── theme-provider.tsx           # Theme management
├── AuthContext.tsx              # Legacy auth context
└── 📂 auth/                     # Modern auth system
    ├── AuthProvider.tsx         # Auth provider
    ├── useAuth.ts               # Auth hook
    ├── authActions.ts           # Auth actions
    ├── profileUtils.ts          # Profile utilities
    └── types.ts                 # Auth types
```

### **Integration Layer**
```
src/integrations/
└── 📂 supabase/                 # Supabase integration
    ├── client.ts                # Supabase client
    └── types.ts                 # Database types
```

### **Utilities & Types**
```
src/
├── 📂 types/                    # TypeScript definitions
│   ├── aiInsights.ts            # AI insights types
│   └── rating.ts                # Rating system types
│
├── 📂 lib/                      # Utility libraries
│   ├── utils.ts                 # General utilities
│   └── 📂 mockData/             # Mock data
│       └── index.ts
│
└── 📂 utils/                    # Feature utilities
    ├── categoryUtils.tsx        # Category utilities
    └── ratingUtils.ts           # Rating utilities
```

## 🎯 Component Organization Patterns

### **1. Feature-Based Grouping**
Components are organized by feature domain rather than technical type:
```
✅ Good: components/market/MarketTrends.tsx
❌ Avoid: components/tables/MarketTrendsTable.tsx
```

### **2. Hierarchical Structure**
Related components are grouped in subdirectories:
```
components/education/
├── EducationTutorial.tsx        # Main component
├── TutorialContentRenderer.tsx  # Supporting component
└── buying-crypto/               # Feature subdirectory
    ├── OverviewContent.tsx
    ├── CexVsDexContent.tsx
    └── FeesExplainedContent.tsx
```

### **3. Consistent Naming**
- **PascalCase** for component files: `MarketInsights.tsx`
- **camelCase** for utility files: `categoryUtils.tsx`
- **kebab-case** for asset files: `blockchain-fundamentals.svg`

## 🔧 Code Organization Principles

### **1. Single Responsibility**
Each file has a clear, single purpose:
```typescript
// ✅ Good: Focused on market statistics
export const useStats = () => {
  // Market statistics logic only
};

// ❌ Avoid: Mixed responsibilities
export const useEverything = () => {
  // Market stats + auth + UI state
};
```

### **2. Dependency Direction**
Dependencies flow from specific to general:
```
Pages → Components → Hooks → Services → External APIs
```

### **3. Export Patterns**
Consistent export patterns for discoverability:
```typescript
// Barrel exports for related functionality
export * from "./useStats";
export * from "./useMarketInsights";

// Named exports for specific functionality
export { useAuth } from "./auth/useAuth";
export type { User } from "./auth/types";
```

## 📊 Metrics & Statistics

### **Codebase Size**
- **19 Pages** - Main application routes
- **90+ Components** - Reusable UI components (including enhanced analytics)
- **18 Hooks** - Custom business logic hooks
- **40+ Services** - API and data services (including enhanced analytics)
- **4 Contexts** - Global state management
- **Multiple Utilities** - Helper functions and types

### **Enhanced Analytics Components**
- **TokenCorrelationMapper** - Interactive network visualization
- **EventImpactAnalyzer** - Calendar-based event tracking
- **VolatilityDashboard** - ML-based risk analysis
- **TeamScreener** - AI team credibility assessment

### **API Integration**
- **CoinGecko API** - Primary market data (with real API key)
- **CoinMarketCap API** - Alternative market data
- **DeepSeek AI** - AI insights and analysis (with real API key)
- **Supabase** - Authentication and database
- **Multiple providers** - Intelligent API routing and fallbacks

### **File Organization**
- **Feature-driven** structure for maintainability
- **Clear separation** between UI, logic, and data
- **Consistent naming** conventions throughout
- **Logical grouping** of related functionality
- **Enhanced analytics** properly integrated into Dashboard page

This structure ensures the codebase remains maintainable, scalable, and easy to navigate as the platform continues to grow with advanced analytics capabilities.
