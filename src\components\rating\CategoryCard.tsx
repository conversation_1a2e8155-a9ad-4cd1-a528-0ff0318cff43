
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>eader, 
  CardTitle 
} from "@/components/ui/card";
import { getCategoryIcon, formatCategory } from "@/utils/categoryUtils";
import { FactorWeight } from "@/types/rating";
import { FactorSlider } from "./FactorSlider";

interface CategoryCardProps {
  category: string;
  factors: FactorWeight[];
  updateWeight: (factorId: string, weight: number) => void;
}

export function CategoryCard({ category, factors, updateWeight }: CategoryCardProps) {
  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getCategoryIcon(category)}
            <CardTitle>{formatCategory(category)} Factors</CardTitle>
          </div>
        </div>
        <CardDescription>
          Adjust weights to customize your scoring model
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {factors.map((factor) => (
            <FactorSlider 
              key={factor.id}
              factor={factor}
              onWeightChange={(value) => updateWeight(factor.id, value)}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
