
import { AlertRule } from "@/hooks/useNewsSentiment";

export function getConditionText(rule: AlertRule): string {
  switch (rule.condition) {
    case 'sentiment_below':
      return `Sentiment below ${rule.threshold}`;
    case 'sentiment_above':
      return `Sentiment above ${rule.threshold}`;
    case 'sentiment_change':
      return `Sentiment change of ${rule.threshold}`;
    case 'news_mention':
      return `${rule.threshold} news mentions`;
    default:
      return '';
  }
}
