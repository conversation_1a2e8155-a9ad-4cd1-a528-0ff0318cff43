# =============================================================================
# CRYPTOVISION PRO - API CONFIGURATION
# =============================================================================

# -----------------------------------------------------------------------------
# REQUIRED APIs (Core Functionality)
# -----------------------------------------------------------------------------

# Supabase Configuration (Required for authentication & database)
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# CoinGecko API (Primary market data - Free tier: 30 req/min, 10k/month)
VITE_COINGECKO_API_KEY=your_coingecko_api_key_here

# -----------------------------------------------------------------------------
# MARKET DATA PROVIDERS (Multi-provider redundancy)
# -----------------------------------------------------------------------------

# CoinMarketCap API (Backup market data - Free tier: 333 req/day, 10k/month)
VITE_COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_here

# CryptoCompare API (Alternative market data - Free tier: 100k req/month)
VITE_CRYPTOCOMPARE_API_KEY=your_cryptocompare_api_key_here

# -----------------------------------------------------------------------------
# ON-CHAIN DATA PROVIDERS
# -----------------------------------------------------------------------------

# Glassnode API (Professional on-chain metrics - Free tier: 1k req/day)
VITE_GLASSNODE_API_KEY=your_glassnode_api_key_here

# Etherscan API (Ethereum blockchain data - Free tier: 5 req/sec)
VITE_ETHERSCAN_API_KEY=your_etherscan_api_key_here

# -----------------------------------------------------------------------------
# DEFI DATA PROVIDERS
# -----------------------------------------------------------------------------

# DeFi Llama API (No API key required - Free unlimited)
# GeckoTerminal API (No API key required - 30 req/min)

# -----------------------------------------------------------------------------
# AI PROVIDERS (Cost optimization through multi-provider strategy)
# -----------------------------------------------------------------------------

# DeepSeek AI (Primary AI - Cost effective: $0.14/1M tokens)
VITE_DEEPSEEK_API_KEY=your_deepseek_api_key_here
VITE_DEEPSEEK_API_URL=https://api.deepseek.com

# OpenAI API (High quality AI - Premium: $1.50/1M tokens)
VITE_OPENAI_API_KEY=your_openai_api_key_here

# Claude API (Specialized financial analysis - $8.00/1M tokens)
VITE_CLAUDE_API_KEY=your_claude_api_key_here

# -----------------------------------------------------------------------------
# APPLICATION CONFIGURATION
# -----------------------------------------------------------------------------

VITE_APP_NAME=CryptoVision Pro
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# -----------------------------------------------------------------------------
# OPTIONAL CONFIGURATION
# -----------------------------------------------------------------------------

# Debug mode (development only)
VITE_DEBUG_MODE=true

# API request timeout (milliseconds)
VITE_API_TIMEOUT=30000

# Cache TTL settings (milliseconds)
VITE_CACHE_TTL_MARKET=300000
VITE_CACHE_TTL_ONCHAIN=1800000
VITE_CACHE_TTL_AI=3600000
