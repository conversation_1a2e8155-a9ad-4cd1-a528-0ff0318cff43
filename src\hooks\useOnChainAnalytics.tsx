import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { generateOnChainData, GECKOTERMINAL_ATTRIBUTION } from "@/services/geckoTerminalApi";

// Types for our on-chain analytics data
export type NetworkActivityData = {
  transactions: {
    daily: number;
    weekly: number;
    change: number;
    history: { date: string; count: number }[];
  };
  activeAddresses: {
    daily: number;
    weekly: number;
    change: number;
    history: { date: string; count: number }[];
  };
  gasUsed: {
    daily: number;
    weekly: number;
    change: number;
    history: { date: string; value: number }[];
  };
  blockHeight: number;
};

export type WhaleMovementData = {
  largeTransactions: {
    count: number;
    volume: number;
    change: number;
  };
  topTokensAccumulated: {
    token: string;
    symbol: string;
    amount: number;
    valueUsd: number;
  }[];
  recentMovements: {
    id: string;
    from: string;
    to: string;
    token: string;
    symbol: string;
    amount: number;
    valueUsd: number;
    timestamp: string;
  }[];
};

export type NetworkHealthData = {
  nodes: {
    total: number;
    change: number;
  };
  staking: {
    total: number;
    percentage: number;
    change: number;
  };
  hashrate: {
    value: number;
    unit: string;
    change: number;
  };
  difficulty: {
    value: number;
    change: number;
  };
  metrics: {
    name: string;
    value: number;
    change: number;
  }[];
};

// Default mock data (will be used as fallback if API fails)
const mockNetworkActivity: NetworkActivityData = {
  transactions: {
    daily: 1245678,
    weekly: 8350456,
    change: 5.8,
    history: Array(14).fill(0).map((_, i) => ({
      date: new Date(Date.now() - (13-i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      count: 1100000 + Math.floor(Math.random() * 300000)
    }))
  },
  activeAddresses: {
    daily: 387291,
    weekly: 1804532,
    change: -2.3,
    history: Array(14).fill(0).map((_, i) => ({
      date: new Date(Date.now() - (13-i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      count: 350000 + Math.floor(Math.random() * 100000)
    }))
  },
  gasUsed: {
    daily: 95423,
    weekly: 643987,
    change: 8.2,
    history: Array(14).fill(0).map((_, i) => ({
      date: new Date(Date.now() - (13-i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      value: 85000 + Math.floor(Math.random() * 20000)
    }))
  },
  blockHeight: 18453769
};

const mockWhaleMovements: WhaleMovementData = {
  largeTransactions: {
    count: 124,
    volume: 1859000000,
    change: 12.7
  },
  topTokensAccumulated: [
    { token: "Bitcoin", symbol: "BTC", amount: 2450, valueUsd: 156800000 },
    { token: "Ethereum", symbol: "ETH", amount: 18750, valueUsd: 43125000 },
    { token: "Solana", symbol: "SOL", amount: 952000, valueUsd: 123760000 },
    { token: "Arbitrum", symbol: "ARB", amount: 8750000, valueUsd: 11375000 },
    { token: "Optimism", symbol: "OP", amount: 3650000, valueUsd: 9125000 }
  ],
  recentMovements: [
    { 
      id: "tx1", 
      from: "0x3a54..8c92", 
      to: "0xf871..2e43", 
      token: "Bitcoin", 
      symbol: "BTC", 
      amount: 850, 
      valueUsd: 54400000,
      timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString()
    },
    { 
      id: "tx2", 
      from: "0xc72b..1f47", 
      to: "0x47e9..9a12", 
      token: "Ethereum", 
      symbol: "ETH", 
      amount: 7250, 
      valueUsd: 16675000,
      timestamp: new Date(Date.now() - 42 * 60 * 1000).toISOString()
    },
    { 
      id: "tx3", 
      from: "0x8e51..7c36", 
      to: "0x2a19..5f78", 
      token: "Solana", 
      symbol: "SOL", 
      amount: 425000, 
      valueUsd: 55250000,
      timestamp: new Date(Date.now() - 67 * 60 * 1000).toISOString()
    },
    { 
      id: "tx4", 
      from: "0x9c31..4a82", 
      to: "0xe08c..2b56", 
      token: "Uniswap", 
      symbol: "UNI", 
      amount: 1250000, 
      valueUsd: 6250000,
      timestamp: new Date(Date.now() - 92 * 60 * 1000).toISOString()
    },
    { 
      id: "tx5", 
      from: "0x4f17..6d23", 
      to: "0xb348..9e72", 
      token: "Chainlink", 
      symbol: "LINK", 
      amount: 1875000, 
      valueUsd: 28125000,
      timestamp: new Date(Date.now() - 118 * 60 * 1000).toISOString()
    }
  ]
};

const mockNetworkHealth: NetworkHealthData = {
  nodes: {
    total: 11452,
    change: 3.7
  },
  staking: {
    total: 24750000,
    percentage: 68.4,
    change: 2.1
  },
  hashrate: {
    value: 512.76,
    unit: "EH/s",
    change: 5.3
  },
  difficulty: {
    value: 75.28,
    change: 4.8
  },
  metrics: [
    { name: "Average Block Time", value: 12.3, change: -0.8 },
    { name: "Average Transaction Fee", value: 2.85, change: 15.2 },
    { name: "Block Space Utilization", value: 87.6, change: 4.2 },
    { name: "New Addresses", value: 127845, change: 7.5 },
    { name: "Network Capacity", value: 92.3, change: 1.2 }
  ]
};

export function useOnChainAnalytics() {
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();
  
  // Use React Query for data fetching with real data from GeckoTerminal
  const { 
    data: onChainData, 
    isLoading 
  } = useQuery({
    queryKey: ['onchain-data'],
    queryFn: generateOnChainData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    placeholderData: {
      networkActivity: mockNetworkActivity,
      whaleMovements: mockWhaleMovements,
      networkHealth: mockNetworkHealth
    },
    meta: {
      onError: (error: any) => {
        setError(error instanceof Error ? error.message : 'Failed to fetch on-chain data');
        console.error('Error fetching on-chain data:', error);
      }
    }
  });
  
  // Extract data from the query result
  const networkData = onChainData?.networkActivity || mockNetworkActivity;
  const whaleData = onChainData?.whaleMovements || mockWhaleMovements;
  const healthData = onChainData?.networkHealth || mockNetworkHealth;
  
  // Function to refresh data
  const refreshData = () => {
    queryClient.invalidateQueries({ queryKey: ['onchain-data'] });
  };
  
  return {
    loading: isLoading,
    error,
    networkData,
    whaleData,
    healthData,
    refreshData,
    attribution: GECKOTERMINAL_ATTRIBUTION
  };
}
