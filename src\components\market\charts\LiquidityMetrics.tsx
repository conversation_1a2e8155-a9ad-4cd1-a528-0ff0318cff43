import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { 
  Droplets, 
  TrendingUp, 
  TrendingDown, 
  Building2,
  ArrowUpDown,
  DollarSign
} from 'lucide-react';
import { LiquidityMetrics as LiquidityMetricsType } from '@/services/api/enhancedMarketInsights';

interface LiquidityMetricsProps {
  liquidity: LiquidityMetricsType;
  loading?: boolean;
}

export const LiquidityMetrics: React.FC<LiquidityMetricsProps> = ({ liquidity, loading }) => {
  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Droplets className="w-5 h-5" />
            Market Liquidity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded animate-pulse" />
            <div className="grid grid-cols-2 gap-4">
              <div className="h-24 bg-gray-200 rounded animate-pulse" />
              <div className="h-24 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (value: number) => {
    if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    return `$${value.toLocaleString()}`;
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? 
      <TrendingUp className="w-4 h-4 text-green-600" /> : 
      <TrendingDown className="w-4 h-4 text-red-600" />;
  };

  // Prepare data for charts
  const pairData = liquidity.majorPairs.map(pair => ({
    name: pair.pair,
    liquidity: pair.liquidity / 1e9, // Convert to billions
    volume: pair.volume24h / 1e9,
    spread: pair.spread * 100 // Convert to percentage
  }));

  const exchangeData = liquidity.exchangeLiquidity.map(exchange => ({
    name: exchange.exchange,
    liquidity: exchange.liquidity / 1e9,
    marketShare: exchange.marketShare
  }));

  // Colors for charts
  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300'];

  // Custom tooltip for bar chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg">
          <p className="font-semibold">{label}</p>
          <p className="text-blue-600">
            Liquidity: ${payload[0].value.toFixed(2)}B
          </p>
          {payload[1] && (
            <p className="text-green-600">
              Volume: ${payload[1].value.toFixed(2)}B
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Droplets className="w-5 h-5" />
          Market Liquidity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Total Liquidity Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-blue-600" />
                  <span className="text-sm text-gray-600">Total Liquidity</span>
                </div>
                <div className="text-2xl font-bold">
                  {formatCurrency(liquidity.totalLiquidity)}
                </div>
                <div className="flex items-center gap-1">
                  {getChangeIcon(liquidity.liquidityChange24h)}
                  <span className={`text-sm ${getChangeColor(liquidity.liquidityChange24h)}`}>
                    {liquidity.liquidityChange24h >= 0 ? '+' : ''}{liquidity.liquidityChange24h.toFixed(2)}%
                  </span>
                  <span className="text-sm text-gray-500">24h</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <ArrowUpDown className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-gray-600">Avg Spread</span>
                </div>
                <div className="text-2xl font-bold">
                  {(pairData.reduce((sum, pair) => sum + pair.spread, 0) / pairData.length).toFixed(3)}%
                </div>
                <div className="text-sm text-gray-500">
                  Across major pairs
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Major Trading Pairs */}
        <div className="space-y-4">
          <h4 className="font-semibold text-sm">Major Trading Pairs</h4>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={pairData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="liquidity" fill="#8884d8" name="Liquidity ($B)" />
                <Bar dataKey="volume" fill="#82ca9d" name="Volume ($B)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Detailed Pair Information */}
        <div className="space-y-3">
          <h4 className="font-semibold text-sm">Pair Details</h4>
          <div className="space-y-2">
            {liquidity.majorPairs.map((pair, index) => (
              <div key={pair.pair} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: COLORS[index % COLORS.length] }} />
                  <div>
                    <p className="font-medium text-sm">{pair.pair}</p>
                    <p className="text-xs text-gray-500">Spread: {(pair.spread * 100).toFixed(3)}%</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-sm">{formatCurrency(pair.liquidity)}</p>
                  <p className="text-xs text-gray-500">Vol: {formatCurrency(pair.volume24h)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Exchange Liquidity Distribution */}
        <div className="space-y-4">
          <h4 className="font-semibold text-sm">Exchange Distribution</h4>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Pie Chart */}
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={exchangeData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="marketShare"
                  >
                    {exchangeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value: number) => [`${value}%`, 'Market Share']}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>

            {/* Exchange List */}
            <div className="space-y-2">
              {liquidity.exchangeLiquidity.map((exchange, index) => (
                <div key={exchange.exchange} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: COLORS[index % COLORS.length] }} />
                    <div className="flex items-center gap-1">
                      <Building2 className="w-4 h-4 text-gray-500" />
                      <span className="font-medium text-sm">{exchange.exchange}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">{formatCurrency(exchange.liquidity)}</p>
                    <p className="text-xs text-gray-500">{exchange.marketShare}% share</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Liquidity Health Indicator */}
        <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20">
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Droplets className="w-5 h-5 text-blue-600" />
                <h4 className="font-semibold">Liquidity Health</h4>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Market Depth</span>
                  <span className="font-medium">
                    {liquidity.totalLiquidity > 50e9 ? 'Excellent' : 
                     liquidity.totalLiquidity > 30e9 ? 'Good' : 
                     liquidity.totalLiquidity > 15e9 ? 'Fair' : 'Poor'}
                  </span>
                </div>
                <Progress 
                  value={Math.min(100, (liquidity.totalLiquidity / 50e9) * 100)} 
                  className="h-2"
                />
              </div>

              <p className="text-xs text-gray-600 leading-relaxed">
                {liquidity.totalLiquidity > 50e9 ? 
                  "Excellent liquidity conditions with tight spreads and minimal slippage expected for large trades." :
                  liquidity.totalLiquidity > 30e9 ?
                  "Good liquidity levels supporting most trading activities with reasonable spreads." :
                  "Moderate liquidity - consider market impact for larger trades and monitor spreads closely."
                }
              </p>
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  );
};
