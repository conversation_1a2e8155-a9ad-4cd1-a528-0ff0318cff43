<svg xmlns="http://www.w3.org/2000/svg" width="800" height="500" viewBox="0 0 800 500" fill="none">
  <defs>
    <linearGradient id="aaveGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#B6509E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2EBAC6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="poolGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="500" fill="#0F172A"/>
  
  <!-- Title -->
  <text x="400" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Aave Lending Protocol</text>
  <text x="400" y="65" text-anchor="middle" fill="#94A3B8" font-family="Arial, sans-serif" font-size="14">Decentralized lending and borrowing platform</text>
  
  <!-- Central Lending Pool -->
  <g transform="translate(350, 220)">
    <rect width="120" height="80" rx="12" fill="url(#aaveGradient)" stroke="#B6509E" stroke-width="3"/>
    <text x="60" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Aave Pool</text>
    <text x="60" y="50" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">Lending Pool</text>
    <text x="60" y="65" text-anchor="middle" fill="#E0F2FE" font-family="Arial, sans-serif" font-size="10">Smart Contract</text>
  </g>
  
  <!-- Depositor/Lender -->
  <g transform="translate(100, 120)">
    <rect width="100" height="70" rx="8" fill="#10B981" stroke="#34D399" stroke-width="2"/>
    <text x="50" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Depositor</text>
    <text x="50" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11">(Lender)</text>
    <text x="50" y="55" text-anchor="middle" fill="#D1FAE5" font-family="Arial, sans-serif" font-size="10">Earns Interest</text>
  </g>
  
  <!-- Borrower -->
  <g transform="translate(600, 120)">
    <rect width="100" height="70" rx="8" fill="#EF4444" stroke="#F87171" stroke-width="2"/>
    <text x="50" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Borrower</text>
    <text x="50" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11">Pays Interest</text>
    <text x="50" y="55" text-anchor="middle" fill="#FEE2E2" font-family="Arial, sans-serif" font-size="10">Provides Collateral</text>
  </g>
  
  <!-- aTokens -->
  <g transform="translate(150, 300)">
    <circle cx="40" cy="40" r="30" fill="#8B5CF6" stroke="#A78BFA" stroke-width="2"/>
    <text x="40" y="35" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">aToken</text>
    <text x="40" y="48" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">(aETH)</text>
    <text x="40" y="85" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Interest-bearing</text>
    <text x="40" y="100" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">receipt token</text>
  </g>
  
  <!-- Collateral -->
  <g transform="translate(550, 300)">
    <rect width="80" height="60" rx="8" fill="#F59E0B" stroke="#FBBF24" stroke-width="2"/>
    <text x="40" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">Collateral</text>
    <text x="40" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">ETH, BTC</text>
    <text x="40" y="52" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">USDC, etc.</text>
    <text x="40" y="80" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="9">Locked in protocol</text>
  </g>
  
  <!-- Flow arrows -->
  <path d="M 200 155 Q 275 190 350 240" stroke="#10B981" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <text x="250" y="180" fill="#10B981" font-family="Arial, sans-serif" font-size="10">Deposit Assets</text>
  
  <path d="M 350 240 Q 275 210 200 180" stroke="#8B5CF6" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <text x="250" y="200" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="10">Receive aTokens</text>
  
  <path d="M 600 155 Q 525 190 470 240" stroke="#EF4444" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <text x="550" y="180" fill="#EF4444" font-family="Arial, sans-serif" font-size="10">Borrow Assets</text>
  
  <path d="M 470 240 Q 525 210 600 180" stroke="#F59E0B" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <text x="550" y="200" fill="#F59E0B" font-family="Arial, sans-serif" font-size="10">Provide Collateral</text>
  
  <!-- Interest Rate Model -->
  <g transform="translate(50, 380)">
    <rect width="200" height="80" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Interest Rate Model</text>
    <text x="100" y="40" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Variable rates based on</text>
    <text x="100" y="55" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">supply and demand</text>
    <text x="100" y="70" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="10">Utilization Rate = Borrowed/Supplied</text>
  </g>
  
  <!-- Liquidation -->
  <g transform="translate(300, 380)">
    <rect width="200" height="80" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Liquidation</text>
    <text x="100" y="40" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">If collateral value drops</text>
    <text x="100" y="55" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">below threshold (LTV)</text>
    <text x="100" y="70" text-anchor="middle" fill="#EF4444" font-family="Arial, sans-serif" font-size="10">Collateral can be liquidated</text>
  </g>
  
  <!-- Flash Loans -->
  <g transform="translate(550, 380)">
    <rect width="200" height="80" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Flash Loans</text>
    <text x="100" y="40" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Borrow without collateral</text>
    <text x="100" y="55" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Must repay in same transaction</text>
    <text x="100" y="70" text-anchor="middle" fill="#06B6D4" font-family="Arial, sans-serif" font-size="10">Used for arbitrage & refinancing</text>
  </g>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#06B6D4"/>
    </marker>
  </defs>
</svg>
