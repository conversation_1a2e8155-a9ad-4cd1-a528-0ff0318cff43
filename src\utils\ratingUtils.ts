
// Function to generate scores based on real market data
export const generateScoresFromMarketData = (marketData: any) => {
  // Generate semi-random but plausible scores based on market data
  const generateScore = (base: number, variance: number) => {
    // Create a score that varies slightly but is anchored to market performance
    return Math.min(10, Math.max(1, base + (Math.random() - 0.5) * variance));
  };

  // Calculate base scores using market data indicators
  const getPriceChangeScore = (change: number) => {
    // Map the price change to a score between 1-10
    return Math.min(10, Math.max(1, 5 + (change / 10)));
  };
  
  const getMarketCapScore = (marketCap: number) => {
    // Larger market cap gets slightly higher fundamental score (stability)
    if (marketCap > 50000000000) return 8 + Math.random();
    if (marketCap > 10000000000) return 7 + Math.random();
    if (marketCap > 1000000000) return 6 + Math.random();
    if (marketCap > 100000000) return 5 + Math.random();
    return 4 + Math.random();
  };
  
  const getVolumeScore = (volume: number, marketCap: number) => {
    // Volume to market cap ratio - higher is better for technical score
    const ratio = volume / marketCap;
    if (ratio > 0.3) return 9 + Math.random() * 0.5;
    if (ratio > 0.2) return 8 + Math.random() * 0.5;
    if (ratio > 0.1) return 7 + Math.random() * 0.5;
    if (ratio > 0.05) return 6 + Math.random() * 0.5;
    return 5 + Math.random() * 0.5;
  };

  // Extract relevant data
  const priceChange = marketData.price_change_percentage_24h || 0;
  const marketCap = marketData.market_cap || 0;
  const volume = marketData.total_volume || 0;
  const ath = marketData.ath_change_percentage || 0;
  
  // Base scores on real metrics
  const technicalBase = getPriceChangeScore(priceChange);
  const fundamentalBase = getMarketCapScore(marketCap);
  const socialBase = 5 + Math.random() * 3; // Randomized
  const onChainBase = getVolumeScore(volume, marketCap);
  
  return {
    technical: generateScore(technicalBase, 1.5),
    onChain: generateScore(onChainBase, 2),
    social: generateScore(socialBase, 2),
    fundamental: generateScore(fundamentalBase, 1.5)
  };
};

// Transform CoinGecko data to our rating format
export const transformCoinData = (coinsData: any[], marketData: any, mockCryptoAssets: any[]) => {
  if (!coinsData || !marketData || coinsData.length === 0) {
    console.log("Using mock asset data");
    return mockCryptoAssets;
  }
  
  return coinsData.map(coin => {
    const coinMarketData = marketData[coin.id];
    
    // If we don't have market data for this coin, use generic values
    if (!coinMarketData) {
      console.log(`No market data for ${coin.id}, using mock data`);
      const mockAsset = mockCryptoAssets.find(a => a.id.toLowerCase() === coin.id.toLowerCase());
      return mockAsset || {
        id: coin.id,
        name: coin.name,
        symbol: coin.symbol.toUpperCase(),
        price: coin.current_price || 0,
        marketCap: coin.market_cap || 0,
        change24h: coin.price_change_percentage_24h || 0,
        scores: {
          technical: 5 + Math.random() * 3,
          onChain: 5 + Math.random() * 3,
          social: 5 + Math.random() * 3,
          fundamental: 5 + Math.random() * 3
        },
        overallScore: 5 + Math.random() * 3
      };
    }
    
    // Generate scores based on market data
    const scores = generateScoresFromMarketData(coinMarketData);
    
    // Calculate overall score as weighted average
    const overallScore = (
      scores.technical * 0.25 +
      scores.onChain * 0.25 +
      scores.social * 0.2 +
      scores.fundamental * 0.3
    );
    
    return {
      id: coin.id,
      name: coin.name,
      symbol: coin.symbol.toUpperCase(),
      price: coinMarketData.current_price || coin.current_price || 0,
      marketCap: coinMarketData.market_cap || coin.market_cap || 0,
      change24h: coinMarketData.price_change_percentage_24h || coin.price_change_percentage_24h || 0,
      scores,
      overallScore
    };
  });
};
