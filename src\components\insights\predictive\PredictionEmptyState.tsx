
import { Lightbulb } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>eader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";

export default function PredictionEmptyState() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5" />
          Price Prediction
        </CardTitle>
        <CardDescription>ML-generated price forecast with confidence intervals</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-8 text-center text-muted-foreground">
          No prediction data available for this asset
        </div>
      </CardContent>
    </Card>
  );
}
