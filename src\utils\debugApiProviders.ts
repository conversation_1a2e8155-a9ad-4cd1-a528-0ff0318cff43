/**
 * Debug utilities for API Provider Management
 * Use these functions to diagnose and fix issues
 */

import { supabase } from '@/integrations/supabase/client';
import { getAllApiProviders } from '@/services/api/providers/apiProviderService';
import { getProviderStatsWithUsage } from '@/services/api/providers/integratedApiProviderService';

/**
 * Check database tables and setup
 */
export async function checkDatabaseSetup(): Promise<void> {
  console.log('🔍 Checking database setup...');
  
  try {
    // Check if api_providers table exists
    const { data: providers, error: providersError } = await supabase
      .from('api_providers')
      .select('count')
      .limit(1);

    if (providersError) {
      console.error('❌ api_providers table issue:', providersError);
      if (providersError.code === '42P01') {
        console.log('💡 Solution: Run the database migration script');
        console.log('   Navigate to Admin Dashboard → API Management → Provider Management');
        console.log('   Follow the database setup wizard');
      }
      return;
    }

    console.log('✅ api_providers table exists');

    // Check if api_costs table exists
    const { data: costs, error: costsError } = await supabase
      .from('api_costs')
      .select('count')
      .limit(1);

    if (costsError) {
      console.error('❌ api_costs table issue:', costsError);
      return;
    }

    console.log('✅ api_costs table exists');

    // Check if api_usage_logs table exists
    const { data: logs, error: logsError } = await supabase
      .from('api_usage_logs')
      .select('count')
      .limit(1);

    if (logsError) {
      console.error('❌ api_usage_logs table issue:', logsError);
      return;
    }

    console.log('✅ api_usage_logs table exists');
    console.log('🎉 Database setup is complete!');

  } catch (error) {
    console.error('❌ Database check failed:', error);
  }
}

/**
 * Check API provider data
 */
export async function checkProviderData(): Promise<void> {
  console.log('🔍 Checking provider data...');
  
  try {
    const providers = await getAllApiProviders();
    console.log(`📊 Found ${providers.length} providers:`, providers);

    if (providers.length === 0) {
      console.log('💡 No providers found. You need to:');
      console.log('   1. Run the database migration script');
      console.log('   2. Or add providers manually');
      return;
    }

    // Check provider stats
    const stats = await getProviderStatsWithUsage();
    console.log(`📈 Provider stats:`, stats);

    // Check for data issues
    stats.forEach(stat => {
      if (stat.requestsToday === 0 && stat.quotaUsed === 0) {
        console.log(`⚠️  Provider "${stat.name}" has no usage data`);
      }
    });

  } catch (error) {
    console.error('❌ Provider data check failed:', error);
  }
}

/**
 * Test toggle functionality
 */
export async function testToggleFunctionality(): Promise<void> {
  console.log('🔍 Testing toggle functionality...');
  
  try {
    const providers = await getAllApiProviders();
    
    if (providers.length === 0) {
      console.log('❌ No providers to test');
      return;
    }

    const testProvider = providers[0];
    console.log(`🧪 Testing toggle for provider: ${testProvider.name}`);
    
    const originalStatus = testProvider.is_active;
    console.log(`   Original status: ${originalStatus ? 'active' : 'inactive'}`);

    // Import the toggle function
    const { toggleProviderStatus } = await import('@/services/api/providers/integratedApiProviderService');
    
    // Toggle to opposite state
    const newStatus = !originalStatus;
    console.log(`   Toggling to: ${newStatus ? 'active' : 'inactive'}`);
    
    const result = await toggleProviderStatus(testProvider.id, newStatus);
    console.log('✅ Toggle successful:', result);

    // Toggle back to original state
    console.log(`   Toggling back to: ${originalStatus ? 'active' : 'inactive'}`);
    const backResult = await toggleProviderStatus(testProvider.id, originalStatus);
    console.log('✅ Toggle back successful:', backResult);

    console.log('🎉 Toggle functionality is working!');

  } catch (error) {
    console.error('❌ Toggle test failed:', error);
  }
}

/**
 * Generate sample data for testing
 */
export async function generateSampleData(): Promise<void> {
  console.log('🧪 Generating sample data...');
  
  try {
    // Import test utilities
    const { generateRealisticTestData } = await import('@/utils/testApiUsage');
    await generateRealisticTestData();
    console.log('🎉 Sample data generated successfully!');
  } catch (error) {
    console.error('❌ Sample data generation failed:', error);
  }
}

/**
 * Run comprehensive diagnostics
 */
export async function runDiagnostics(): Promise<void> {
  console.log('🔧 Running comprehensive diagnostics...');
  console.log('=====================================');
  
  await checkDatabaseSetup();
  console.log('');
  
  await checkProviderData();
  console.log('');
  
  await testToggleFunctionality();
  console.log('');
  
  console.log('🎯 Diagnostics complete!');
  console.log('=====================================');
}

/**
 * Quick fix for common issues
 */
export async function quickFix(): Promise<void> {
  console.log('🔧 Running quick fix...');
  
  try {
    // Check if database is set up
    const { error } = await supabase
      .from('api_providers')
      .select('count')
      .limit(1);

    if (error && error.code === '42P01') {
      console.log('❌ Database not set up');
      console.log('💡 Please run the database migration script first');
      console.log('   Go to: Admin Dashboard → API Management → Provider Management');
      console.log('   Follow the database setup wizard');
      return;
    }

    // Check if providers exist
    const providers = await getAllApiProviders();
    
    if (providers.length === 0) {
      console.log('⚠️  No providers found, generating sample data...');
      await generateSampleData();
    } else {
      console.log('✅ Providers exist, checking data...');
      await checkProviderData();
    }

    console.log('🎉 Quick fix complete!');

  } catch (error) {
    console.error('❌ Quick fix failed:', error);
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).debugApiProviders = {
    checkDatabaseSetup,
    checkProviderData,
    testToggleFunctionality,
    generateSampleData,
    runDiagnostics,
    quickFix,
  };
  
  console.log('🔧 API Provider Debug Tools Available:');
  console.log('- window.debugApiProviders.runDiagnostics() - Run full diagnostics');
  console.log('- window.debugApiProviders.quickFix() - Quick fix common issues');
  console.log('- window.debugApiProviders.checkDatabaseSetup() - Check database');
  console.log('- window.debugApiProviders.checkProviderData() - Check provider data');
  console.log('- window.debugApiProviders.testToggleFunctionality() - Test toggles');
  console.log('- window.debugApiProviders.generateSampleData() - Generate test data');
}
