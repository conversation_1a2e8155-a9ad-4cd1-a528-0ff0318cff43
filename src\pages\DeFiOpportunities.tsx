
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import HeaderBar from "@/components/HeaderBar";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import YieldOpportunities from "@/components/defi/YieldOpportunities";
import RiskAnalysis from "@/components/defi/RiskAnalysis";
import StrategyBuilder from "@/components/defi/StrategyBuilder";
import LiquidityPoolAnalyzer from "@/components/defi/LiquidityPoolAnalyzer";
import ProtocolHealthMetrics from "@/components/defi/ProtocolHealthMetrics";
import GasOptimizer from "@/components/defi/GasOptimizer";
import CrossChainBridges from "@/components/defi/CrossChainBridges";
import { useDefiData } from "@/hooks/useDefiData";

export default function DeFiOpportunities() {
  const { toast } = useToast();
  const { loading, refreshData, error } = useDefiData();
  
  const handleRefresh = () => {
    refreshData();
    toast({
      title: "Data refreshed",
      description: "All DeFi opportunities have been updated with the latest data.",
    });
  };
  
  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar 
        title="DeFi Opportunity Explorer" 
        description="Find optimal yield opportunities across DeFi protocols"
        onRefresh={handleRefresh}
        isLoading={loading}
      />
      
      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          {error && (
            <div className="bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 mb-6">
              <p className="text-red-700 dark:text-red-400">{error}</p>
            </div>
          )}
          
          <Tabs defaultValue="yield" className="w-full">
            <TabsList className="grid w-full md:w-auto grid-cols-7 h-auto">
              <TabsTrigger value="yield">Yield Farming</TabsTrigger>
              <TabsTrigger value="risk">Risk Analysis</TabsTrigger>
              <TabsTrigger value="strategy">Strategy Builder</TabsTrigger>
              <TabsTrigger value="liquidity">Liquidity Pools</TabsTrigger>
              <TabsTrigger value="health">Protocol Health</TabsTrigger>
              <TabsTrigger value="gas">Gas Optimizer</TabsTrigger>
              <TabsTrigger value="bridges">Cross-Chain Bridges</TabsTrigger>
            </TabsList>
            
            <TabsContent value="yield" className="mt-6">
              <YieldOpportunities isLoading={loading} />
            </TabsContent>
            
            <TabsContent value="risk" className="mt-6">
              <RiskAnalysis isLoading={loading} />
            </TabsContent>
            
            <TabsContent value="strategy" className="mt-6">
              <StrategyBuilder isLoading={loading} />
            </TabsContent>

            <TabsContent value="liquidity" className="mt-6">
              <LiquidityPoolAnalyzer isLoading={loading} />
            </TabsContent>

            <TabsContent value="health" className="mt-6">
              <ProtocolHealthMetrics isLoading={loading} />
            </TabsContent>

            <TabsContent value="gas" className="mt-6">
              <GasOptimizer isLoading={loading} />
            </TabsContent>

            <TabsContent value="bridges" className="mt-6">
              <CrossChainBridges isLoading={loading} />
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}
