
import { useState } from "react";
import InputParameters from "./calculator/InputParameters";
import FeeBreakdown from "./calculator/FeeBreakdown";
import { calculateFees } from "./calculator/feeCalculation";
import { exchanges } from "./calculator/exchangeData";

export default function FeeCalculator() {
  const [selectedExchange, setSelectedExchange] = useState('cex-1');
  const [investmentAmount, setInvestmentAmount] = useState(1000);
  const [slippagePercentage, setSlippagePercentage] = useState(0.5);

  // Calculate fees based on current selections
  const fees = calculateFees(
    exchanges[selectedExchange],
    investmentAmount,
    slippagePercentage
  );

  return (
    <div className="grid md:grid-cols-3 gap-6">
      <div className="md:col-span-1">
        <InputParameters 
          investmentAmount={investmentAmount}
          setInvestmentAmount={setInvestmentAmount}
          selectedExchange={selectedExchange}
          setSelectedExchange={setSelectedExchange}
          slippagePercentage={slippagePercentage}
          setSlippagePercentage={setSlippagePercentage}
        />
      </div>
      
      <div className="md:col-span-2">
        <FeeBreakdown 
          exchange={exchanges[selectedExchange]} 
          fees={fees} 
          investmentAmount={investmentAmount}
          slippagePercentage={slippagePercentage}
        />
      </div>
    </div>
  );
}
