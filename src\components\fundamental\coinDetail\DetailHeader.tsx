
import { FundamentalCoin } from "@/hooks/useFundamentalAnalysis";

interface DetailHeaderProps {
  coin: FundamentalCoin;
}

export function DetailHeader({ coin }: DetailHeaderProps) {
  if (!coin) return null;

  const formatPrice = (price: number) => `$${price.toLocaleString()}`;
  const formatChange = (change: number) => `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
  const getChangeColor = (change: number) => change >= 0 ? 'text-green-500' : 'text-red-500';

  return (
    <div className="flex items-center gap-4">
      <div className="w-12 h-12 rounded-full overflow-hidden bg-background flex items-center justify-center">
        {coin.image ? (
          <img src={coin.image} alt={coin.name} className="w-10 h-10" />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-lg font-bold">
            {coin.symbol.charAt(0)}
          </div>
        )}
      </div>
      <div>
        <div className="flex items-center gap-2 text-2xl font-semibold leading-none tracking-tight">
          {coin.name}
          <span className="text-sm font-normal text-muted-foreground">
            {coin.symbol}
          </span>
        </div>
        <div className="flex items-center gap-4 mt-1">
          <span className="text-sm font-medium">{formatPrice(coin.price)}</span>
          <span className={`text-xs ${getChangeColor(coin.change24h)}`}>
            {formatChange(coin.change24h)}
          </span>
        </div>
      </div>
    </div>
  );
}
