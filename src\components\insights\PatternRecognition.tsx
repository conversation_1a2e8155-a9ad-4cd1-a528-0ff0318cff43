
import { ChartPattern } from "@/types/aiInsights";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, Tooltip, ResponsiveContainer } from "recharts";
import { ArrowUpRight, ArrowDownRight, ArrowRight, Lightbulb } from "lucide-react";

interface PatternRecognitionProps {
  patterns: ChartPattern[];
  isLoading: boolean;
}

export default function PatternRecognition({ patterns, isLoading }: PatternRecognitionProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Pattern Recognition
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            <Skeleton className="h-[180px] w-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get the most significant pattern (highest confidence)
  const significantPattern = patterns.length > 0 
    ? patterns.sort((a, b) => b.confidence - a.confidence)[0]
    : null;

  if (!significantPattern) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Pattern Recognition
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-8 text-center text-muted-foreground">
            No significant patterns detected in the current timeframe
          </div>
        </CardContent>
      </Card>
    );
  }

  const patternData = significantPattern.pricePoints.map((price, index) => ({
    day: index + 1,
    price
  }));

  const getPatternIcon = (type: string) => {
    switch (type) {
      case "bullish":
        return <ArrowUpRight className="h-4 w-4 text-crypto-positive" />;
      case "bearish":
        return <ArrowDownRight className="h-4 w-4 text-crypto-negative" />;
      default:
        return <ArrowRight className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getPatternColor = (type: string) => {
    switch (type) {
      case "bullish":
        return "bg-crypto-positive/20 text-crypto-positive";
      case "bearish":
        return "bg-crypto-negative/20 text-crypto-negative";
      default:
        return "bg-secondary text-muted-foreground";
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Pattern Recognition
          </div>
          <Badge variant="outline" className={getPatternColor(significantPattern.type)}>
            {getPatternIcon(significantPattern.type)}
            <span className="ml-1 capitalize">{significantPattern.type}</span>
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            {significantPattern.name}
            <span className="text-sm font-normal text-muted-foreground">
              ({significantPattern.confidence}% confidence)
            </span>
          </h3>
          <p className="text-sm text-muted-foreground mt-1">{significantPattern.description}</p>
        </div>
        
        <div className="h-[180px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={patternData}>
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip 
                formatter={(value: number) => [`$${value.toLocaleString()}`, 'Price']}
                labelFormatter={(label) => `Day ${label}`}
              />
              <Line 
                type="monotone" 
                dataKey="price" 
                strokeWidth={2} 
                stroke={significantPattern.type === 'bullish' ? '#22c55e' : 
                         significantPattern.type === 'bearish' ? '#ef4444' : 
                         '#64748b'} 
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
