
import { useState } from "react";
import { cn } from "@/lib/utils";
import { CheckCircle, Circle } from "lucide-react";
import { 
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@/components/ui/tooltip";

export type TechnicalIndicator = 
  | "sma20" 
  | "sma50" 
  | "sma200" 
  | "rsi" 
  | "bollinger" 
  | "macd"
  | "volume";

interface TechnicalIndicatorSelectorProps {
  activeIndicators: TechnicalIndicator[];
  onToggle: (indicator: TechnicalIndicator) => void;
}

const indicatorInfo = {
  sma20: {
    name: "SMA 20",
    description: "20-day Simple Moving Average",
    color: "#2563eb" // blue
  },
  sma50: {
    name: "SMA 50",
    description: "50-day Simple Moving Average",
    color: "#7c3aed" // purple
  },
  sma200: {
    name: "SMA 200",
    description: "200-day Simple Moving Average",
    color: "#be185d" // pink
  },
  rsi: {
    name: "RS<PERSON>",
    description: "Relative Strength Index - measures momentum",
    color: "#ea580c" // orange
  },
  bollinger: {
    name: "<PERSON><PERSON><PERSON>",
    description: "Bollinger Bands - volatility indicator",
    color: "#0ea5e9" // sky blue
  },
  macd: {
    name: "MACD",
    description: "Moving Average Convergence Divergence",
    color: "#16a34a" // green
  },
  volume: {
    name: "Volume",
    description: "Trading volume",
    color: "#64748b" // slate
  }
};

export default function TechnicalIndicatorSelector({
  activeIndicators,
  onToggle
}: TechnicalIndicatorSelectorProps) {
  return (
    <div className="flex flex-wrap gap-1.5">
      {(Object.entries(indicatorInfo) as [TechnicalIndicator, typeof indicatorInfo.sma20][]).map(([key, info]) => {
        const isActive = activeIndicators.includes(key);
        return (
          <Tooltip key={key}>
            <TooltipTrigger asChild>
              <button
                onClick={() => onToggle(key)}
                className={cn(
                  "px-2 py-1 text-xs rounded-md flex items-center gap-1 transition-colors",
                  isActive 
                    ? "bg-secondary text-secondary-foreground" 
                    : "bg-muted hover:bg-secondary/50"
                )}
                style={{ borderLeft: isActive ? `3px solid ${info.color}` : undefined }}
              >
                {isActive ? (
                  <CheckCircle size={14} />
                ) : (
                  <Circle size={14} />
                )}
                {info.name}
              </button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>{info.description}</p>
            </TooltipContent>
          </Tooltip>
        );
      })}
    </div>
  );
}
