<svg xmlns="http://www.w3.org/2000/svg" width="800" height="500" viewBox="0 0 800 500" fill="none">
  <defs>
    <linearGradient id="compoundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00D395;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00B386;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="marketGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="500" fill="#0F172A"/>
  
  <!-- Title -->
  <text x="400" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Compound Protocol</text>
  <text x="400" y="65" text-anchor="middle" fill="#94A3B8" font-family="Arial, sans-serif" font-size="14">Algorithmic money markets for lending and borrowing</text>
  
  <!-- Central Compound Protocol -->
  <g transform="translate(350, 220)">
    <rect width="120" height="80" rx="12" fill="url(#compoundGradient)" stroke="#00D395" stroke-width="3"/>
    <text x="60" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Compound</text>
    <text x="60" y="50" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">Protocol</text>
    <text x="60" y="65" text-anchor="middle" fill="#E0FDF4" font-family="Arial, sans-serif" font-size="10">Money Markets</text>
  </g>
  
  <!-- Money Markets -->
  <g transform="translate(100, 100)">
    <rect width="80" height="60" rx="8" fill="url(#marketGradient)" stroke="#60A5FA" stroke-width="2"/>
    <text x="40" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">ETH</text>
    <text x="40" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11">Market</text>
    <text x="40" y="52" text-anchor="middle" fill="#DBEAFE" font-family="Arial, sans-serif" font-size="9">cETH</text>
  </g>
  
  <g transform="translate(200, 100)">
    <rect width="80" height="60" rx="8" fill="url(#marketGradient)" stroke="#60A5FA" stroke-width="2"/>
    <text x="40" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">USDC</text>
    <text x="40" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11">Market</text>
    <text x="40" y="52" text-anchor="middle" fill="#DBEAFE" font-family="Arial, sans-serif" font-size="9">cUSDC</text>
  </g>
  
  <g transform="translate(520, 100)">
    <rect width="80" height="60" rx="8" fill="url(#marketGradient)" stroke="#60A5FA" stroke-width="2"/>
    <text x="40" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">DAI</text>
    <text x="40" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11">Market</text>
    <text x="40" y="52" text-anchor="middle" fill="#DBEAFE" font-family="Arial, sans-serif" font-size="9">cDAI</text>
  </g>
  
  <g transform="translate(620, 100)">
    <rect width="80" height="60" rx="8" fill="url(#marketGradient)" stroke="#60A5FA" stroke-width="2"/>
    <text x="40" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">WBTC</text>
    <text x="40" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11">Market</text>
    <text x="40" y="52" text-anchor="middle" fill="#DBEAFE" font-family="Arial, sans-serif" font-size="9">cWBTC</text>
  </g>
  
  <!-- Supplier -->
  <g transform="translate(100, 320)">
    <rect width="100" height="70" rx="8" fill="#10B981" stroke="#34D399" stroke-width="2"/>
    <text x="50" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Supplier</text>
    <text x="50" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11">Earns Interest</text>
    <text x="50" y="55" text-anchor="middle" fill="#D1FAE5" font-family="Arial, sans-serif" font-size="10">Receives cTokens</text>
  </g>
  
  <!-- Borrower -->
  <g transform="translate(600, 320)">
    <rect width="100" height="70" rx="8" fill="#EF4444" stroke="#F87171" stroke-width="2"/>
    <text x="50" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Borrower</text>
    <text x="50" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11">Pays Interest</text>
    <text x="50" y="55" text-anchor="middle" fill="#FEE2E2" font-family="Arial, sans-serif" font-size="10">Uses cTokens as collateral</text>
  </g>
  
  <!-- cTokens explanation -->
  <g transform="translate(300, 320)">
    <rect width="200" height="80" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">cTokens</text>
    <text x="100" y="40" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">ERC-20 tokens representing</text>
    <text x="100" y="55" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">your claim on underlying assets</text>
    <text x="100" y="70" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="10">Exchange rate increases over time</text>
  </g>
  
  <!-- Flow arrows -->
  <path d="M 200 355 Q 275 300 350 260" stroke="#10B981" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <text x="250" y="320" fill="#10B981" font-family="Arial, sans-serif" font-size="10">Supply Assets</text>
  
  <path d="M 600 355 Q 525 300 470 260" stroke="#EF4444" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <text x="550" y="320" fill="#EF4444" font-family="Arial, sans-serif" font-size="10">Borrow Assets</text>
  
  <!-- Arrows to markets -->
  <path d="M 350 240 Q 275 200 200 160" stroke="#00D395" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 380 240 Q 320 200 260 160" stroke="#00D395" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 420 240 Q 480 200 540 160" stroke="#00D395" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 450 240 Q 525 200 600 160" stroke="#00D395" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Interest Rate Model -->
  <g transform="translate(50, 420)">
    <rect width="180" height="60" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="90" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Interest Rate Model</text>
    <text x="90" y="35" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Algorithmic rates based on</text>
    <text x="90" y="50" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">utilization of each market</text>
  </g>
  
  <!-- Collateral Factor -->
  <g transform="translate(250, 420)">
    <rect width="180" height="60" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="90" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Collateral Factor</text>
    <text x="90" y="35" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Maximum borrowing power</text>
    <text x="90" y="50" text-anchor="middle" fill="#F59E0B" font-family="Arial, sans-serif" font-size="10">ETH: 82.5%, USDC: 87%</text>
  </g>
  
  <!-- COMP Governance -->
  <g transform="translate(450, 420)">
    <rect width="180" height="60" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="90" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">COMP Governance</text>
    <text x="90" y="35" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Token holders vote on</text>
    <text x="90" y="50" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">protocol parameters</text>
  </g>
  
  <!-- Liquidation -->
  <g transform="translate(650, 420)">
    <rect width="120" height="60" rx="8" fill="#1E293B" stroke="#334155" stroke-width="2"/>
    <text x="60" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Liquidation</text>
    <text x="60" y="35" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">If account becomes</text>
    <text x="60" y="50" text-anchor="middle" fill="#EF4444" font-family="Arial, sans-serif" font-size="10">undercollateralized</text>
  </g>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#06B6D4"/>
    </marker>
  </defs>
</svg>
