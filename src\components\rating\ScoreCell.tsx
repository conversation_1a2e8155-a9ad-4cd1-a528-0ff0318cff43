
import { cn } from "@/lib/utils";

interface ScoreCellProps {
  score: number;
}

export function ScoreCell({ score }: ScoreCellProps) {
  let colorClass = "bg-gray-100";
  
  if (score >= 8) colorClass = "bg-green-100 text-green-800";
  else if (score >= 6) colorClass = "bg-blue-100 text-blue-800";
  else if (score >= 4) colorClass = "bg-yellow-100 text-yellow-800";
  else colorClass = "bg-red-100 text-red-800";
  
  return (
    <span className={cn("px-2 py-0.5 rounded font-medium", colorClass)}>
      {score.toFixed(1)}
    </span>
  );
}
