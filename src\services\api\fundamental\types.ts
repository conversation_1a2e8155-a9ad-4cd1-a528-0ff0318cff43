
// Fundamental Analysis Types

export interface CoinAnalysis {
  id: string;
  name: string;
  symbol: string;
  fundamentalScore: number;
  technicalScore: number;
  sentimentScore: number;
  overallScore: number;
  analysis: {
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    threats: string[];
  };
  metrics: {
    marketCap: number;
    volume24h: number;
    priceChange24h: number;
    priceChange7d: number;
    priceChange30d: number;
  };
}

export interface TokenomicsAnalysis {
  totalSupply: number;
  circulatingSupply: number;
  maxSupply?: number;
  inflationRate: number;
  distribution: {
    public: number;
    team: number;
    advisors: number;
    treasury: number;
    ecosystem: number;
  };
  vestingSchedule: {
    category: string;
    amount: number;
    unlockDate: string;
  }[];
  score: number;
}

export interface DeveloperMetrics {
  githubScore: number;
  commitActivity: {
    date: string;
    commits: number;
  }[];
  contributors: number;
  repositories: number;
  lastUpdate: string;
  codeQuality: number;
}

export interface OnChainMetrics {
  activeAddresses: number;
  transactionCount: number;
  networkValue: number;
  hashRate?: number;
  stakingRatio?: number;
  validatorCount?: number;
  averageTransactionFee: number;
  blockTime: number;
}

export interface FundamentalScore {
  overall: number;
  technology: number;
  adoption: number;
  community: number;
  tokenomics: number;
  team: number;
  roadmap: number;
}
