
import { getPriceForecastData } from '../priceHistory/forecastService';
import { fetchCoinHistoricalData } from '../priceHistory/historyClient';

export interface CoinAnalysisData {
  technical: {
    indicators: any[];
    signals: {
      trend: 'bullish' | 'bearish' | 'neutral';
      momentum: 'strong' | 'moderate' | 'weak';
      volatility: 'high' | 'medium' | 'low';
    };
    support: number;
    resistance: number;
  };
  fundamental: {
    score: number;
    strengths: string[];
    weaknesses: string[];
    outlook: string;
  };
  prediction: {
    shortTerm: { target: number; confidence: number };
    mediumTerm: { target: number; confidence: number };
    longTerm: { target: number; confidence: number };
  };
  risk: {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    score: number;
  };
}

export const generateCoinAnalysis = async (coinData: any): Promise<CoinAnalysisData> => {
  try {
    const currentPrice = coinData.current_price || coinData.price || 0;
    const priceChange24h = coinData.price_change_percentage_24h || coinData.change24h || 0;
    const marketCap = coinData.market_cap || coinData.marketCap || 0;
    const volume = coinData.total_volume || coinData.volume || 0;

    // Technical Analysis
    const technicalSignals = {
      trend: priceChange24h > 2 ? 'bullish' : priceChange24h < -2 ? 'bearish' : 'neutral',
      momentum: Math.abs(priceChange24h) > 5 ? 'strong' : Math.abs(priceChange24h) > 2 ? 'moderate' : 'weak',
      volatility: Math.abs(priceChange24h) > 10 ? 'high' : Math.abs(priceChange24h) > 5 ? 'medium' : 'low'
    } as const;

    // Fundamental Analysis
    const fundamentalScore = Math.min(10, Math.max(1, 
      5 + (priceChange24h * 0.1) + (volume / marketCap > 0.1 ? 2 : 0)
    ));

    // Price Predictions
    const shortTermTarget = currentPrice * (1 + (priceChange24h / 100) * 0.5);
    const mediumTermTarget = currentPrice * (1 + (priceChange24h / 100) * 1.2);
    const longTermTarget = currentPrice * (1 + (priceChange24h / 100) * 2.0);

    // Risk Assessment
    const riskScore = Math.min(10, Math.max(1, 
      5 + (Math.abs(priceChange24h) * 0.2) - (volume / marketCap > 0.05 ? 1 : 0)
    ));

    return {
      technical: {
        indicators: [
          { name: 'RSI', value: 50 + (priceChange24h * 2), signal: 'neutral' },
          { name: 'MACD', value: priceChange24h * 0.1, signal: technicalSignals.trend },
          { name: 'Moving Average', value: currentPrice * 0.98, signal: 'support' }
        ],
        signals: technicalSignals,
        support: currentPrice * 0.92,
        resistance: currentPrice * 1.08
      },
      fundamental: {
        score: fundamentalScore,
        strengths: [
          ...(volume / marketCap > 0.1 ? ['High trading activity'] : []),
          ...(priceChange24h > 0 ? ['Positive momentum'] : []),
          'Active development community'
        ],
        weaknesses: [
          ...(volume / marketCap < 0.05 ? ['Low liquidity'] : []),
          ...(priceChange24h < -5 ? ['Recent price decline'] : [])
        ],
        outlook: priceChange24h > 5 ? 'Very positive with strong upward momentum' :
                priceChange24h > 0 ? 'Positive with steady growth potential' :
                priceChange24h > -5 ? 'Neutral with mixed signals' :
                'Cautious due to recent volatility'
      },
      prediction: {
        shortTerm: { target: shortTermTarget, confidence: 75 },
        mediumTerm: { target: mediumTermTarget, confidence: 65 },
        longTerm: { target: longTermTarget, confidence: 55 }
      },
      risk: {
        level: riskScore > 7 ? 'high' : riskScore > 4 ? 'medium' : 'low',
        factors: [
          ...(Math.abs(priceChange24h) > 10 ? ['High volatility'] : []),
          ...(volume / marketCap < 0.05 ? ['Limited liquidity'] : []),
          'Market correlation risk',
          'Regulatory uncertainty'
        ],
        score: riskScore
      }
    };
  } catch (error) {
    console.error('Error generating coin analysis:', error);
    // Return default analysis
    return {
      technical: {
        indicators: [],
        signals: { trend: 'neutral', momentum: 'weak', volatility: 'medium' },
        support: 0,
        resistance: 0
      },
      fundamental: {
        score: 5,
        strengths: [],
        weaknesses: [],
        outlook: 'Analysis unavailable'
      },
      prediction: {
        shortTerm: { target: 0, confidence: 0 },
        mediumTerm: { target: 0, confidence: 0 },
        longTerm: { target: 0, confidence: 0 }
      },
      risk: {
        level: 'medium',
        factors: [],
        score: 5
      }
    };
  }
};
