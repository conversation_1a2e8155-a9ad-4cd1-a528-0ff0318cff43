
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import HeaderBar from "@/components/HeaderBar";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { useOnChainAnalytics } from "@/hooks/useOnChainAnalytics";
import NetworkActivitySection from "@/components/onchain/NetworkActivitySection";
import WhaleMovementsSection from "@/components/onchain/WhaleMovementsSection";
import NetworkHealthSection from "@/components/onchain/NetworkHealthSection";

export default function OnChainAnalytics() {
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();
  const { networkData, whaleData, healthData, refreshData, loading, attribution } = useOnChainAnalytics();
  
  const handleRefresh = () => {
    setRefreshing(true);
    refreshData();
    
    // Simulate minimum loading time for better UX
    setTimeout(() => {
      setRefreshing(false);
      toast({
        title: "Data refreshed",
        description: "All on-chain metrics have been updated.",
      });
    }, 1500);
  };
  
  // Combine loading states for UI
  const displayLoading = loading || refreshing;
  
  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar 
        title="On-Chain Analytics" 
        description="Real-time blockchain metrics and network analysis"
        onRefresh={handleRefresh}
        isLoading={displayLoading}
      />
      
      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          <Card className="animate-scale-in">
            <CardHeader className="pb-2">
              <CardTitle>Blockchain Network Overview</CardTitle>
              <CardDescription>
                Analyze key on-chain metrics to understand network usage and adoption
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="network" className="w-full">
                <TabsList className="grid w-full md:w-auto grid-cols-3 h-auto">
                  <TabsTrigger value="network">Network Activity</TabsTrigger>
                  <TabsTrigger value="whales">Whale Movements</TabsTrigger>
                  <TabsTrigger value="health">Network Health</TabsTrigger>
                </TabsList>
                
                <TabsContent value="network" className="mt-6">
                  <NetworkActivitySection data={networkData} isLoading={displayLoading} />
                </TabsContent>
                
                <TabsContent value="whales" className="mt-6">
                  <WhaleMovementsSection data={whaleData} isLoading={displayLoading} />
                </TabsContent>
                
                <TabsContent value="health" className="mt-6">
                  <NetworkHealthSection data={healthData} isLoading={displayLoading} />
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter className="text-xs text-muted-foreground">
              {attribution}
            </CardFooter>
          </Card>
        </div>
      </main>
    </div>
  );
}
