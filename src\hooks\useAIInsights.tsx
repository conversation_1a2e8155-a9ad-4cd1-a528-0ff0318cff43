
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  generatePatternRecognitionData,
  generateAnomalyAlerts, 
  generateSentimentAnalysis,
  generatePriceForecasts,
  generateRiskAssessment
} from '@/services/api/aiInsightsService';
import { ChartPattern, AnomalyAlert, SentimentSource, PredictionModel, RiskAssessment } from '@/types/aiInsights';

export function useAIInsights(coinId: string = 'bitcoin') {
  const [selectedAsset, setSelectedAsset] = useState(coinId);
  const [timeframe, setTimeframe] = useState(30); // days

  // Pattern Recognition
  const {
    data: patternData,
    isLoading: isLoadingPatterns,
    refetch: refetchPatterns
  } = useQuery({
    queryKey: ['ai-patterns', selectedAsset, timeframe],
    queryFn: () => generatePatternRecognitionData(selectedAsset, timeframe),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Anomaly Detection
  const {
    data: anomalyData,
    isLoading: isLoadingAnomalies,
    refetch: refetchAnomalies
  } = useQuery({
    queryKey: ['ai-anomalies'],
    queryFn: () => generateAnomalyAlerts(5),
    staleTime: 2 * 60 * 1000, // 2 minutes - anomalies should update more frequently
  });

  // Sentiment Analysis
  const {
    data: sentimentData,
    isLoading: isLoadingSentiment,
    refetch: refetchSentiment
  } = useQuery({
    queryKey: ['ai-sentiment', selectedAsset],
    queryFn: () => generateSentimentAnalysis(selectedAsset),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Price Predictions
  const {
    data: predictionData,
    isLoading: isLoadingPredictions,
    refetch: refetchPredictions
  } = useQuery({
    queryKey: ['ai-predictions', selectedAsset, timeframe],
    queryFn: () => generatePriceForecasts(selectedAsset, timeframe),
    staleTime: 10 * 60 * 1000, // 10 minutes - predictions can be cached longer
  });

  // Risk Assessment
  const {
    data: riskData,
    isLoading: isLoadingRisk,
    refetch: refetchRisk
  } = useQuery({
    queryKey: ['ai-risk', selectedAsset],
    queryFn: () => generateRiskAssessment([selectedAsset]),
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  const isLoading = 
    isLoadingPatterns || 
    isLoadingAnomalies || 
    isLoadingSentiment || 
    isLoadingPredictions || 
    isLoadingRisk;

  const refreshAll = () => {
    refetchPatterns();
    refetchAnomalies();
    refetchSentiment();
    refetchPredictions();
    refetchRisk();
  };

  return {
    selectedAsset,
    setSelectedAsset,
    timeframe,
    setTimeframe,
    patternData: patternData || [],
    anomalyData: anomalyData || [],
    sentimentData: sentimentData || [],
    predictionData: predictionData,
    riskData: riskData || [],
    isLoading,
    refreshAll
  };
}
