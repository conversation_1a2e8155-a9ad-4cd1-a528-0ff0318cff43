
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Database, RefreshCw, Trash2, Clock, HardDrive } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

export function CacheManagementPanel() {
  const queryClient = useQueryClient();

  // Cache status
  const { data: cacheStatus, isLoading } = useQuery({
    queryKey: ['cacheStatus'],
    queryFn: async () => {
      const { data } = await supabase
        .from('cache_status')
        .select('*')
        .order('last_updated', { ascending: false });
      return data || [];
    },
    refetchInterval: 30000,
  });

  // Cache statistics
  const cacheStats = React.useMemo(() => {
    if (!cacheStatus) return null;
    
    const totalSize = cacheStatus.reduce((sum, cache) => sum + (cache.data_size_bytes || 0), 0);
    const totalHits = cacheStatus.reduce((sum, cache) => sum + cache.hit_count, 0);
    const totalMisses = cacheStatus.reduce((sum, cache) => sum + cache.miss_count, 0);
    const hitRate = totalHits + totalMisses > 0 ? (totalHits / (totalHits + totalMisses)) * 100 : 0;
    
    return {
      totalEntries: cacheStatus.length,
      totalSize: totalSize,
      hitRate: hitRate,
      totalRequests: totalHits + totalMisses
    };
  }, [cacheStatus]);

  // Clear specific cache entry
  const clearCacheMutation = useMutation({
    mutationFn: async (cacheKey: string) => {
      const { error } = await supabase
        .from('cache_status')
        .delete()
        .eq('cache_key', cacheKey);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cacheStatus'] });
      toast({
        title: 'Cache cleared',
        description: 'Cache entry has been successfully cleared.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to clear cache entry.',
        variant: 'destructive',
      });
    },
  });

  // Clear all cache
  const clearAllCacheMutation = useMutation({
    mutationFn: async () => {
      const { error } = await supabase
        .from('cache_status')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cacheStatus'] });
      toast({
        title: 'All cache cleared',
        description: 'All cache entries have been successfully cleared.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to clear all cache entries.',
        variant: 'destructive',
      });
    },
  });

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  const getCacheStatusBadge = (cache: any) => {
    if (isExpired(cache.expires_at)) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Cache Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Entries</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{cacheStats?.totalEntries || 0}</div>
            <p className="text-xs text-muted-foreground">Active cache entries</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Size</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatBytes(cacheStats?.totalSize || 0)}
            </div>
            <p className="text-xs text-muted-foreground">Cache storage used</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hit Rate</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cacheStats?.hitRate?.toFixed(1) || 0}%
            </div>
            <p className="text-xs text-muted-foreground">Cache efficiency</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{cacheStats?.totalRequests || 0}</div>
            <p className="text-xs text-muted-foreground">Cache lookups</p>
          </CardContent>
        </Card>
      </div>

      {/* Cache Management Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Cache Management
            </CardTitle>
            <div className="flex gap-2">
              <Button 
                onClick={() => queryClient.invalidateQueries({ queryKey: ['cacheStatus'] })}
                variant="outline" 
                size="sm"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              <Button 
                onClick={() => clearAllCacheMutation.mutate()}
                variant="destructive" 
                size="sm"
                disabled={clearAllCacheMutation.isPending}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Clear All
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Cache Key</TableHead>
                <TableHead>Provider</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Hit Count</TableHead>
                <TableHead>Miss Count</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Expires At</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cacheStatus?.map((cache) => (
                <TableRow key={cache.id}>
                  <TableCell className="font-mono text-xs max-w-xs truncate">
                    {cache.cache_key}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{cache.provider}</Badge>
                  </TableCell>
                  <TableCell>{getCacheStatusBadge(cache)}</TableCell>
                  <TableCell>{formatBytes(cache.data_size_bytes || 0)}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">{cache.hit_count}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{cache.miss_count}</Badge>
                  </TableCell>
                  <TableCell className="text-xs">
                    {new Date(cache.last_updated).toLocaleString()}
                  </TableCell>
                  <TableCell className="text-xs">
                    {new Date(cache.expires_at).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <Button
                      onClick={() => clearCacheMutation.mutate(cache.cache_key)}
                      variant="ghost"
                      size="sm"
                      disabled={clearCacheMutation.isPending}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
