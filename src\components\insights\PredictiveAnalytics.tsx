
import { PredictionModel } from "@/types/aiInsights";
import { <PERSON>, <PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { AlertCircle, Lightbulb } from "lucide-react";
import { calculatePotentialReturn } from "./predictive/formatters";
import PriceChart, { EnhancedDataPoint } from "./predictive/PriceChart";
import MetricsDisplay from "./predictive/MetricsDisplay";
import ModelInfoPopover from "./predictive/ModelInfoPopover";
import PredictionLoadingState from "./predictive/PredictionLoadingState";
import PredictionEmptyState from "./predictive/PredictionEmptyState";

interface PredictiveAnalyticsProps {
  prediction: PredictionModel | null | undefined;
  isLoading: boolean;
}

export default function PredictiveAnalytics({ prediction, isLoading }: PredictiveAnalyticsProps) {
  if (isLoading) {
    return <PredictionLoadingState />;
  }

  if (!prediction) {
    return <PredictionEmptyState />;
  }

  // Extract a subset of historical prices to make the chart clearer
  const recentHistoricalPrices = prediction.historicalPrices.slice(-14);
  
  // Calculate the date that separates historical from forecast data
  const currentDate = prediction.forecast[0]?.date;
  
  // Combine data for the chart
  const chartData = [
    ...recentHistoricalPrices,
    ...prediction.forecast
  ];
  
  // Cast our chartData to the enhanced type and add upper and lower bounds
  const enhancedChartData = chartData as EnhancedDataPoint[];
  
  prediction.forecast.forEach((item, i) => {
    if (prediction.upperBound[i] && prediction.lowerBound[i]) {
      enhancedChartData.forEach(dataPoint => {
        if (dataPoint.date === item.date) {
          dataPoint.upperBound = prediction.upperBound[i].price;
          dataPoint.lowerBound = prediction.lowerBound[i].price;
        }
      });
    }
  });
  
  // Calculate the potential return based on the last price and the last forecast
  const lastHistoricalPrice = recentHistoricalPrices[recentHistoricalPrices.length - 1]?.price;
  const lastForecastPrice = prediction.forecast[prediction.forecast.length - 1]?.price;
  const potentialReturn = lastHistoricalPrice && lastForecastPrice 
    ? calculatePotentialReturn(lastHistoricalPrice, lastForecastPrice)
    : 0;
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Price Prediction
            </CardTitle>
            <CardDescription>ML-generated price forecast with confidence intervals</CardDescription>
          </div>
          <ModelInfoPopover modelDetails={prediction.modelDetails} />
        </div>
      </CardHeader>
      <CardContent>
        <PriceChart 
          enhancedChartData={enhancedChartData}
          currentDate={currentDate}
        />
        
        <MetricsDisplay 
          prediction={prediction}
          potentialReturn={potentialReturn}
        />
        
        <div className="mt-5 text-sm text-muted-foreground flex items-center">
          <AlertCircle className="h-4 w-4 mr-2" />
          This is an AI-generated forecast and should not be used as financial advice.
        </div>
      </CardContent>
    </Card>
  );
}
