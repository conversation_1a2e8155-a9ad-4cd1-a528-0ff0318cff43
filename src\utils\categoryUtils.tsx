
import { ReactNode } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, Database } from "lucide-react";

export const getCategoryIcon = (category: string): ReactNode => {
  switch (category) {
    case 'technical':
      return <LineChart size={20} className="text-blue-500" />;
    case 'onChain':
      return <Database size={20} className="text-purple-500" />;
    case 'social':
      return <TrendingUp size={20} className="text-orange-500" />;
    case 'fundamental':
      return <BarChart size={20} className="text-green-500" />;
    default:
      return <LineChart size={20} />;
  }
};

export const formatCategory = (category: string): string => {
  switch (category) {
    case 'onChain':
      return 'On-Chain';
    default:
      return category.charAt(0).toUpperCase() + category.slice(1);
  }
};
