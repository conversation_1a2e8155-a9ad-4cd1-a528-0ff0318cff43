<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400" fill="none">
  <defs>
    <linearGradient id="blockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chainGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="#0F172A"/>
  
  <!-- Grid pattern -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#1E293B" stroke-width="1" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="800" height="400" fill="url(#grid)"/>
  
  <!-- Block 1 -->
  <g transform="translate(50, 150)">
    <rect width="120" height="100" rx="8" fill="url(#blockGradient)" stroke="#60A5FA" stroke-width="2"/>
    <text x="60" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Block 1</text>
    <text x="60" y="50" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Hash: 0x1a2b</text>
    <text x="60" y="65" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Prev: 0x0000</text>
    <text x="60" y="80" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Nonce: 12345</text>
  </g>
  
  <!-- Chain link 1 -->
  <path d="M 170 200 Q 200 180 230 200" stroke="url(#chainGradient)" stroke-width="4" fill="none"/>
  <circle cx="200" cy="190" r="6" fill="#10B981"/>
  
  <!-- Block 2 -->
  <g transform="translate(230, 150)">
    <rect width="120" height="100" rx="8" fill="url(#blockGradient)" stroke="#60A5FA" stroke-width="2"/>
    <text x="60" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Block 2</text>
    <text x="60" y="50" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Hash: 0x3c4d</text>
    <text x="60" y="65" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Prev: 0x1a2b</text>
    <text x="60" y="80" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Nonce: 67890</text>
  </g>
  
  <!-- Chain link 2 -->
  <path d="M 350 200 Q 380 180 410 200" stroke="url(#chainGradient)" stroke-width="4" fill="none"/>
  <circle cx="380" cy="190" r="6" fill="#10B981"/>
  
  <!-- Block 3 -->
  <g transform="translate(410, 150)">
    <rect width="120" height="100" rx="8" fill="url(#blockGradient)" stroke="#60A5FA" stroke-width="2"/>
    <text x="60" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Block 3</text>
    <text x="60" y="50" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Hash: 0x5e6f</text>
    <text x="60" y="65" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Prev: 0x3c4d</text>
    <text x="60" y="80" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Nonce: 24680</text>
  </g>
  
  <!-- Chain link 3 -->
  <path d="M 530 200 Q 560 180 590 200" stroke="url(#chainGradient)" stroke-width="4" fill="none"/>
  <circle cx="560" cy="190" r="6" fill="#10B981"/>
  
  <!-- Block 4 -->
  <g transform="translate(590, 150)">
    <rect width="120" height="100" rx="8" fill="url(#blockGradient)" stroke="#60A5FA" stroke-width="2"/>
    <text x="60" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Block 4</text>
    <text x="60" y="50" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Hash: 0x7g8h</text>
    <text x="60" y="65" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Prev: 0x5e6f</text>
    <text x="60" y="80" text-anchor="middle" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="10">Nonce: 13579</text>
  </g>
  
  <!-- Title -->
  <text x="400" y="50" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Blockchain Structure</text>
  <text x="400" y="75" text-anchor="middle" fill="#94A3B8" font-family="Arial, sans-serif" font-size="14">Each block contains a hash of the previous block, creating an immutable chain</text>
  
  <!-- Security indicators -->
  <g transform="translate(50, 300)">
    <circle cx="15" cy="15" r="8" fill="#10B981"/>
    <text x="35" y="20" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="12">Cryptographically Secured</text>
  </g>
  
  <g transform="translate(250, 300)">
    <circle cx="15" cy="15" r="8" fill="#F59E0B"/>
    <text x="35" y="20" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="12">Decentralized Network</text>
  </g>
  
  <g transform="translate(450, 300)">
    <circle cx="15" cy="15" r="8" fill="#EF4444"/>
    <text x="35" y="20" fill="#E2E8F0" font-family="Arial, sans-serif" font-size="12">Immutable Records</text>
  </g>
</svg>
