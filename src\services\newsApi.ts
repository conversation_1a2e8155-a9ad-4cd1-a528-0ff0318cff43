
import axios from 'axios';
import { AlertRule } from '@/hooks/useNewsSentiment';

// Environment-based configuration
const NEWS_API_KEY = import.meta.env.VITE_NEWS_API_KEY;
const NEWS_API_ENDPOINT = import.meta.env.VITE_NEWS_API_ENDPOINT || 'https://api.example.com/v1';

// Create an axios instance with base configuration
const apiClient = axios.create({
  baseURL: NEWS_API_ENDPOINT,
  headers: {
    'Content-Type': 'application/json',
    ...(NEWS_API_KEY && { 'Authorization': `Bearer ${NEWS_API_KEY}` })
  }
});

// Alert rule management functions
export async function createAlertRule(rule: Omit<AlertRule, 'id'>): Promise<AlertRule> {
  try {
    // Attempt to create with API
    try {
      const response = await apiClient.post('/alerts', rule);
      return response.data;
    } catch (apiError) {
      console.warn('API call failed, using mock implementation', apiError);

      // Fallback mock implementation if API fails
      const mockId = Date.now().toString();
      const mockRule: AlertRule = { ...rule, id: mockId };

      // Store in localStorage as fallback
      const existingRules = JSON.parse(localStorage.getItem('alertRules') || '[]');
      localStorage.setItem('alertRules', JSON.stringify([...existingRules, mockRule]));

      return mockRule;
    }
  } catch (error) {
    console.error('Error creating alert rule:', error);
    throw error;
  }
}

export async function fetchAlertRules(): Promise<AlertRule[]> {
  try {
    // Attempt to fetch from API
    try {
      const response = await apiClient.get('/alerts');
      return response.data;
    } catch (apiError) {
      console.warn('API call failed, using localStorage fallback', apiError);

      // Fallback to localStorage if API fails
      return JSON.parse(localStorage.getItem('alertRules') || '[]');
    }
  } catch (error) {
    console.error('Error fetching alert rules:', error);
    throw error;
  }
}

export async function updateAlertRule(id: string, updates: Partial<AlertRule>): Promise<AlertRule> {
  try {
    // Attempt to update with API
    try {
      const response = await apiClient.patch(`/alerts/${id}`, updates);
      return response.data;
    } catch (apiError) {
      console.warn('API call failed, using localStorage fallback', apiError);

      // Fallback mock implementation if API fails
      const existingRules = JSON.parse(localStorage.getItem('alertRules') || '[]');
      const updatedRules = existingRules.map((rule: AlertRule) =>
        rule.id === id ? { ...rule, ...updates } : rule
      );

      localStorage.setItem('alertRules', JSON.stringify(updatedRules));

      const updatedRule = updatedRules.find((rule: AlertRule) => rule.id === id);
      if (!updatedRule) throw new Error('Alert rule not found');
      return updatedRule;
    }
  } catch (error) {
    console.error('Error updating alert rule:', error);
    throw error;
  }
}

export async function deleteAlertRule(id: string): Promise<void> {
  try {
    // Attempt to delete with API
    try {
      await apiClient.delete(`/alerts/${id}`);
    } catch (apiError) {
      console.warn('API call failed, using localStorage fallback', apiError);

      // Fallback mock implementation if API fails
      const existingRules = JSON.parse(localStorage.getItem('alertRules') || '[]');
      const filteredRules = existingRules.filter((rule: AlertRule) => rule.id !== id);

      localStorage.setItem('alertRules', JSON.stringify(filteredRules));
    }
  } catch (error) {
    console.error('Error deleting alert rule:', error);
    throw error;
  }
}
