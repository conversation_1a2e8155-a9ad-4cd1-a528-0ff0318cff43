-- Migration: Create API Providers Management Tables
-- Description: Creates tables for managing API providers, costs, and cache entries
-- Version: 1.0.0
-- Date: 2024-12-19

-- =============================================================================
-- API PROVIDERS TABLE
-- =============================================================================

-- Create API providers table
CREATE TABLE IF NOT EXISTS api_providers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL UNIQUE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('market', 'onchain', 'defi', 'ai')),
  priority INTEGER NOT NULL CHECK (priority >= 1 AND priority <= 100),
  rate_limit_per_minute INTEGER CHECK (rate_limit_per_minute >= 0),
  monthly_quota INTEGER CHECK (monthly_quota >= 0),
  cost_per_request DECIMAL(10,6) CHECK (cost_per_request >= 0),
  cost_per_token DECIMAL(10,8) CHECK (cost_per_token >= 0),
  is_active BOOLEAN DEFAULT true,
  config JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_api_providers_type ON api_providers(type);
CREATE INDEX IF NOT EXISTS idx_api_providers_priority ON api_providers(type, priority);
CREATE INDEX IF NOT EXISTS idx_api_providers_active ON api_providers(is_active);

-- =============================================================================
-- API COSTS TABLE
-- =============================================================================

-- Create API costs tracking table
CREATE TABLE IF NOT EXISTS api_costs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID REFERENCES api_providers(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  requests_count INTEGER DEFAULT 0 CHECK (requests_count >= 0),
  tokens_used INTEGER DEFAULT 0 CHECK (tokens_used >= 0),
  estimated_cost DECIMAL(10,4) DEFAULT 0 CHECK (estimated_cost >= 0),
  actual_cost DECIMAL(10,4) CHECK (actual_cost >= 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(provider_id, date)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_api_costs_provider_date ON api_costs(provider_id, date);
CREATE INDEX IF NOT EXISTS idx_api_costs_date ON api_costs(date);

-- =============================================================================
-- CACHE ENTRIES TABLE
-- =============================================================================

-- Create enhanced cache management table
CREATE TABLE IF NOT EXISTS cache_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cache_key VARCHAR(255) NOT NULL UNIQUE,
  provider VARCHAR(50) NOT NULL,
  data_type VARCHAR(50) NOT NULL,
  data JSONB NOT NULL,
  ttl_seconds INTEGER NOT NULL CHECK (ttl_seconds > 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  hit_count INTEGER DEFAULT 0 CHECK (hit_count >= 0),
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_cache_entries_key ON cache_entries(cache_key);
CREATE INDEX IF NOT EXISTS idx_cache_entries_expires ON cache_entries(expires_at);
CREATE INDEX IF NOT EXISTS idx_cache_entries_provider ON cache_entries(provider);
CREATE INDEX IF NOT EXISTS idx_cache_entries_type ON cache_entries(data_type);

-- =============================================================================
-- AI PROMPT TEMPLATES TABLE
-- =============================================================================

-- Create AI prompt templates table
CREATE TABLE IF NOT EXISTS ai_prompt_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id VARCHAR(50) NOT NULL UNIQUE,
  type VARCHAR(20) NOT NULL,
  template TEXT NOT NULL,
  max_tokens INTEGER NOT NULL CHECK (max_tokens > 0),
  temperature DECIMAL(3,2) NOT NULL CHECK (temperature >= 0 AND temperature <= 2),
  cache_ttl INTEGER NOT NULL CHECK (cache_ttl >= 0),
  cost_tier VARCHAR(10) NOT NULL CHECK (cost_tier IN ('low', 'medium', 'high')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_templates_type ON ai_prompt_templates(type);
CREATE INDEX IF NOT EXISTS idx_ai_templates_tier ON ai_prompt_templates(cost_tier);

-- =============================================================================
-- FUNCTIONS AND TRIGGERS
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_api_providers_updated_at 
    BEFORE UPDATE ON api_providers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_prompt_templates_updated_at 
    BEFORE UPDATE ON ai_prompt_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE api_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_costs ENABLE ROW LEVEL SECURITY;
ALTER TABLE cache_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_prompt_templates ENABLE ROW LEVEL SECURITY;

-- Create policies for admin access
-- Note: Adjust these policies based on your authentication system

-- API Providers policies
CREATE POLICY "Admin can manage api_providers" ON api_providers
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

-- API Costs policies
CREATE POLICY "Admin can view api_costs" ON api_costs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

CREATE POLICY "System can insert api_costs" ON api_costs
    FOR INSERT WITH CHECK (true);

-- Cache entries policies
CREATE POLICY "System can manage cache_entries" ON cache_entries
    FOR ALL USING (true);

-- AI prompt templates policies
CREATE POLICY "Admin can manage ai_prompt_templates" ON ai_prompt_templates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

-- =============================================================================
-- INITIAL DATA
-- =============================================================================

-- Insert default API providers
INSERT INTO api_providers (name, type, priority, rate_limit_per_minute, monthly_quota, cost_per_request, is_active, config) VALUES
('CoinGecko', 'market', 1, 30, 10000, 0.001, true, '{"baseUrl": "https://api.coingecko.com/api/v3", "timeout": 15000}'),
('CoinMarketCap', 'market', 2, 333, 10000, 0.002, false, '{"baseUrl": "https://pro-api.coinmarketcap.com/v1", "timeout": 15000}'),
('CryptoCompare', 'market', 3, 100, 100000, 0.0005, false, '{"baseUrl": "https://min-api.cryptocompare.com/data", "timeout": 15000}'),
('Glassnode', 'onchain', 1, 1000, 1000, 0.01, false, '{"baseUrl": "https://api.glassnode.com/v1", "timeout": 30000}'),
('GeckoTerminal', 'onchain', 2, 30, 50000, 0, true, '{"baseUrl": "https://api.geckoterminal.com/api/v2", "timeout": 15000}'),
('DeFi Llama', 'defi', 1, 300, 999999, 0, true, '{"baseUrl": "https://api.llama.fi", "timeout": 10000}'),
('DeepSeek', 'ai', 1, 100, 1000000, null, true, '{"baseUrl": "https://api.deepseek.com", "timeout": 60000}'),
('OpenAI', 'ai', 2, 60, 150000, null, false, '{"baseUrl": "https://api.openai.com/v1", "timeout": 60000}'),
('Claude', 'ai', 3, 50, 200000, null, false, '{"baseUrl": "https://api.anthropic.com/v1", "timeout": 60000}')
ON CONFLICT (name) DO NOTHING;

-- Update AI providers with token costs
UPDATE api_providers SET cost_per_token = 0.00000014 WHERE name = 'DeepSeek';
UPDATE api_providers SET cost_per_token = 0.0000015 WHERE name = 'OpenAI';
UPDATE api_providers SET cost_per_token = 0.000008 WHERE name = 'Claude';

-- Insert default AI prompt templates
INSERT INTO ai_prompt_templates (template_id, type, template, max_tokens, temperature, cache_ttl, cost_tier) VALUES
('quick_analysis', 'analysis', 'Analyze {coinName} ({symbol}): Price: {price} ({change}%) Market Cap: {marketCap}. Provide 2-3 sentence analysis focusing on momentum and key levels.', 150, 0.1, 1800, 'low'),
('market_sentiment', 'sentiment', 'Market sentiment analysis for {timeframe}: Top gainers: {gainers} Top losers: {losers} Market cap: {totalMarketCap} Volume: {volume}. Summarize market sentiment in 2 sentences.', 100, 0.2, 900, 'low'),
('price_prediction', 'prediction', 'Technical analysis for {coinName}: Current: {price} 24h: {change24h}% 7d: {change7d}% Volume: {volume} RSI: {rsi} MA20: {ma20}. Provide price outlook for {timeframe} with confidence level.', 300, 0.3, 3600, 'medium'),
('investment_strategy', 'strategy', 'Investment strategy for {riskProfile} investor: Portfolio: {portfolio} Goals: {goals} Timeframe: {timeframe} Market conditions: {marketConditions}. Provide personalized strategy with 3-5 specific recommendations.', 600, 0.4, 7200, 'high'),
('batch_analysis', 'analysis', 'Analyze these cryptocurrencies: {coinData}. For each coin, provide: 1. Trend direction 2. Key support/resistance 3. Risk level (1-5). Keep each analysis to 1-2 sentences.', 800, 0.2, 1800, 'medium')
ON CONFLICT (template_id) DO NOTHING;

-- =============================================================================
-- CLEANUP FUNCTION
-- =============================================================================

-- Function to clean up expired cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_entries WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- COMMENTS
-- =============================================================================

COMMENT ON TABLE api_providers IS 'Configuration and management of API providers';
COMMENT ON TABLE api_costs IS 'Daily cost tracking for API providers';
COMMENT ON TABLE cache_entries IS 'Enhanced cache management with TTL and hit tracking';
COMMENT ON TABLE ai_prompt_templates IS 'Optimized prompt templates for AI services';

COMMENT ON COLUMN api_providers.priority IS 'Lower numbers = higher priority (1-100)';
COMMENT ON COLUMN api_providers.config IS 'Provider-specific configuration (JSON)';
COMMENT ON COLUMN api_costs.estimated_cost IS 'Estimated cost based on usage and rates';
COMMENT ON COLUMN api_costs.actual_cost IS 'Actual cost from provider billing';
COMMENT ON COLUMN cache_entries.hit_count IS 'Number of times this cache entry was accessed';
COMMENT ON COLUMN ai_prompt_templates.cost_tier IS 'Cost tier for provider selection (low/medium/high)';

-- Migration completed successfully
SELECT 'API Providers management tables created successfully' AS status;
